package utility

import (
	"github.com/gogf/gf/v2/util/grand"
	"math/rand"
	"strings"
	"time"
)

const charset = "0123456789abcdefghijklmnopqrstuvwxyz"

var seededRand *rand.Rand = rand.New(rand.NewSource(time.Now().UnixNano()))

func RandBytes(n int) []byte {
	bytes := make([]byte, n)
	for i := 0; i < n; i++ {
		bytes[i] = charset[seededRand.Intn(len(charset))]
	}
	return bytes
}

func RandString(n int) string {
	return string(RandBytes(n))
}

func RandCode(charLen, digitsLen int) string {
	str := generateString(charLen)
	return str + grand.Digits(digitsLen)
}

func generateString(length int) string {
	// 定义字符集，不包含容易混淆的字符
	const charset = "ABCDEFGHJKLMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz"
	// 使用当前时间作为随机数种子
	rand.Seed(time.Now().UnixNano())
	var result strings.Builder
	for i := 0; i < length; i++ {
		// 随机选择字符集中的一个字符
		index := rand.Intn(len(charset))
		result.WriteByte(charset[index])
	}
	return result.String()
}
