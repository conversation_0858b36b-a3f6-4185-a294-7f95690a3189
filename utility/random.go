package utility

import (
	"math/rand"
)

const (
	letterBytes         = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
	digitBytes          = "0123456789"
	letterAndDigitBytes = letterBytes + digitBytes
)

// 生成一个长度为N的字符串，其中第一位是字母，后面是字母+数字
func GenerateRandomString(n int) string {
	b := make([]byte, n)
	b[0] = letterBytes[rand.Intn(len(letterBytes))]
	for i := 1; i < n; i++ {
		b[i] = letterAndDigitBytes[rand.Intn(len(letterAndDigitBytes))]
	}
	return string(b)
}
