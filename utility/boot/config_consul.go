package boot

import (
	"github.com/gogf/gf/contrib/config/consul/v2"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"halalplus/utility"
)

// init gf从consul的获取配置文件
func init() {
	ctx := gctx.GetInitCtx()
	consulConfig, err := utility.GetConsulConfig()
	if err != nil {
		g.Log().Info(ctx, err)
		return
	}

	configPath, err := utility.GetConsulGoFrameCfgPath()
	if err != nil {
		g.Log().Info(ctx, err)
		return
	}

	adapter, err := consul.New(ctx, consul.Config{
		ConsulConfig: *consulConfig,
		Path:         configPath,
		Watch:        true,
		Logger:       g.Log("consul-config-adapter"),
	})
	if err != nil {
		// 配置获取不到
		g.Log().Fatal(ctx, err)
	}

	g.Log().Info(ctx, "GoFrame Config consul", consulConfig.Address, configPath)
	g.Cfg().SetAdapter(adapter)
}
