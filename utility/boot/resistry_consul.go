package boot

import (
	"github.com/gogf/gf/contrib/rpc/grpcx/v2"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"halalplus/utility"
	consul "halalplus/utility/gf-registry-consul"
)

// init consul注册与发现
func init() {
	ctx := gctx.GetInitCtx()
	consulConfig, err := utility.GetConsulConfig()
	if err != nil {
		g.Log().Info(ctx, "不使用consul注册与发现 "+err.Error())
		return
	}
	registry, err := consul.New(consul.WithAddress(consulConfig.Address))
	if err != nil {
		g.Log().Fatal(ctx, err)
	}
	grpcx.Resolver.Register(registry)
	g.Log().Info(ctx, "consul注册与发现", consulConfig.Address)
}
