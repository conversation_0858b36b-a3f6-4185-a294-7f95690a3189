package batch

import "context"

// CronJobBatch 批量处理数据 支持模板 自定义
// 参数ctx,可以让调用者主动取消或者超时控制 参数是channel，我们从这个channel中读取数据。channel可以在外部被关闭
// 参数size是批处理的大小，从channel中读取一批数据的进行处理
// 参数fn自定义处理函数，我们把从channel中读取的一批数据传递给这个函数，由这个函数来处理这批数据
func CronJobBatch[T any](ctx context.Context, ch <-chan T, size int, fn func(input []T)) {
	var items []T = make([]T, 0, size)
	for {
		select {
		case v, ok := <-ch:
			//当ch在外部被关闭时
			if !ok {
				fn(items)
				return
			}
			// 从channel中获取数据缓存下来
			items = append(items, v)
			if len(items) == size {
				fn(items)
				items = make([]T, 0, size)
			}
			// 调用者 主动取消或者超时取消
		case <-ctx.Done():
			fn(items)
			return
		default:
			if len(items) > 0 {
				fn(items)
				items = make([]T, 0, size)
			} else {
				select {
				case <-ctx.Done():
					fn(items)
					return
				case v, ok := <-ch:
					if !ok {
						return
					}
					items = append(items, v)
				}
			}
		}
	}
}
