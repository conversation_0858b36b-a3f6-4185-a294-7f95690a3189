package utility

import (
	"fmt"
	"net/url"
	"strings"
)

// GetTopLevelDomain 获取一级域名（例如 example.com）
func GetTopLevelDomain(rawURL string) (string, error) {

	// 解析 URL
	parsedURL, err := url.Parse(rawURL)
	if err != nil {
		return "", fmt.<PERSON><PERSON>rf("invalid URL: %v", err)
	}

	// 获取主机名部分（如 pc.test22.vip）
	host := parsedURL.Host

	// 检查是否包含端口号并去除
	if strings.Contains(host, ":") {
		host = strings.Split(host, ":")[0]
	}

	// 分割域名部分
	parts := strings.Split(host, ".")

	// 至少需要两部分（顶级域名 + 二级域名）
	if len(parts) < 2 {
		return "", fmt.Errorf("invalid domain")
	}

	// 获取最后两部分组成一级域名
	topLevelDomain := strings.Join(parts[len(parts)-2:], ".")
	return topLevelDomain, nil
}

func IsTopLevelDomain(host string) bool {
	if strings.Contains(host, "http") {
		return false
	}
	// 分割域名部分
	parts := strings.Split(host, ".")
	return len(parts) == 2
}
