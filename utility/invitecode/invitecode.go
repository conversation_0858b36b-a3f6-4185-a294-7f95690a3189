package invitecode

import (
	"math/rand"
	"strings"
	"sync"
	"time"
)

// 根据整型用户id生成六位长度邀请码
// 根据邀请码生成整型用户id
const (
	BASE = "zx9wyltn6e8s2dbqf7cp5uar4hvik3mj"
	//BASE    = "ZX9WYLTN6E8S2DBQF7CP5UAR4HVIK3MJ"
	DECIMAL = 32
	PAD     = "g"
	LEN     = 6
)

func Encode(uid uint64) string {
	id := uid
	mod := uint64(0)
	res := ""
	for id != 0 {
		mod = id % DECIMAL
		id = id / DECIMAL
		res += string(BASE[mod])
	}
	resLen := len(res)
	if resLen < LEN {
		res += PAD
		for i := 0; i < LEN-resLen-1; i++ {
			res += string(BASE[(int(uid)+i)%DECIMAL])
		}
	}
	return res
}

func Decode(code string) uint64 {
	res := uint64(0)
	lenCode := len(code)
	baseArr := []byte(BASE)       // 字符串进制转换为byte数组
	baseRev := make(map[byte]int) // 进制数据键值转换为map
	for k, v := range baseArr {
		baseRev[v] = k
	}
	// 查找补位字符的位置
	isPad := strings.Index(code, PAD)
	if isPad != -1 {
		lenCode = isPad
	}
	r := 0
	for i := 0; i < lenCode; i++ {
		// 补充字符直接跳过
		if string(code[i]) == PAD {
			continue
		}
		index, ok := baseRev[code[i]]
		if !ok {
			return 0
		}
		b := uint64(1)
		for j := 0; j < r; j++ {
			b *= DECIMAL
		}
		res += uint64(index) * b
		r++
	}
	return res
}

const letterBytes = "ABCDEFGHJKLMNPQRSTUVWXYZ23456789"

var randInvite = rand.New(rand.NewSource(time.Now().UnixNano()))
var randMutex sync.Mutex
var seed int64 = ************

func GenerateInviteCode(userID int64) string {
	randMutex.Lock()
	defer randMutex.Unlock()
	// 使用 user ID 作为随机数种子的一部分，增加随机性
	randInvite.Seed(seed + userID)

	b := make([]byte, 8)
	for i := range b {
		b[i] = letterBytes[randInvite.Intn(len(letterBytes))]
	}
	return string(b)
}
