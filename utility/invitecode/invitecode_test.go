package invitecode

import (
	"fmt"
	"sync"
	"testing"
)

func TestEncode(t *testing.T) {
	for i := 1; i < 100; i++ {
		t.Log(Encode(uint64(i)))
	}
}

func TestDecode(t *testing.T) {
	var maxSize int = 1000000100
	var wg sync.WaitGroup
	var result1, result2 string
	wg.Add(2)
	go func() {
		result1 = Encode(uint64(1))
		wg.Done()
	}()
	go func() {
		result2 = Encode(uint64(36))
		wg.Done()
	}()
	wg.Wait()
	fmt.Printf("result1: %s, result2:%s", result1, result2)

	for i := 1; i < maxSize; i++ {
		result := Encode(uint64(i))
		t.Log(i, result)
		num := Decode(result)
		if num != uint64(i) {
			t.Fatal("num != i")
		}
	}
}

func TestCheckRepeat(t *testing.T) {
	var maxSize int64 = 100000
	mapUid := make(map[string]struct{}, maxSize)
	for i := 1; i < 500000; i++ {
		result := Encode(uint64(i))
		if _, ok := mapUid[result]; ok {
			t.Fatal("Repeat encode "+result+
				" i = ", i)
		} else {
			mapUid[result] = struct{}{}
		}
	}
}
