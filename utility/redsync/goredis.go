package redsync

import (
	"context"
	"github.com/gogf/gf/v2/database/gredis"
	"strings"
	"time"

	redsyncredis "github.com/go-redsync/redsync/v4/redis"
)

type pool struct {
	delegate *gredis.Redis
}

func (p *pool) Get(ctx context.Context) (redsyncredis.Conn, error) {
	if ctx == nil {
		ctx = context.Background()
	}
	return &conn{p.delegate, ctx}, nil
}

// NewPool returns a Goredis-based pool implementation.
func NewPool(delegate *gredis.Redis) redsyncredis.Pool {
	return &pool{delegate}
}

type conn struct {
	delegate *gredis.Redis
	ctx      context.Context
}

func (c *conn) Get(name string) (string, error) {
	value, err := c.delegate.Get(c.ctx, name)
	return value.String(), err
}

func (c *conn) Set(name string, value string) (bool, error) {
	reply, err := c.delegate.Set(c.ctx, name, value)
	return reply.Bool(), err
}

func (c *conn) SetNX(name string, value string, expiry time.Duration) (bool, error) {
	switch expiry {
	case 0:
		return c.delegate.SetNX(c.ctx, name, value)
	case -1:
		res, err := c.delegate.Set(c.ctx, name, value, gredis.SetOption{
			TTLOption: gredis.TTLOption{
				KeepTTL: true,
			},
			NX: true,
		})
		return res.Bool(), err
	default:
		op := gredis.SetOption{
			NX: true,
		}
		if usePrecise(expiry) {
			op.PX = formatMs(c.ctx, expiry)
		} else {
			op.EX = formatSec(c.ctx, expiry)
		}
		res, err := c.delegate.Set(c.ctx, name, value, op)
		return res.Bool(), err
	}
}

func (c *conn) PTTL(name string) (time.Duration, error) {
	res, err := c.delegate.PTTL(c.ctx, name)
	return time.Duration(res) * time.Millisecond, err
}

func (c *conn) Eval(script *redsyncredis.Script, keysAndArgs ...interface{}) (interface{}, error) {

	keys := make([]string, script.KeyCount)
	args := keysAndArgs

	if script.KeyCount > 0 {
		for i := 0; i < script.KeyCount; i++ {
			keys[i] = keysAndArgs[i].(string)
		}
		args = keysAndArgs[script.KeyCount:]
	}

	v, err := c.delegate.EvalSha(c.ctx, script.Hash, int64(script.KeyCount), keys, args)
	if err != nil && strings.Contains(err.Error(), "NOSCRIPT ") {
		v, err = c.delegate.Eval(c.ctx, script.Src, int64(script.KeyCount), keys, args)
	}
	return v.Interface(), err
}

func (c *conn) Close() error {
	// Not needed for this library
	return nil
}

func noErrNil(err error) error { // TODO 不确定是否需要加上这个
	/*if err == gredis. {
		return nil
	}*/
	return err
}

func usePrecise(dur time.Duration) bool {
	return dur < time.Second || dur%time.Second != 0
}

func formatMs(ctx context.Context, dur time.Duration) *int64 {
	res := dur.Milliseconds()
	return &res
}

func formatSec(ctx context.Context, dur time.Duration) *int64 {
	res := int64(dur.Seconds())
	return &res
}
