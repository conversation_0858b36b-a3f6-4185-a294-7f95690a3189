package redsync

import (
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"time"
)

type GetLockInput struct {
	Key          any   `json:"key"`
	ExpireSecond int64 `json:"expireSecond"` // 过期时间(以秒为单位)
}

type GetLockOutput struct {
	Lock   *Lock
	Unlock func()
}

// GetLock 获取锁
func GetLock(ctx g.Ctx, in GetLockInput) (out *GetLockOutput, err error) {
	out = new(GetLockOutput)
	newKey := "redsync." + gconv.String(in.Key)
	if in.ExpireSecond < 1 {
		in.ExpireSecond = 30 // 默认30秒
	}
	g.Log().Debug(ctx, newKey)
	out.Lock = NewLock(newKey, time.Duration(in.ExpireSecond)*time.Second)
	if out.Lock == nil {
		err = gerror.NewCodef(gcode.CodeInternalError, "redsync.GetLock: %s fail", newKey)
		g.Log().Line().Error(ctx, err)
		return out, err
	}
	// 开始上锁
	lockedErr := out.Lock.Lock(ctx)
	if lockedErr != nil {
		err = gerror.NewCode(gcode.CodeOperationFailed, "concurrently is not allowed") // 上锁失败， 不允许并行进行
		return out, err
	}
	out.Unlock = func() {
		_, _ = out.Lock.Unlock(ctx)
	}
	return out, nil
}
