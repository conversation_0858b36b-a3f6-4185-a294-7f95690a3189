package redsync

// 引入 redsync 分布式锁库， 并进行业务层封装
import (
	"context"
	"halalplus/utility/exception"
	"sync"
	"time"

	"github.com/go-redsync/redsync/v4"
	_ "github.com/gogf/gf/contrib/drivers/mysql/v2"
	_ "github.com/gogf/gf/contrib/nosql/redis/v2"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/util/gutil"
)

type Lock struct {
	Key        string
	expire     time.Duration // 锁的过期时间，但可以通过续租约来延长
	maxLife    time.Duration // 锁的最长存活时间，即续约最多只能续到这个时间 ，到时将强制解锁
	mutex      *redsync.Mutex
	stopChan   chan struct{}
	unlockOnce sync.Once
}

var locker *redsync.Redsync
var defaultMaxLife = time.Minute // 锁的最长存活时间 ，默认是一分钟
var defaultMaxLifeLock sync.RWMutex

func init() {
	ctx := gctx.New()
	// 尝试从数据库中取得锁的最长时间, 如果取不到，就使用默认时间
	gutil.Go(ctx, func(ctx context.Context) {
		time.Sleep(time.Minute)
		for {
			v, err := g.DB().Ctx(ctx).Raw("show variables like 'innodb_lock_wait_timeout'").Fields("Value").All()
			if err == nil {
				if v.Len() > 0 {
					vi, ok := v[0].Map()["Value"].(int)
					if ok && vi > 0 {
						defaultMaxLifeLock.Lock()
						defaultMaxLife = time.Duration(vi+2) * time.Second // 如果能取到，就在这个时间的基础上加2秒，确保一定长于事务超时时间
						g.Log().Line().Debug(ctx, "lock defaultMaxLife changed:", defaultMaxLife.Seconds())
						defaultMaxLifeLock.Unlock()
					}
				}
			} else {
				g.Log().Line().Error(ctx, err)
			}
			time.Sleep(time.Hour * 23)
		}
	}, nil)
	// Create an instance of redsync to be used to obtain a mutual exclusion lock.
	rs := redsync.New(NewPool(g.Redis()))
	locker = rs
	if locker == nil {
		panic("redis locker nil")
	}
}

// NewLock: 新建一把锁。  一把锁只能进行一次性的Lock+unlock。
// key: 锁的唯一key
// expire: 锁的过期时间 （续租约的时间固定为过期时间的一半）
func NewLock(key string, expire time.Duration) *Lock {
	defaultMaxLifeLock.RLock()
	maxLife := defaultMaxLife
	defaultMaxLifeLock.RUnlock()
	mutex := locker.NewMutex(key, redsync.WithExpiry(expire))
	return &Lock{
		Key:        key,
		expire:     expire,
		mutex:      mutex,
		maxLife:    maxLife,
		stopChan:   make(chan struct{}),
		unlockOnce: sync.Once{},
	}
}

// Lock: 一定要拿到锁，上锁后自动续租约
func (l *Lock) Lock(ctx context.Context) error {
	err := l.mutex.LockContext(ctx)
	if err != nil {
		return err
	}
	gutil.Go(ctx, func(ctx context.Context) {
		ticker := time.NewTicker(l.expire / 2)
		maxLifeTicker := time.NewTicker(l.maxLife)
		defer func() {
			ticker.Stop()
			maxLifeTicker.Stop()
		}()
		for {
			select {
			case <-ticker.C:
				l.mutex.Extend()
				// 续约操作
			case <-l.stopChan:
				return
			case <-maxLifeTicker.C:
				l.mutex.Unlock()
				return
			}
		}
	}, exception.RecoverPrint)
	return nil
}

func (l *Lock) Unlock(ctx context.Context) (bool, error) {
	l.unlockOnce.Do(func() {
		close(l.stopChan)
	})
	return l.mutex.UnlockContext(ctx)
}

// LockWithoutAutoLease: 一定要拿到锁，但不会自动续租(需要手动调用Extend来实现
func (l *Lock) LockWithoutAutoLease(ctx context.Context) error {
	return l.mutex.Lock()
}

// TryLock: 只尝试上锁一次， 失败就返回error
func (l *Lock) TryLock(ctx context.Context) error {
	return l.mutex.TryLockContext(ctx)
}

// 一些意外情况的处理：
/*
* 上锁后业务异常卡死，一直续租：本组件不处理这种情况，需要在业务侧做好对这种情况的处理。比如redis/mysql的最大超时时间。
* goroutine加锁后panic导致未执行unlock: 需要手动在lock后增加defer unlock的代码
* 上锁一直拿不到锁：会一直有时间间隔地重试，直到达到最大重试次数（重试32次），然后返回error，因此在竞争非常大的情况下也要自行做好超时处理
*
 */
