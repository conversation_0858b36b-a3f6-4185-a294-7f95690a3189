package redsync

import (
	_ "github.com/gogf/gf/contrib/drivers/mysql/v2"
	"github.com/gogf/gf/v2/os/gctx"
	"testing"
	"time"
)

func TestLock(t *testing.T) {

}

func TestNewLock(t *testing.T) {
	lock := NewLock("uid1", 6*time.Second)
	ctx := gctx.New()
	go func() {
		err := lock.Lock(ctx)
		t.Log("locked:", err, time.Now().String())
	}()
	go func() {
		newLock := NewLock("uid1", 3*time.Second)
		for i := 0; i < 10; i++ {
			time.Sleep(time.Second)
			err := newLock.Lock(ctx)
			t.Log("lockedafter:", i, err, time.Now().String())

		}
	}()
	time.Sleep(5 * time.Second)
	t.Log(lock.Unlock(ctx))

	time.Sleep(3 * time.Second)

}
