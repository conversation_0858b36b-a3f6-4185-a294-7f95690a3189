package bloom

import (
	"context"
	"github.com/gogf/gf/v2/database/gredis"
	"github.com/spaolacci/murmur3"
)

type (
	Filter struct {
		bits   uint
		bitSet bitsSetProvider
	}

	bitsSetProvider interface {
		check(ctx context.Context, offsets []uint) (bool, error)
		set(ctx context.Context, offsets []uint) error
	}
)

func New(store *gredis.Redis, key string, setCount uint) *Filter {
	bits := 20 * setCount
	return &Filter{
		bits:   bits,
		bitSet: newRedisBitSet(store, key, bits),
	}
}

// for detailed error rate table, see http://pages.cs.wisc.edu/~cao/papers/summary-cache/node8.html
// hashFuncCount as k in the error rate table
const hashFuncCount = 14

func (f *Filter) getLocations(data []byte) []uint {
	locations := make([]uint, hashFuncCount)
	for i := uint(0); i < hashFuncCount; i++ {
		hashVal := hashSum(append(data, byte(i)))
		locations[i] = uint(hashVal % uint64(f.bits))
	}
	return locations

}

func hashSum(data []byte) uint64 {
	return murmur3.Sum64(data)
}

// Add 数据key对应的value转为字节数组， 存在key对应的字节数组中。
func (f *Filter) Add(ctx context.Context, data []byte) error {
	locs := f.getLocations(data)
	return f.bitSet.set(ctx, locs)
}

// Exists 数据key对应的value转为字节数组， 遍历所有字节判断 是否已有改数据，如果返回值为false一定没有，
// 返回值为true则可能没有 还要进一步查询db等类似数据源进行判断
func (f *Filter) Exists(ctx context.Context, data []byte) (bool, error) {
	loc := f.getLocations(data)
	isExist, err := f.bitSet.check(ctx, loc)
	if err != nil {
		return true, err
	}
	return isExist, nil
}
