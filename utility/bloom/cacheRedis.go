package bloom

import (
	"context"
	"errors"
	"github.com/gogf/gf/v2/database/gredis"
	"github.com/gogf/gf/v2/util/gconv"
	"strconv"
)

type (
	redisBitSet struct {
		store *gredis.Redis
		key   string
		bits  uint
	}
)

var (
	setBitScript = `
for _, offset in ipairs(ARGV) do
	redis.call("setbit", KEYS[1], offset, 1)
end
`
	//	checkBitScript = `
	//for _, offset in ipairs(ARGV) do
	//	if tonumber(redis.call("getbit" KEYS[1], offset)) == 0 then
	//    	return false
	//    end
	//end
	//return true
	//`

	checkBitScript = `
for _, offset in ipairs(ARGV) do
	if tonumber(redis.call("getbit", KEYS[1], offset)) == 0 then
		return false
	end
end
return true
`
)

func newRedisBitSet(store *gredis.Redis, key string, bits uint) *redisBitSet {
	return &redisBitSet{
		store: store,
		key:   key,
		bits:  bits,
	}
}

// 偏离量整形数组  转为字符数组
func (r *redisBitSet) buildOffsetArgs(offsets []uint) ([]string, error) {
	var args []string
	for _, off := range offsets {
		if off > r.bits {
			return nil, errors.New("too large offset")
		}
		ar := strconv.FormatUint(uint64(off), 10)
		args = append(args, ar)
	}
	return args, nil
}

func (r *redisBitSet) check(ctx context.Context, offsets []uint) (bool, error) {
	args, err := r.buildOffsetArgs(offsets)
	if err != nil {
		return false, err
	}

	val, err := r.store.Eval(ctx, checkBitScript, 1, []string{r.key}, gconv.Interfaces(args))
	if err != nil {
		return false, err
	}
	if val.Int64() == 1 {
		return true, nil
	}

	return false, nil
}

func (r *redisBitSet) Del(ctx context.Context) error {
	_, err := r.store.Del(ctx, r.key)
	return err
}

func (r *redisBitSet) set(ctx context.Context, offsets []uint) error {
	args, err := r.buildOffsetArgs(offsets)
	if err != nil {
		return err
	}
	_, err = r.store.Eval(ctx, setBitScript, 1, []string{r.key}, gconv.Interfaces(args))
	return err
}
