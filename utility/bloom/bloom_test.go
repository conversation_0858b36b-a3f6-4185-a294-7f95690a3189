package bloom

import (
	"fmt"
	"github.com/gogf/gf/contrib/nosql/redis/v2"
	"github.com/gogf/gf/v2/database/gredis"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/util/grand"
	"github.com/stretchr/testify/assert"
	"testing"
)

func newTestRedis() (*gredis.Redis, error) {
	var (
		err         error
		store       *gredis.Redis
		redisConfig = &gredis.Config{
			Address: "*************:6379",
			Db:      2,
			Pass:    "wg=YjQgsjy",
			Cluster: false,
		}
	)
	gredis.RegisterAdapterFunc(func(config *gredis.Config) gredis.Adapter {
		return redis.New(config)
	})
	store, err = gredis.New(redisConfig)
	return store, err
}

func TestRedisBitSet_New_Set_Test(t *testing.T) {
	var (
		err   error
		ctx   = gctx.New()
		store *gredis.Redis
	)
	store, err = newTestRedis()

	bitSet := newRedisBitSet(store, "test_key", 1024)
	isSetBefore, err := bitSet.check(ctx, []uint{0})
	if err != nil {
		t.Fatal(err)
	}
	if isSetBefore {
		t.Fatal("Bit should not be set")
	}
	err = bitSet.set(ctx, []uint{512})
	if err != nil {
		t.Fatal(err)
	}
	isSetAfter, err := bitSet.check(ctx, []uint{512})
	if err != nil {
		t.Fatal(err)
	}
	if !isSetAfter {
		t.Fatal("Bit should be set")
	}

	err = bitSet.Del(ctx)
	if err != nil {
		t.Fatal(err)
	}
}

func TestRedisBitSet_Add(t *testing.T) {
	var (
		err   error
		ctx   = gctx.New()
		store *gredis.Redis
	)
	store, err = newTestRedis()

	filter := New(store, "test_key", 64)
	assert.Nil(t, filter.Add(ctx, []byte("hello")))
	assert.Nil(t, filter.Add(ctx, []byte("world")))
	ok, err := filter.Exists(ctx, []byte("hello"))
	assert.Nil(t, err)
	assert.True(t, ok)
}

func TestFilter_Exists(t *testing.T) {
	var (
		err   error
		ctx   = gctx.New()
		store *gredis.Redis
	)
	store, err = newTestRedis()

	rbs := New(store, "test", 64)
	_, err = rbs.Exists(ctx, []byte{0, 1, 2})
	assert.NoError(t, err)

}

func TestRedisBitSet_check(t *testing.T) {
	var (
		err   error
		ctx   = gctx.New()
		store *gredis.Redis
	)
	store, err = newTestRedis()

	rbs := newRedisBitSet(store, "test", 0)
	assert.Error(t, rbs.set(ctx, []uint{0, 1, 2}))
	_, err = rbs.check(ctx, []uint{0, 1, 2})
	assert.Error(t, err)

	rbs = newRedisBitSet(store, "test", 64)
	_, err = rbs.check(ctx, []uint{0, 1, 2})
	assert.NoError(t, err)
}

func TestRedisBatch(t *testing.T) {
	var (
		err   error
		ctx   = gctx.New()
		store *gredis.Redis
	)
	size := 100 * 1000
	setAccount := make(map[string]struct{}, size)
	for i := 1; i <= 100; i++ {
		key := GenRandomAccountName()
		setAccount[key] = struct{}{}
	}

	fmt.Printf("Begin total=%d ", len(setAccount))
	store, err = newTestRedis()
	filter := New(store, "bitset:setAccount", uint(size*20))
	//for k, _ := range setAccount {
	//	err = filter.Add(ctx, []byte(k))
	//	if err != nil {
	//		fmt.Printf("add failed! key:%s, err:%s", k, err)
	//		return
	//	}
	//}

	var isExist bool
	var failedCount int
	for k, _ := range setAccount {
		isExist, err = filter.Exists(ctx, []byte(k))
		if err != nil {
			fmt.Printf("Exists failed! key:%s, err:%s", k, err)
			return
		}
		if !isExist {
			failedCount++
		}
	}

	fmt.Printf("Finish total=%d, failedCount=%d\n", len(setAccount), failedCount)
}

func GenRandomAccountName() string {
	// 生成随机12位账号名，首位必须是字符
	head := grand.Letters(1)
	// 后7位字母加数字
	body := grand.S(11, false)
	return head + body
}
