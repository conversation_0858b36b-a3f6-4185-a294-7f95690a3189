package utility

import (
	"testing"
	"time"
)

func TestGetTimeRangeMonths(t *testing.T) {
	//t1, err := time.ParseInLocation(time.DateTime, "2024-10-01 00:00:00", GetDefaultLocation())
	//if err != nil {
	//	return
	//}
	//t.Log(t1)
	//t2, err := time.ParseInLocation(time.DateTime, "2024-10-01 00:00:00", GetDefaultLocation())
	//if err != nil {
	//	return
	//}
	//t.Log(t2)
	//
	//out := GetTimeRangeMonths(context.Background(), t1.UnixMilli(), t2.UnixMilli())
	//
	//for _, row := range out {
	//	t.Log(row.StartTime.Format(time.DateTime), row.EndTime.Format(time.DateTime))
	//}
	//
}

func TestWeek(t *testing.T) {
	t1 := time.Date(2024, 12, 11, 0, 0, 0, 0, GetDefaultLocation())
	t.Log(t1.ISOWeek())
	t.Log(GetThisWeekMonday(t1).ISOWeek())
}

func TestGetYinNiDate(t *testing.T) {
	var startTime int64 = 1736960400000
	t.Log(GetYinNiDate(startTime))
}
