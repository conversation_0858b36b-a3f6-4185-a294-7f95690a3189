package utility

// Slice2Map 把一个slice转成map
// slice: 指定类型的一个数组
// keyFunc: 从这个元素中获取key的函数
// VE: slice里元素的类型
// KE: 转成的map的key类型
// 示例： 把一个 []User 转成 map[uint]User, 就是  Slice2Map[User, uint](...)
func Slice2Map[VE any, KE comparable](slice []VE, keyFunc func(e VE) KE) map[KE]VE {
	m := make(map[KE]VE)
	for _, e := range slice {
		m[keyFunc(e)] = e
	}
	return m
}

// Slice2MapSlice: []xx -> map[][]xxx
func Slice2MapSlice[VE any, KE comparable](slice []VE, keyFunc func(e VE) KE) map[KE][]VE {
	m := make(map[KE][]VE)
	for _, e := range slice {
		ke := keyFunc(e)
		m[ke] = append(m[ke], e)
	}
	return m
}

// SliceConv 把一个slice转成另一个类型的slice, 比如可以从 []user中取出 user.id, 组成一个 []uint
func SliceConv[E1, E2 any](slice []E1, convFunc func(e E1) E2) (newSlice []E2) {
	newSlice = make([]E2, len(slice))
	for i, e := range slice {
		newSlice[i] = convFunc(e)
	}
	return newSlice
}
