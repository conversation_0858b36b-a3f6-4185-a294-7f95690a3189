package utility

import (
	"testing"
	"time"
)

func TestContainsSpecialChars(t *testing.T) {
	type args struct {
		srcObjectName string
	}
	var tests = []struct {
		name string
		args args
		want bool
	}{
		// TODO: Add test cases.
		{
			name: "Test1",
			args: args{
				srcObjectName: "[\"tmp/pub/userRemark/1fzy5jk3ck6d3dxqwf0o00id007j1vg9.png\"]",
			},
			want: true,
		},
		{
			name: "Test2",
			args: args{
				srcObjectName: "tmp/pub/userRemark/1fzy5jk3ck6d3dxqwf0o00id007j1vg9.png",
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ContainsSpecialChars(tt.args.srcObjectName); got != tt.want {
				t.<PERSON>rrorf("ContainsSpecialChars() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestTuoMin(t *testing.T) {
	// t.Log(TuoMin("account"))

	yearStart, err := time.Parse(time.DateTime, "2024-01-01 00:00:00")
	if err != nil {
		t.Error(err)
	}
	for i := 0; i < 366; i++ {
		locNow := yearStart.AddDate(0, 0, i)
		day := locNow.Day()
		monthBegin := locNow.AddDate(0, -1, -day+1)
		t.Log(locNow.Format(time.DateTime), monthBegin.Format(time.DateTime))
	}

	// now := time.Date(time.Now().Year(), time.Now().Month(), 1, 0, 0, 0, 0, GetDefaultLocation()).AddDate(0, -1, 0)
	// lastMonth := now.AddDate(0, -1, 0)

}

func TestParseDomain(t *testing.T) {
	t.Log(ParseDomain("https://m1.ngtests.com?agentCode=z8xgzx"))
}

func TestGgetLocalLANIP(t *testing.T) {
	lanIp := GetLocalLANIP()
	t.Log(lanIp)
}
