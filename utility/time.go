package utility

import (
	"context"
	"halalplus/utility/slices"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/gtime"
)

// https://jp.cybozu.help/general/zh/admin/list_systemadmin/list_localization/timezone.html
var loc, _ = time.LoadLocation("Asia/Jakarta") // (UTC+07:00) 曼谷,河内,雅加达

func GetDefaultLocation() *time.Location {
	return loc
}

func GetDefaultTime(t time.Time) time.Time {
	return t.In(loc)
}

func ConvertDate(beginTime, endTime int64) (begin, end string) {
	t1 := time.Unix(beginTime/1000, 0)
	// 格式化为 年-月-日
	begin = t1.Format("2006-01-02")

	t2 := time.Unix(endTime/1000, 0)
	// 格式化为 年-月-日
	end = t2.Format("2006-01-02")
	return
}

func ConvertDateCompact(beginTime, endTime int64) (begin, end string) {
	t1 := time.Unix(beginTime/1000, 0)
	// 格式化为 年-月-日
	begin = t1.Format("20060102")

	t2 := time.Unix(endTime/1000, 0)
	// 格式化为 年-月-日
	end = t2.Format("20060102")

	return
}

func GTimeDate() *gtime.Time {
	now := ConvertZoneTime()
	return now.StartOfMonth()
}

func GetDateByTime(dateTime int64) *gtime.Time {
	loc := GetDefaultLocation()
	dateT := time.UnixMilli(dateTime).In(loc)
	st := time.Date(dateT.Year(), dateT.Month(), dateT.Day(), 0, 0, 0, 0, loc)
	return gtime.NewFromTime(st)
}

func GetYinNiDate(dateTime int64) string {
	loc := GetDefaultLocation()
	dateT := time.UnixMilli(dateTime).In(loc)
	st := time.Date(dateT.Year(), dateT.Month(), dateT.Day(), 0, 0, 0, 0, loc)
	return st.Format("2006-01-02")
}

func GetDate() string {
	var now *gtime.Time
	now = ConvertZoneTime()
	return now.Layout("2006-01-02")
}

func GetDateHour() (string, int) {
	var now *gtime.Time
	for {
		now = ConvertZoneTime()
		minu := now.Minute()
		// second := now.Second()
		if minu >= 59 {
			time.Sleep(100 * time.Millisecond)
		} else {
			break
		}
	}

	return now.Layout("2006-01-02"), now.Hour()
}

func GetTodayRange() (todayBegin, todayEnd int64) {
	// 处理定时器偏差的问题  可能定时器超时时间无限接近整小时
	// 获取的当前时间 分钟必须 在 [0 1]之间
	for {
		now := ConvertZoneTime()
		if now.Minute() >= 59 && now.Second() >= 58 {
			time.Sleep(100 * time.Millisecond)
		} else {
			break
		}
	}

	now := ConvertZoneTime().StartOfDay()
	todayEnd = now.Add(24 * time.Hour).UnixMilli()
	todayBegin = now.UnixMilli()
	return
}

func ConvertZoneTime() *gtime.Time {
	// 现行时间
	nowUTC := time.Now().UTC()
	zone := "Asia/Jakarta"
	// 转换为印尼时间
	t1, err := gtime.ConvertZone(nowUTC.Format("2006-01-02 15:04:05"), zone, "")
	if err != nil {
		g.Log().Line().Errorf(gctx.New(), "ConvertZone %s Time failed! ", zone)
	}
	return t1
}

// 取时间戳的当天(默认时区)范围
func GetDayRange(ctx context.Context, dateTime int64) (startTime int64, endTime int64, err error) {
	loc := GetDefaultLocation()
	dateT := time.UnixMilli(dateTime).In(loc)
	startTime = time.Date(dateT.Year(), dateT.Month(), dateT.Day(), 0, 0, 0, 0, loc).UnixMilli()
	endTime = startTime + 1000*60*60*24 - 1
	return
}

func GetMonthRange(ctx context.Context, dateTime int64) (startTime int64, endTime int64, err error) {
	loc := GetDefaultLocation()
	dateT := time.UnixMilli(dateTime).In(loc)
	monthStartTime := time.Date(dateT.Year(), dateT.Month(), 1, 0, 0, 0, 0, loc)
	startTime = monthStartTime.UnixMilli()
	endTime = monthStartTime.AddDate(0, 1, 0).UnixMilli() - 1
	return
}

type TimeRange struct {
	StartTime time.Time `json:"startTime"`
	EndTime   time.Time `json:"endTime"`
}

// GetTimeRangeDays 获取时间的天数范围
func GetTimeRangeDays(ctx context.Context, startTime int64, endTime int64) (out []TimeRange) {

	s := time.UnixMilli(startTime).In(GetDefaultLocation())
	e := time.UnixMilli(endTime).In(GetDefaultLocation())
	m := s
	i := 0
	for m.Before(e) || m.Equal(e) {
		item := TimeRange{
			StartTime: m,
			EndTime:   m.AddDate(0, 0, 1).Add(-1 * time.Millisecond),
		}
		out = append(out, item)
		m = m.AddDate(0, 0, 1)
		i++
		if i > 365 {
			break
		}
	}
	slices.Reverse(out)
	return
}

// GetTimeRangeMonths 获取时间的月份范围
func GetTimeRangeMonths(ctx context.Context, startTime int64, endTime int64) (out []TimeRange) {
	m := startTime
	i := 0
	for m <= endTime {
		start, end, err := GetMonthRange(ctx, m)
		if err != nil {
			break
		}
		item := TimeRange{
			StartTime: time.UnixMilli(start).In(loc),
			EndTime:   time.UnixMilli(end).In(loc),
		}
		out = append(out, item)
		i++
		if i > 12 {
			break
		}
		m = item.StartTime.AddDate(0, 1, 0).UnixMilli()
	}
	slices.Reverse(out)
	return

}

func GetThisWeekMonday(t time.Time) time.Time {
	// 获取当前时间是星期几 (星期天为0，星期一为1，以此类推)
	weekday := int(t.Weekday())

	// 计算到星期一的偏移量
	offset := weekday
	if offset == 0 { // 如果是星期天，偏移量设为6 (即前推6天)
		offset = 6
	} else {
		offset -= 1 // 其他情况，前推 weekday - 1 天
	}

	// 获取本周一的日期
	monday := t.AddDate(0, 0, -offset)

	// 将时间设置为午夜 00:00:00，确保是一周的第一秒
	monday = time.Date(monday.Year(), monday.Month(), monday.Day(), 0, 0, 0, 0, monday.Location())

	return monday
}

func GetThisMonthDay1(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), 1, 0, 0, 0, 0, t.Location())
}

// 返回上周周一的时间
func GetLastMonday(t time.Time) time.Time {
	// 获取给定时间所在的星期几
	weekday := t.Weekday()

	// 计算到上周一的天数差
	// 如果今天是星期天，则需要特殊处理
	daysToSubtract := 0
	if weekday == time.Sunday {
		daysToSubtract = 7 + 6
	} else {
		daysToSubtract = int(weekday) + 6
	}

	// 减去相应的天数
	lastMonday := t.AddDate(0, 0, -daysToSubtract)
	lastMonday = time.Date(lastMonday.Year(), lastMonday.Month(), lastMonday.Day(), 0, 0, 0, 0, lastMonday.Location())
	return lastMonday
}

// 本周最后一毫秒
func GetEndOfWeekTimestamp(t time.Time) int64 {
	// 获取本周的最后一天（星期日）
	endOfWeek := t.AddDate(0, 0, (int(time.Sunday)-int(t.Weekday())+7)%7)
	// 设置时间为当天的最后一秒（23:59:59.999999999）
	endOfWeek = time.Date(endOfWeek.Year(), endOfWeek.Month(), endOfWeek.Day(), 23, 59, 59, 999999999, endOfWeek.Location())
	// 使用 UnixMilli 获取毫秒时间戳
	return endOfWeek.UnixMilli()
}
