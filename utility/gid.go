package utility

import (
	"bytes"
	"errors"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gogf/gf/v2/util/grand"
	"strconv"
	"strings"
	"sync"
	"time"
)

const (
	sequenceMax = 9999 // 最大值 999
)

// Gid 生成唯一数字
// 12位时间格式(yymdhis)+2位机器号+用户数字后2位+3位随机数
type Gid struct {
	workerId  string // 机器号
	timestamp int64
	sequence  int // 序列号
	data      *bytes.Buffer
	mu        sync.Mutex
}

func NewGid(workerId int) (*Gid, error) {
	if workerId <= 0 || workerId > 9 {
		return nil, errors.New("NewGid:workerId must by 1~9")
	}
	return &Gid{
		workerId: gconv.String(workerId),
		data:     bytes.NewBuffer(make([]byte, 0, 20)),
	}, nil
}

func (g *Gid) Generate(useId uint) string {
	g.mu.Lock()
	defer g.mu.Unlock()
	now := time.Now().Unix()
	if now == g.timestamp {
		g.sequence = g.sequence + 1
		if g.sequence > sequenceMax {
			for now <= g.timestamp {
				time.Sleep(time.Microsecond * 100)
				now = time.Now().Unix()
			}
			g.sequence = grand.N(1, 999)
		}
	} else {
		g.sequence = grand.N(1, 999)
	}
	g.timestamp = now
	g.data.Reset()
	g.data.WriteString(time.Unix(now, 0).Format("060102150405"))
	g.data.WriteString(g.workerId)
	g.data.WriteString(g.GetSuffix(int64(useId), 2))
	g.data.WriteString(g.GetSuffix(int64(g.sequence), 4))
	return g.data.String()
}

func (g *Gid) GetSuffix(id int64, l int) string {
	data := strconv.FormatInt(id, 10)
	if len(data) < l {
		data = strings.Repeat("0", l-len(data)) + data
	} else if len(data) > l {
		data = data[len(data)-l:]
	}
	return data
}
