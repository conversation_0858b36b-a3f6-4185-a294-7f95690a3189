package utility

import (
	"errors"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/hashicorp/consul/api"
	"os"
)

// GetConsulConfig 环境变量设置了CONSUL_HTTP_ADDR才生效
func GetConsulConfig() (*api.Config, error) {
	if g.<PERSON>(os.Getenv(api.HTTPAddrEnvName)) {
		return nil, errors.New("env var " + api.HTTPAddrEnvName + " is not set")
	}
	return api.DefaultConfig(), nil
}

// GetConsulGoFrameCfgPath 环境变量设置了CONSUL_GF_CONFIG_PATH才生效
func GetConsulGoFrameCfgPath() (string, error) {
	configPath := os.Getenv("CONSUL_GF_CONFIG_PATH")
	if g.<PERSON>(configPath) {
		return "", errors.New("env var CONSUL_GF_CONFIG_PATH is not set")
	}
	return configPath, nil
}
