package unary

import (
	"context"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/util/gconv"
	"google.golang.org/grpc"
	"halalplus/api/common"
)

// UnaryCommonError 错误处理中间件
func UnaryCommonError(
	ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler,
) (interface{}, error) {
	res, err := handler(ctx, req)
	if err != nil {
		code := gerror.Code(err)
		if code.Code() != -1 {
			ersp := &common.CommonResponse{
				Error: &common.Error{},
			}
			ersp.Code = 1
			ersp.Msg = code.Message()
			ersp.Error.Code = int32(code.Code())
			ersp.Error.Reason = code.Message()
			ersp.Error.Detail = gconv.String(code.Detail())
			err = nil
			res = ersp
		}
	}
	return res, err
}
