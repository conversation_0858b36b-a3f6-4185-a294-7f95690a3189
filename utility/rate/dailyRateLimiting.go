package rate

import (
	"context"
	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/database/gredis"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"idnuapi/internal/consts"
)

func RequestCtrlDailyLimit(ctx context.Context, key string, limit int) (err error) {
	var requestedCount *gvar.Var
	requestedCount, err = g.Redis().Get(ctx, key)
	if err != nil {
		return err
	}
	// the key does not exist
	if requestedCount.Val() == nil {
		// 设置键值对，记录IP的发送次数，并设置过期时间\
		todayEnd := gtime.Now().EndOfDay().Unix()
		leftSecond := todayEnd - gtime.Now().Unix()
		gRet, err := g.Redis().Set(ctx, key, 1, gredis.SetOption{
			TTLOption: gredis.TTLOption{
				EX: &leftSecond,
			},
			NX: true,
		})
		if err != nil {
			return err
		}
		// when is nil the key exist
		if gRet.Val() == nil {
			// 重新获取值
			requestedCount, err = g.Redis().Get(ctx, key)
		} else {
			g.Log().Line().Debugf(ctx, "RequestCtrlDailyLimit key:%s one day first time, limit:%d", key, limit)
			// 第一次 设置为1  不需要累加 直接返回
			return nil
		}
	}

	// 判断是否超过当天最大发送次数
	if requestedCount.Int() > limit {
		g.Log().Line().Debugf(ctx, "RequestCtrlDailyLimit key:%s exceeding limit:%d requested count:%d. Access denied!", key, limit, requestedCount.Int())
		return gerror.New(g.I18n().Tf(ctx, `account.count.exceeds.limit`))
	} else {
		var lastCount int64 = 0
		lastCount, err = g.Redis().Incr(ctx, key)
		// 防止并发操作超过限定值
		if int(lastCount) > limit {
			g.Log().Line().Debugf(ctx, "RequestCtrlDailyLimit key:%s exceeding limit:%d requested count:%d. Access denied!", key, limit, requestedCount.Int())
			return gerror.New(g.I18n().Tf(ctx, `account.count.exceeds.limit`))
		}
	}
	return
}

func RequestDailyCtrlIncCount(ctx context.Context, key string) (err error) {
	var requestedCount *gvar.Var
	requestedCount, err = g.Redis().Get(ctx, key)
	if err != nil {
		return err
	}

	// the key does exist
	if requestedCount.Val() != nil {
		_, err = g.Redis().Incr(ctx, key)
		return
	}

	// 设置键值对，记录IP的发送次数，并设置过期时间
	_, err = g.Redis().Set(ctx, key, 1, gredis.SetOption{
		TTLOption: gredis.TTLOption{
			EX: &consts.OneDayVal,
		},
		NX: true,
	})
	if err == nil {
		g.Log().Line().Debugf(ctx, "RequestDailyCtrlIncCount key:%s one day first time ", key)
	}
	return
}

func RequestDailyCtrlCheckCount(ctx context.Context, key string, limit int) (err error) {
	var requestedCount *gvar.Var
	requestedCount, err = g.Redis().Get(ctx, key)
	if err != nil {
		return err
	}
	// the key does not exist
	if requestedCount.Val() != nil {
		// 判断是否超过当天最大发送次数
		if requestedCount.Int() > limit {
			g.Log().Line().Debugf(ctx, "RequestDailyCtrlCheckCount key:%s exceeding limit:%d requested count:%d. Access denied!", key, limit, requestedCount.Int())
			return gerror.New(g.I18n().Tf(ctx, `account.count.exceeds.limit`))
		}
	}
	return
}

func RequestDailyCtrlCheckAndIncCount(ctx context.Context, key string, limit int) (err error) {
	err = RequestDailyCtrlCheckCount(ctx, key, limit)
	if err != nil {
		return
	}
	err = RequestDailyCtrlIncCount(ctx, key)
	return
}
