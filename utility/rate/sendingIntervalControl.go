package rate

import (
	"context"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"time"
)

func RequestCtrlSendingInterval(ctx context.Context, key string, limitInterval int) (err error) {
	return
	// 获取最后发送时间
	lastSendTime, err := g.Redis().Get(ctx, key)
	if err != nil {
		return err
	}

	currentTime := time.Now().Unix()
	//the key does not exist
	if lastSendTime.Val() == nil {
		// 设置键值对 过期时间，记录key最后发送时间
		err = g.Redis().SetEX(ctx, key, currentTime, int64(limitInterval))
		if err != nil {
			return err
		}
	} else {
		// 计算时间间隔（秒）
		sendInterval := time.Duration(currentTime - lastSendTime.Int64())
		g.Log().Line().Debugf(ctx, "RequestCtrlSendingInterval key:%s sendInterval:%d less then limit:%d", key, sendInterval, limitInterval)
		err = gerror.New(g.I18n().Tf(ctx, `account.frequency.exceeds.limit`))
	}

	return
}
