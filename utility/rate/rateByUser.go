package rate

import (
	"context"
	"github.com/gogf/gf/v2/container/gmap"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/gtimer"
	"github.com/gogf/gf/v2/util/gconv"
	"golang.org/x/time/rate"
	"time"
)

// UserLimiter 用户级别的限流器
type UserLimiter struct {
	limiter  *rate.Limiter // 内置限流器
	lastSeen time.Time     // 过期时间
}

// 存放全局限流器
var userLimiters *gmap.HashMap

// 每隔多久清除过期限流器
var cleanupInterval = 10 * time.Minute

func init() {
	userLimiters = gmap.NewHashMap(true)
	gtimer.AddSingleton(gctx.New(), cleanupInterval, func(ctx context.Context) {
		cleanupExpiredLimiters(ctx)
	})
}

func getKey[T string | uint](userKey T, uri string) string {
	return gconv.String(userKey) + ":" + uri
}

// GetUserLimiter 获取限流器
// userKey 用户标识, uri 路径, requestsPerSecond 每秒限制数
func GetUserLimiter[T string | uint](userKey T, uri string, requestsPerSecond int) *rate.Limiter {
	key := getKey(userKey, uri)
	now := time.Now()
	if v, ok := userLimiters.Search(key); ok {
		userLimiter := v.(*UserLimiter)
		userLimiter.lastSeen = now
		return userLimiter.limiter
	}
	if requestsPerSecond < 1 {
		requestsPerSecond = 2
	}
	limiter := rate.NewLimiter(rate.Limit(requestsPerSecond), requestsPerSecond)
	userLimiters.Set(key, &UserLimiter{limiter: limiter, lastSeen: now})
	return limiter
}

// cleanupExpiredLimiters 清除过期的限流器
func cleanupExpiredLimiters(ctx context.Context) {
	if userLimiters.IsEmpty() {
		return
	}
	now := time.Now()
	userLimiters.Iterator(func(key, value interface{}) bool {
		userLimiter := value.(*UserLimiter)
		if now.Sub(userLimiter.lastSeen) > cleanupInterval {
			userLimiters.Remove(key)
		}
		return true
	})
}
