package rate

import (
	"context"
	"github.com/gogf/gf/v2/database/gredis"
	"github.com/gogf/gf/v2/frame/g"
	"idnuapi/internal/consts"
	"time"
)

// rateByDay: 按天维度的频控, defaultTimes: 一天限多少次。
// 工作流程： 查key, 如果不存在， 按defaultTimes创建， 成功； 如果存在，count-1, 如果count<=0就失败。

const rateByDayPrefix = "rateLimitByDay:"

// return: true表示可以通过
func RateLimitByDay(ctx context.Context, key string, now time.Time, defaultTimes int) bool {
	// 使用Redis实现by day的频控
	key = rateByDayPrefix + key + ":" + now.Format("20060102")
	gv, err := g.Redis().Get(ctx, key)
	if err != nil {
		g.Log().Line().Info(ctx, err)
		return false
	}

	if gv.IsNil() {
		g.<PERSON>is().Set(ctx, key, defaultTimes, gredis.SetOption{
			TTLOption: gredis.TTLOption{
				EX: &consts.OneDayVal,
			},
			NX: true,
		})
		return true
	}
	if gv.Int() <= 0 {
		return false
	}
	g.Redis().Decr(ctx, key)
	return true
}
