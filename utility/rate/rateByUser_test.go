package rate

import (
	"testing"
	"time"
)

func TestGetUserLimiter(t *testing.T) {
	go getUserLimiter(t, 1, 500*time.Millisecond) // 1秒访问2次
	go getUserLimiter(t, 2, 1*time.Second)        // 1秒访问1次
	go getUserLimiter(t, 3, 100*time.Millisecond) // 1秒访问10次
	time.Sleep(4 * time.Minute)
}

func TestGetUserLimiter1(t *testing.T) {
	getUserLimiter(t, 1, 500*time.Millisecond) // 1秒访问2次
}

func TestGetUserLimiter2(t *testing.T) {
	getUserLimiter(t, 2, 1*time.Second) // 1秒访问1次
}

func TestGetUserLimiter3(t *testing.T) {
	getUserLimiter(t, 3, 100*time.Millisecond) // 1秒访问10次
}

func getUserLimiter(t *testing.T, id uint, st time.Duration) {
	for i := 0; i < 30; i++ {
		limiter := GetUserLimiter(id, "test", 2)
		if limiter.Allow() {
			t.Logf("%d allow %s ", id, time.Now())
		} else {
			t.Logf("%d no %s ", id, time.Now())
		}
		time.Sleep(st)
	}
}
