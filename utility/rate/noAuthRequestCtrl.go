package rate

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"idnuapi/internal/consts"
	"idnuapi/internal/service"
)

func NoAuthCtrlByIP(ctx context.Context, limitDailyCnt int) (err error) {
	ip := service.Utility().GetClientIp(g.RequestFromCtx(ctx))
	if g.IsEmpty(ip) {
		return
	}
	keyIP := consts.KeyRateNoAuthRequest(ip)
	err = RequestCtrlDailyLimit(ctx, keyIP, limitDailyCnt)
	if err != nil {
		return
	}

	return
}
