package rate

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"idnuapi/internal/consts"
	"idnuapi/internal/service"
)

func OptCtrlByIPAccount(ctx context.Context, account string, limitDailyCnt, limitInterval int) (err error) {
	ip := service.Utility().GetClientIp(g.RequestFromCtx(ctx))
	if !g.IsEmpty(ip) {
		keyIP := consts.KeyRateOptIpDaily(ip)
		err = RequestCtrlDailyLimit(ctx, keyIP, limitDailyCnt)
		if err != nil {
			return
		}
	}

	keyAccount := consts.KeyRateOptInternal(account)
	err = RequestCtrlSendingInterval(ctx, keyAccount, limitInterval)
	//if err != nil {
	//	return gerror.New(g.I18n().Tf(ctx, fmt.Sprintf("Send frequently, try again after %d seconds.", limitInterval)))
	//}

	return
}
