package utility

import "testing"

func TestGetTopLevelDomain(t *testing.T) {
	type args struct {
		domain string
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		{
			name: "Test1",
			args: args{
				domain: "https://pc.test22.vip",
			},
			want:    "test22.vip",
			wantErr: false,
		},
		{
			name: "Test2",
			args: args{
				domain: "https://pc.test22.vip?arg1=val1",
			},
			want:    "test22.vip",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetTopLevelDomain(tt.args.domain)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetTopLevelDomain() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.<PERSON>("GetTopLevelDomain() got = %v, want %v", got, tt.want)
			}
		})
	}
}
