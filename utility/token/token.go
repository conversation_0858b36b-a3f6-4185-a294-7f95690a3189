package token

import (
	"context"
	"github.com/gogf/gf/contrib/rpc/grpcx/v2"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/golang-jwt/jwt/v5"
	"strings"
)

type JWTD struct {
	Ty string `json:"ty"`
	jwt.RegisteredClaims
}

func ParseJWT(token, secretKey string) (*JWTD, error) {
	t, err := jwt.ParseWithClaims(token, &JWTD{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(secretKey), nil
	})
	if err != nil {
		return nil, err
	}
	if claims, ok := t.Claims.(*JWTD); ok && t.Valid {
		return claims, nil
	} else {
		return nil, err
	}
}

// GetUserIdByToken 从 gRPC 上下文中提取 JWT token 并解析出用户 ID。
// NOTICE: 必须在 gRPC controller 中使用，依赖 grpcx.Ctx.IncomingMap(ctx)
func GetUserIdByToken(ctx context.Context) (userId uint64, err error) {
	headerMap := grpcx.Ctx.IncomingMap(ctx)

	// 安全获取 token 字段
	ok := headerMap.Contains(HttpToken)
	if !ok {
		return 0, T(ctx, CodeVerifyAccessTokenError)
	}

	token, ok := headerMap.Get(HttpToken).(string)
	if !ok || len(token) == 0 {
		return 0, T(ctx, CodeVerifyAccessTokenError)
	}

	// 去除 Bearer 前缀
	token = strings.TrimPrefix(token, "Bearer ")
	if len(token) == 0 {
		return 0, T(ctx, CodeVerifyAccessTokenError)
	}

	// 解析 JWT
	jwtd, err := ParseJWT(token, JWTSecretKey)
	if err != nil {
		g.Log().Debug(ctx, "failed to parse JWT", err)
		return 0, T(ctx, CodeVerifyAccessTokenError)
	}

	// 验证类型是否为用户类型
	if jwtd.Ty != "user" {
		g.Log().Debug(ctx, "invalid JWT type", jwtd.Ty)
		return 0, T(ctx, CodeVerifyAccessTokenError)
	}

	// 获取 subject（userId）
	sub, err := jwtd.GetSubject()
	if err != nil || g.IsEmpty(sub) {
		g.Log().Debug(ctx, "invalid JWT subject", jwtd)
		return 0, T(ctx, CodeVerifyAccessTokenError)
	}

	return gconv.Uint64(sub), nil
}
