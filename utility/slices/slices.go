package slices

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"strings"
)

func Index[S ~[]E, E comparable](s S, v E) int {
	for i := range s {
		if v == s[i] {
			return i
		}
	}
	return -1
}

func Contains[S ~[]E, E comparable](s S, v E) bool {
	return Index(s, v) >= 0
}

func Remove[S ~[]E, E comparable](s S, v E) S {
	i := Index(s, v)
	if i < 0 {
		return s
	}
	return Delete(s, i, i+1)
}

func Delete[S ~[]E, E any](s S, i, j int) S {
	_ = s[i:j] // bounds check

	return append(s[:i], s[j:]...)
}

func Equal[S ~[]E, E comparable](s1, s2 S) bool {
	if len(s1) != len(s2) {
		return false
	}
	for i := range s1 {
		if s1[i] != s2[i] {
			return false
		}
	}
	return true
}

// Unique 去重
func Unique[S ~[]E, E comparable](s1 S) S {
	res := make([]E, 0, len(s1))
	m := make(map[E]struct{})
	for _, item := range s1 {
		if _, ok := m[item]; ok {
			continue
		}
		m[item] = struct{}{}
		res = append(res, item)
	}
	return res
}

// RemoveEmpty 去掉空值
func RemoveEmpty[S ~[]E, E comparable](s1 S) S {
	res := make([]E, 0, len(s1))
	for _, v := range s1 {
		if g.IsEmpty(v) {
			continue
		}
		res = append(res, v)
	}
	return res
}

func Join[S ~[]E, E any](s S, sep string) string {
	data := make([]string, len(s))
	for _, r := range s {
		gs := gconv.String(r)
		if len(gs) < 1 {
			continue
		}
		data = append(data, gs)
	}
	return strings.Join(data, sep)
}

func Reverse[S ~[]E, E any](s S) {
	for i, j := 0, len(s)-1; i < j; i, j = i+1, j-1 {
		s[i], s[j] = s[j], s[i]
	}
}

func GetPage[T any](slice []T, pageNumber int, pageSize int) []T {
	// 计算切片的长度
	sliceLength := len(slice)
	// 计算分页数量
	pageCount := (sliceLength + pageSize - 1) / pageSize
	// 确保页数在有效范围内
	if pageNumber < 1 || pageNumber > pageCount {
		return nil
	}
	// 计算起始索引和结束索引
	start := (pageNumber - 1) * pageSize
	end := start + pageSize
	if end > sliceLength {
		end = sliceLength
	}
	// 返回指定页数的切片
	return slice[start:end]
}

func Concat[S ~[]E, E any](slices ...S) S {
	size := 0
	for _, s := range slices {
		size += len(s)
		if size < 0 {
			panic("len out of range")
		}
	}
	newslice := Grow[S](nil, size)
	for _, s := range slices {
		newslice = append(newslice, s...)
	}
	return newslice
}

func Grow[S ~[]E, E any](s S, n int) S {
	if n < 0 {
		panic("cannot be negative")
	}
	if n -= cap(s) - len(s); n > 0 {
		s = append(s[:cap(s)], make([]E, n)...)[:len(s)]
	}
	return s
}

// Split 切片分隔
func Split[T any](slice []T, size int) [][]T {
	if size <= 0 {
		panic("size must be greater than 0")
	}
	var result [][]T
	for i := 0; i < len(slice); i += size {
		end := i + size
		if end > len(slice) {
			end = len(slice)
		}
		result = append(result, slice[i:end])
	}

	return result
}
