package slices

import (
	"fmt"
	"reflect"
	"testing"
)

func TestRemove(t *testing.T) {
	type args[S interface{ ~[]E }, E comparable] struct {
		s S
		v E
	}
	type testCase[S interface{ ~[]E }, E comparable] struct {
		name string
		args args[S, E]
		want S
	}
	tests := []testCase[[]string, string]{
		testCase[[]string, string]{
			name: "a",
			args: args[[]string, string]{
				s: []string{"a", "b", "c"},
				v: "a",
			},
			want: []string{"b", "c"},
		},
		testCase[[]string, string]{
			name: "b",
			args: args[[]string, string]{
				s: []string{"a", "b", "c"},
				v: "b",
			},
			want: []string{"a", "c"},
		},
		testCase[[]string, string]{
			name: "c",
			args: args[[]string, string]{
				s: []string{"a", "b", "c"},
				v: "c",
			},
			want: []string{"a", "b"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Remove(tt.args.s, tt.args.v); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Remove() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetPage(t *testing.T) {
	numbers := []int{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11}
	// 每页显示的数量
	pageSize := -1
	// 需要获取的页数
	pageNumber := 1
	res := GetPage(numbers, pageNumber, pageSize)
	fmt.Println(res)
}

func TestSplit(t *testing.T) {
	r1 := Split([]int{1, 2, 3}, 2)
	t.Log(r1)
	r2 := Split([]string{"1", "2"}, 2)
	t.Log(r2)
	r3 := Split([]int{1}, 2)
	t.Log(r3)
}
