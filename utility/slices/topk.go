package slices

import (
	"container/heap"
	"sort"
)

// 只取前K个
// sortOrder； 1 asc, 2 desc
func TopK[E any](items []E, K int, getPriority func(e E) float64, sortOrder int) (topk []E) {
	//if len(items) < K {
	//	return items
	//}
	if sortOrder == 1 {
		pq := make(PQAsc, 0)
		heap.Init(&pq)
		for _, item := range items {
			heap.Push(&pq, &Item{value: item, priority: getPriority(item)})
			for pq.Len() > K {
				heap.Pop(&pq)
			}
		}
		for pq.Len() > 0 {
			item := heap.Pop(&pq).(*Item)
			topk = append(topk, item.value.(E))
		}
		swap := func(a, b int) bool {
			return getPriority(topk[a]) < getPriority(topk[b])
		}
		sort.Slice(topk, swap)
	} else if sortOrder == 2 {
		pq := make(PQDesc, 0)
		heap.Init(&pq)
		for _, item := range items {
			heap.Push(&pq, &Item{value: item, priority: getPriority(item)})
			for pq.Len() > K {
				heap.Pop(&pq)
			}
		}
		for pq.Len() > 0 {
			item := heap.Pop(&pq).(*Item)
			topk = append(topk, item.value.(E))
		}
		swap := func(a, b int) bool {
			return getPriority(topk[a]) > getPriority(topk[b])
		}
		sort.Slice(topk, swap)
	}
	return topk
}

type Item struct {
	value    any     // The value of the item; arbitrary.
	priority float64 // The priority of the item in the queue.
	index    int     // The index of the item in the heap.
}

type PQAsc []*Item

func (pq PQAsc) Len() int { return len(pq) }

// > 表示大的在队列前面
func (pq PQAsc) Less(i, j int) bool {
	return pq[i].priority > pq[j].priority
}

func (pq PQAsc) Swap(i, j int) {
	pq[i], pq[j] = pq[j], pq[i]
	pq[i].index = i
	pq[j].index = j
}

func (pq *PQAsc) Push(x interface{}) {
	n := len(*pq)
	item := x.(*Item)
	item.index = n
	*pq = append(*pq, item)
}

func (pq *PQAsc) Pop() interface{} {
	old := *pq
	n := len(old)
	item := old[n-1]
	item.index = -1 // for safety
	*pq = old[0 : n-1]
	return item
}

func (pq *PQAsc) update(item *Item, value string, priority float64) {
	item.value = value
	item.priority = priority
	heap.Fix(pq, item.index)
}

type PQDesc []*Item

func (pq PQDesc) Len() int { return len(pq) }

// < 表示小的在队列前面,即被pop的时候先被pop出来
func (pq PQDesc) Less(i, j int) bool {
	return pq[i].priority < pq[j].priority
}

func (pq PQDesc) Swap(i, j int) {
	pq[i], pq[j] = pq[j], pq[i]
	pq[i].index = i
	pq[j].index = j
}

func (pq *PQDesc) Push(x interface{}) {
	n := len(*pq)
	item := x.(*Item)
	item.index = n
	*pq = append(*pq, item)
}

func (pq *PQDesc) Pop() interface{} {
	old := *pq
	n := len(old)
	item := old[n-1]
	item.index = -1 // for safety
	*pq = old[0 : n-1]
	return item
}

func (pq *PQDesc) update(item *Item, value string, priority float64) {
	item.value = value
	item.priority = priority
	heap.Fix(pq, item.index)
}
