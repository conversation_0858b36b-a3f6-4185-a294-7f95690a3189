package utility

import "testing"

func TestRandCode(t *testing.T) {
	type args struct {
		charLen   int
		digitsLen int
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "TestRandCode",
			args: args{
				charLen:   2,
				digitsLen: 8,
			},
			want: "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := RandCode(tt.args.charLen, tt.args.digitsLen); got != tt.want {
				t.<PERSON>rf("RandCode() = %v, want %v", got, tt.want)
			}
		})
	}
}
