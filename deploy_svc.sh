#!/bin/bash

# 部署单个微服务到测试服 bash deploy_svc.sh app/user-account-svc/

SHELL_NAME=$0
SVC_PATH=$1
ROOT_DIR=$(pwd)
NAME=`basename $1`

SVC_PATH=$ROOT_DIR/$SVC_PATH
echo $SVC_PATH
cd $SVC_PATH

GOOS=linux GOARCH=amd64 go build -o ${NAME}.latest
chmod a+x ${NAME}.latest
scp ${NAME}.latest halal-dev:/home/<USER>/supervisor/${NAME%-svc}/

ssh halal-dev <<EOF
cd /home/<USER>/supervisor/${NAME%-svc}
test -f ${NAME}.latest && mv ${NAME}.latest ${NAME}
sudo supervisorctl restart ${NAME}
EOF