#!/bin/bash

#
# 制作pb，制作flutter_proto_lib
#

# 安装 Dart 插件（用于生成 Dart 代码）
# 安装dart sdk
# dart pub global activate protoc_plugin
# export PATH="$PATH":"$HOME/.pub-cache/bin":"$HOME/workspace4nu/flutter/bin"

ROOT_DIR=$(pwd)

function make_imported_pb() {
  SVC_PATH=$1
  NAME=$2
  DST_PATH=$3
  test -d $DST_PATH || mkdir -p $DST_PATH
    # 查找所有 proto 文件
    PROTO_FILES=$(find $SVC_PATH/manifest/protobuf -name "*.proto")

    # 为每个 proto 文件生成 pb 文件导入apisix
    for PROTO in $PROTO_FILES; do
#      echo "Processing proto file: $PROTO"
      # 去掉 manifest/protobuf前的字符串
      DST_FILE=${PROTO#*manifest/protobuf/}
      # 替换后缀 .proto → .pb
      DST_FILE=${NAME}/${DST_FILE%.proto}.pb
      # 把 / 换成 __
      DST_FILE=${DST_FILE//\//__}

      protoc --include_imports \
        --proto_path=/usr/local/include \
        --proto_path=$SVC_PATH/manifest/protobuf  \
        --descriptor_set_out=$DST_PATH/$DST_FILE \
        $PROTO

      SERVICE=$(grpcurl -protoset $DST_PATH/$DST_FILE list)
      if [[ $SERVICE == \(N* ]]; then
        # 删掉无服务的pb文件
        rm $DST_PATH/$DST_FILE
      else
        PBID="${NAME%-svc}.${SERVICE}.pb"
        mv $DST_PATH/$DST_FILE $DST_PATH/$PBID
      fi
    done
}


function copy_proto() {
  SVC_PATH=$1
  NAME=$2
  DST_PATH=$3
  test -d $DST_PATH || mkdir -p $DST_PATH
    # 查找所有 proto 文件
    PROTO_FILES=$(find $SVC_PATH/manifest/protobuf -name "*.proto")

    # 为每个 proto 文件生成 pb 文件导入apisix
    for PROTO in $PROTO_FILES; do
      # 去掉 manifest/protobuf前的字符串
      DST_FILE=${PROTO#*manifest/protobuf/}
      DST_FILE_PATH=$DST_PATH/${NAME%-svc}/$DST_FILE
      mkdir -p $(dirname ${DST_FILE_PATH})
      cp $PROTO $DST_FILE_PATH
#      echo "copy to $DST_FILE_PATH"
    done
}


function echo_pubspec_yaml() {
  GIT_COMMIT_HASH=$(git rev-parse HEAD)

  current_year=$(date +%Y)
  version_major=$((current_year - 2000))

  # 获取当前日期
  DATE=$(date +%Y%m%d)

  # 获取今年的第几周（01-53）
  WEEK=$(date +%V)

  # 获取今年的第几天（001-366）
  DAY_OF_YEAR=$(date +%j | sed 's/^0*//')  # 去除前导0

  # 写入 pubspec.yaml（你也可以保留其他字段，这里仅为演示）
  cat <<EOF
name: flutter_proto_lib
description: Dart Protobuf SDK for Flutter apps. git commit $GIT_COMMIT_HASH
version: 0.${version_major}.${DAY_OF_YEAR}
publish_to: none
environment:
  sdk: '>=2.17.0 <4.0.0'

dependencies:
  protobuf: ^4.1.0
EOF
}


function gen_flutter_lib() {
  SVC_PATH=$1
  NAME=$2
  FLUTTER_PROTO_LIB_DIR=$3
  DST_PATH=$3/lib/${NAME}
  test -d $DST_PATH || mkdir -p $DST_PATH
    # 查找所有 proto 文件
    PROTO_FILES=$(find $SVC_PATH/manifest/protobuf -name "*.proto")

    for PROTO in $PROTO_FILES; do
      protoc --proto_path=$SVC_PATH/manifest/protobuf  \
        --proto_path=/usr/local/include \
        --dart_out=$DST_PATH \
        $PROTO
    done

    cd $FLUTTER_PROTO_LIB_DIR && find lib/${NAME} -name "*.dart" | sort | sed 's|lib/|export '\''|g' | sed "s|\$|';|g" > lib/${NAME}.dart
}

OUTDIR=$ROOT_DIR/flutter_proto_lib
test -d $OUTDIR || mkdir $OUTDIR
for NAME in `ls app`; do
  # 微服务目录
  SVC_PATH=$ROOT_DIR/app/$NAME

  make_imported_pb $SVC_PATH $NAME ${OUTDIR}/pb
  gen_flutter_lib $SVC_PATH ${NAME%-svc} ${OUTDIR}
  copy_proto $SVC_PATH $NAME ${OUTDIR}/proto
done

cd $ROOT_DIR && echo_pubspec_yaml > ${OUTDIR}/pubspec.yaml