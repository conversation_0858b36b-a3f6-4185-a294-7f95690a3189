# HalalPlus 微服务平台

HalalPlus 是基于 GoFrame、gRPC、APISIX 等主流云原生技术栈打造的现代化微服务基础平台，支持高并发、高可用、灵活扩展，面向金融、支付等对数据一致性与安全性要求极高的业务场景。

## 快速连接

* 产品原型 https://modao.cc/proto/MqFYhwsPsz0b2ral44GhIp/sharing?view_mode=read_only&screen=rbpUqE7f6u96PlPbH
* 第一期产品原型 https://dkhtg1.axshare.com/?g=14&id=pone0n&p=%E9%BB%98%E8%AE%A4%E9%A1%B5
* 技术文档 https://docs.google.com/document/d/16NythKlSJRUduCKNBFUCgRmkoxv7LbhqGyq9bMVurKo/edit?usp=sharing

## quick start

1. 在每个微服务下定义接口`manifest/protobuf`
2. 每次修改proto后，必须在根目录运行`sh gf-gen-pb.sh`
3. 新增逻辑 internal/logic/xxx/xxx.go, 执行命令更新service接口 `gf gen service`
4. 使用`grpcurl`测试接口：grpcurl -plaintext -d '{"account":"testuser","password":"123456"}' 127.0.0.1:9200 user.v1.UserService/SignIn
5. apisix网关添加proto和配置接口
6. 数据库更改记录在微服务各自的manifest/sql

## 微服务

### 1. 用户服务（user-account-svc）

负责：注册、登录、实名认证、用户基础信息、绑卡、积分、KYC、消息、账户管理

**典型接口：**
- 注册/登录/找回密码
- KYC 认证
- 账户信息/积分/余额查询
- 绑卡/解绑
- 修改个人资料、头像
- 消息/通知列表

---

### 2. 支付服务（payment-svc）

负责：扫码、收付款、转账、账单流水、风控、支付结果回调、交易记录、服务费结算  
支付服务关注资金流、交易、转账、账单、风控、收款/付款，涉及金融合规与资金安全。

**典型接口：**
- 发起/扫码支付
- 订单收款/付款
- 用户/卡/USDT 转账
- 账单流水/历史查询
- 风控校验（可内嵌或独立调用 risk-svc）
- 服务费/手续费明细

---

### 3. 理财&慈善服务（wealth-charity-svc）

负责：理财产品管理、申购赎回、慈善捐赠、扎卡特计算与缴纳、捐赠记录

**典型接口：**
- 查询理财产品
- 申购/赎回理财
- 发起慈善捐赠/记录查询
- 扎卡特天课计算/缴纳
- 捐赠证明下载

---

### 4. 商城服务（mall-svc）

负责：商品、订单、优惠券、商家、购物、地址管理、活动

**典型接口：**
- 商品/活动列表
- 下单、支付、订单管理
- 优惠券发放/领取/使用
- 商家与店铺管理
- 用户地址增删改查

---

### 5. 宗教&内容服务（islamic-content-svc）

负责：古兰经、祷告、礼拜、朝觐、资讯、头条、专题、视频、日历

**典型接口：**
- 古兰经章节/音频/注释
- 祷告/礼拜时间与提醒
- 朝觐相关信息/证书
- 资讯/专题/视频内容
- 日历/提醒/推送

---

### 6. 文件服务（file-storage-svc）

负责：文件/图片/音频/证书/视频上传与存储，支持 OSS/MinIO 等

**典型接口：**
- 上传文件（支持分片/断点续传）
- 获取/下载文件
- 生成访问链接（带鉴权/时效）
- 文件预览与权限校验

---

### 7. 消息推送服务（notify-svc）

负责：短信、邮件、通知推送，队列管理，模板渲染，异步发送与重试

**典型接口：**
- 发送短信/邮件（注册、登录、验证码、通知）
- 发送状态查询/重试
- 模板内容渲染与管理
- 发送日志与回执

**实现建议：**
- 所有业务服务需异步调用 notify-svc，通过 MQ 触发，保证高可用和抗压。

---


```mermaid
graph TD
    A[API 网关 APISIX] -- HTTP/gRPC --> UA[user-account-svc]
    A -- HTTP/gRPC --> P[payment-svc]
    A -- HTTP/gRPC --> W[wealth-charity-svc]
    A -- HTTP/gRPC --> M[mall-svc]
    A -- HTTP/gRPC --> IC[islamic-content-svc]
    UA -- 调用 --> N[notify-svc]
    UA -- 调用 --> F[file-storage-svc]
    P -- 调用 --> N
    W -- 调用 --> N
    M -- 调用 --> N
    IC -- 调用 --> F
```


## 协作与通用建议

- **API 网关统一对外接口**：推荐 APISIX，兼容 HTTP/gRPC/gRPC-Web。
- **微服务间通信**：优先 gRPC（数据结构清晰、高效），也可 REST。
- **鉴权与安全**：JWT 统一鉴权，敏感服务（支付/账户/文件）建议细粒度权限与审计。
- **异步与可扩展性**：notify-svc、部分账务建议用 MQ 解耦。
- **运维监控**：Prometheus/Grafana 监控各服务，OpenTelemetry/Jaeger 跟踪全链路。
- **分布式事务**：重要转账、充值等流程建议结合 DTM/事务补偿。
- 
## 技术架构

- **GoFrame**：高性能 Go 语言微服务开发框架
- **gRPC & Protocol Buffers**：高效、强类型的微服务间通信协议与数据结构
- **DTM**：分布式事务管理，支持 TCC、SAGA、XA、消息事务等
- **APISIX + etcd**：云原生 API 网关，统一 HTTP/gRPC/gRPC-Web 入出口
- **Consul**：服务注册与发现、配置中心
- **AWS SQS**：分布式消息队列，服务解耦与异步事件处理
- **MySQL**：核心数据持久化
- **Redis**：高性能缓存、分布式锁、会话管理
- **Prometheus + Grafana**：监控告警与数据可视化
- **OpenTelemetry / Jaeger**：分布式链路追踪

## 项目特性

- 完全微服务化、解耦、可弹性扩容
- 支持 gRPC、HTTP、gRPC-Web、WebSocket 等多种协议
- 金融级分布式事务一致性保障
- 完善的服务发现与健康检查
- 丰富的网关治理（认证、限流、熔断、灰度等）
- 全链路监控和可观测性
- 适配公有云/私有云/混合云部署

## 架构图

```mermaid
graph TD
  subgraph Gateway
    A[APISIX]
  end
  subgraph Service
    B[GoFrame微服务gRPC+DTM]
    C[MySQL]
    D[Redis]
    E[AWS SQS]
  end
  subgraph Infra
    F[Consul]
    G[etcd]
  end
  subgraph Observability
    H[Prometheus]
    I[Grafana]
    J[Jaeger]
  end

  A -- http/grpc/grpc-web --> B
  B -- 数据库 --> C
  B -- 缓存/锁 --> D
  B -- 消息队列 --> E
  B -- 注册/发现 --> F
  A -- 配置中心/发现 --> G
  H -- 指标 --> B
  I -- 可视化 --> H
  J -- 链路追踪 --> B
```