package otp

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	v1 "halalplus/app/notify-svc/api/otp/v1"

	"github.com/gogf/gf/contrib/rpc/grpcx/v2"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
)

type Controller struct {
	v1.UnimplementedOtpServiceServer
}

func Register(s *grpcx.GrpcServer) {
	v1.RegisterOtpServiceServer(s.Server, &Controller{})
}

// SendSms 发送短信验证码
func (*Controller) SendSms(ctx context.Context, req *v1.SendSmsReq) (res *v1.SendSmsRes, err error) {
	// Extract metadata from the incoming context
	// This includes any values set by the client
	m := grpcx.Ctx.IncomingMap(ctx)

	// Log the incoming metadata for debugging
	// This shows what data we received from the client
	g.Log().Infof(ctx, `###incoming data: %v`, m.Map())

	res = new(v1.SendSmsRes)
	res.Code = 200
	res.Msg = "success"
	return res, nil
}

func (*Controller) Verify(ctx context.Context, req *v1.VerifyReq) (res *v1.VerifyRes, err error) {
	return nil, gerror.NewCode(gcode.CodeNotImplemented)
}
