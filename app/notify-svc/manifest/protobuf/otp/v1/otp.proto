syntax = "proto3";

package otp.v1;
option go_package = "halalplus/app/notify-svc/api/otp/v1;otpv1";
import "common/front_info.proto";
import "common/base.proto";
import "common/phone_info.proto";

// OTP的类型
enum OtpType {
  UNKNOWN = 0;              // 必须有0值，建议用于未知
  REGISTER = 1;             // 注册
  LOGIN = 2;                // 登录
  BIND = 3;                 // 绑定
  LOGIN_PASSWORD_UPDATE = 4; // 登录密码修改
  PAY_PASSWORD_UPDATE = 5;   // 支付密码修改
  ADD_BANK_CARD = 6;         // 添加银行卡
  ADD_CRYPTO_ADDRESS = 7;    // 添加虚拟币地址
  OTHER = 9999;              // 其他
}

// 短信OTP
message SendSmsReq {
  common.PhoneInfo phone_info = 1;
  OtpType otp_type = 2;
  common.FrontInfo front_info = 3;
}

message SendSmsRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
}

message VerifyReq {
  string otp = 1;
  OtpType otp_type = 2;
  common.FrontInfo front_info = 3;
}

message VerifyRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
}

service OtpService {
  // 检查OTP
  rpc Verify(VerifyReq) returns (VerifyRes);

  // 发送短信验证码
  // POST /api/notify/otp/v1/OtpService/SendSms
  rpc SendSms(SendSmsReq) returns (SendSmsRes);
}