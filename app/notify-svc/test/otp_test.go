package test

import (
	"context"
	"github.com/gogf/gf/contrib/rpc/grpcx/v2"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/test/gtest"
	otpv1 "halalplus/app/notify-svc/api/otp/v1"
	"halalplus/utility/gf-registry-consul"
	"testing"
)

func Test_SendSms(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		registry, err := consul.New()
		if err != nil {
			g.Log().Fatal(context.Background(), err)
		}

		grpcx.Resolver.Register(registry)
		ctx := gctx.GetInitCtx()
		conn, err := grpcx.Client.NewGrpcClientConn("notify-svc")
		if err != nil {
			g.Log().Error(ctx, err)
		}
		otpService := otpv1.NewOtpServiceClient(conn)
		for i := 1; i <= 10; i++ {
			// Create a new context with metadata
			// The metadata includes:
			// 1. User ID
			// 2. User name
			// These values will be passed to the server
			ctxClient := grpcx.Ctx.NewOutgoing(gctx.New(), g.Map{
				"UserId":   "1000",
				"UserName": "john",
				"Counter":  i,
			})
			rst, err := otpService.SendSms(ctxClient, &otpv1.SendSmsReq{})
			if err != nil {
				g.Log().Fatalf(ctx, `create user failed: %+v`, err)
			} else {
				g.Log().Info(ctx, `send sms successfully`, rst)
			}
		}
	})
}
