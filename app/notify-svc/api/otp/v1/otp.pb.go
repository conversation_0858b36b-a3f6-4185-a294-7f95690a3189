// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: otp/v1/otp.proto

package otpv1

import (
	common "halalplus/api/common"
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// OTP的类型
type OtpType int32

const (
	OtpType_UNKNOWN               OtpType = 0    // 必须有0值，建议用于未知
	OtpType_REGISTER              OtpType = 1    // 注册
	OtpType_LOGIN                 OtpType = 2    // 登录
	OtpType_BIND                  OtpType = 3    // 绑定
	OtpType_LOGIN_PASSWORD_UPDATE OtpType = 4    // 登录密码修改
	OtpType_PAY_PASSWORD_UPDATE   OtpType = 5    // 支付密码修改
	OtpType_ADD_BANK_CARD         OtpType = 6    // 添加银行卡
	OtpType_ADD_CRYPTO_ADDRESS    OtpType = 7    // 添加虚拟币地址
	OtpType_OTHER                 OtpType = 9999 // 其他
)

// Enum value maps for OtpType.
var (
	OtpType_name = map[int32]string{
		0:    "UNKNOWN",
		1:    "REGISTER",
		2:    "LOGIN",
		3:    "BIND",
		4:    "LOGIN_PASSWORD_UPDATE",
		5:    "PAY_PASSWORD_UPDATE",
		6:    "ADD_BANK_CARD",
		7:    "ADD_CRYPTO_ADDRESS",
		9999: "OTHER",
	}
	OtpType_value = map[string]int32{
		"UNKNOWN":               0,
		"REGISTER":              1,
		"LOGIN":                 2,
		"BIND":                  3,
		"LOGIN_PASSWORD_UPDATE": 4,
		"PAY_PASSWORD_UPDATE":   5,
		"ADD_BANK_CARD":         6,
		"ADD_CRYPTO_ADDRESS":    7,
		"OTHER":                 9999,
	}
)

func (x OtpType) Enum() *OtpType {
	p := new(OtpType)
	*p = x
	return p
}

func (x OtpType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OtpType) Descriptor() protoreflect.EnumDescriptor {
	return file_otp_v1_otp_proto_enumTypes[0].Descriptor()
}

func (OtpType) Type() protoreflect.EnumType {
	return &file_otp_v1_otp_proto_enumTypes[0]
}

func (x OtpType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OtpType.Descriptor instead.
func (OtpType) EnumDescriptor() ([]byte, []int) {
	return file_otp_v1_otp_proto_rawDescGZIP(), []int{0}
}

// 短信OTP
type SendSmsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PhoneInfo *common.PhoneInfo `protobuf:"bytes,1,opt,name=phone_info,json=phoneInfo,proto3" json:"phone_info,omitempty"`
	OtpType   OtpType           `protobuf:"varint,2,opt,name=otp_type,json=otpType,proto3,enum=otp.v1.OtpType" json:"otp_type,omitempty"`
	FrontInfo *common.FrontInfo `protobuf:"bytes,3,opt,name=front_info,json=frontInfo,proto3" json:"front_info,omitempty"`
}

func (x *SendSmsReq) Reset() {
	*x = SendSmsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_otp_v1_otp_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendSmsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendSmsReq) ProtoMessage() {}

func (x *SendSmsReq) ProtoReflect() protoreflect.Message {
	mi := &file_otp_v1_otp_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendSmsReq.ProtoReflect.Descriptor instead.
func (*SendSmsReq) Descriptor() ([]byte, []int) {
	return file_otp_v1_otp_proto_rawDescGZIP(), []int{0}
}

func (x *SendSmsReq) GetPhoneInfo() *common.PhoneInfo {
	if x != nil {
		return x.PhoneInfo
	}
	return nil
}

func (x *SendSmsReq) GetOtpType() OtpType {
	if x != nil {
		return x.OtpType
	}
	return OtpType_UNKNOWN
}

func (x *SendSmsReq) GetFrontInfo() *common.FrontInfo {
	if x != nil {
		return x.FrontInfo
	}
	return nil
}

type SendSmsRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *SendSmsRes) Reset() {
	*x = SendSmsRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_otp_v1_otp_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendSmsRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendSmsRes) ProtoMessage() {}

func (x *SendSmsRes) ProtoReflect() protoreflect.Message {
	mi := &file_otp_v1_otp_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendSmsRes.ProtoReflect.Descriptor instead.
func (*SendSmsRes) Descriptor() ([]byte, []int) {
	return file_otp_v1_otp_proto_rawDescGZIP(), []int{1}
}

func (x *SendSmsRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SendSmsRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SendSmsRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type VerifyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Otp       string            `protobuf:"bytes,1,opt,name=otp,proto3" json:"otp,omitempty"`
	OtpType   OtpType           `protobuf:"varint,2,opt,name=otp_type,json=otpType,proto3,enum=otp.v1.OtpType" json:"otp_type,omitempty"`
	FrontInfo *common.FrontInfo `protobuf:"bytes,3,opt,name=front_info,json=frontInfo,proto3" json:"front_info,omitempty"`
}

func (x *VerifyReq) Reset() {
	*x = VerifyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_otp_v1_otp_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyReq) ProtoMessage() {}

func (x *VerifyReq) ProtoReflect() protoreflect.Message {
	mi := &file_otp_v1_otp_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyReq.ProtoReflect.Descriptor instead.
func (*VerifyReq) Descriptor() ([]byte, []int) {
	return file_otp_v1_otp_proto_rawDescGZIP(), []int{2}
}

func (x *VerifyReq) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

func (x *VerifyReq) GetOtpType() OtpType {
	if x != nil {
		return x.OtpType
	}
	return OtpType_UNKNOWN
}

func (x *VerifyReq) GetFrontInfo() *common.FrontInfo {
	if x != nil {
		return x.FrontInfo
	}
	return nil
}

type VerifyRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *VerifyRes) Reset() {
	*x = VerifyRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_otp_v1_otp_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyRes) ProtoMessage() {}

func (x *VerifyRes) ProtoReflect() protoreflect.Message {
	mi := &file_otp_v1_otp_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyRes.ProtoReflect.Descriptor instead.
func (*VerifyRes) Descriptor() ([]byte, []int) {
	return file_otp_v1_otp_proto_rawDescGZIP(), []int{3}
}

func (x *VerifyRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *VerifyRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *VerifyRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_otp_v1_otp_proto protoreflect.FileDescriptor

var file_otp_v1_otp_proto_rawDesc = []byte{
	0x0a, 0x10, 0x6f, 0x74, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x74, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x06, 0x6f, 0x74, 0x70, 0x2e, 0x76, 0x31, 0x1a, 0x17, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x61, 0x73, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x9c, 0x01, 0x0a, 0x0a, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x6d, 0x73, 0x52, 0x65, 0x71, 0x12, 0x30,
	0x0a, 0x0a, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x2a, 0x0a, 0x08, 0x6f, 0x74, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x6f, 0x74, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x74, 0x70, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x07, 0x6f, 0x74, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x30, 0x0a, 0x0a,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x72, 0x6f, 0x6e, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x09, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x57,
	0x0a, 0x0a, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d,
	0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x7b, 0x0a, 0x09, 0x56, 0x65, 0x72, 0x69, 0x66,
	0x79, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x6f, 0x74, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6f, 0x74, 0x70, 0x12, 0x2a, 0x0a, 0x08, 0x6f, 0x74, 0x70, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x6f, 0x74, 0x70, 0x2e, 0x76,
	0x31, 0x2e, 0x4f, 0x74, 0x70, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x6f, 0x74, 0x70, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x30, 0x0a, 0x0a, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x46, 0x72, 0x6f, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x22, 0x56, 0x0a, 0x09, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x52, 0x65,
	0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2a, 0xa4, 0x01, 0x0a,
	0x07, 0x4f, 0x74, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e,
	0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x45,
	0x52, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x10, 0x02, 0x12, 0x08,
	0x0a, 0x04, 0x42, 0x49, 0x4e, 0x44, 0x10, 0x03, 0x12, 0x19, 0x0a, 0x15, 0x4c, 0x4f, 0x47, 0x49,
	0x4e, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x57, 0x4f, 0x52, 0x44, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54,
	0x45, 0x10, 0x04, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x41, 0x59, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x57,
	0x4f, 0x52, 0x44, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x10, 0x05, 0x12, 0x11, 0x0a, 0x0d,
	0x41, 0x44, 0x44, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x06, 0x12,
	0x16, 0x0a, 0x12, 0x41, 0x44, 0x44, 0x5f, 0x43, 0x52, 0x59, 0x50, 0x54, 0x4f, 0x5f, 0x41, 0x44,
	0x44, 0x52, 0x45, 0x53, 0x53, 0x10, 0x07, 0x12, 0x0a, 0x0a, 0x05, 0x4f, 0x54, 0x48, 0x45, 0x52,
	0x10, 0x8f, 0x4e, 0x32, 0x6f, 0x0a, 0x0a, 0x4f, 0x74, 0x70, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x2e, 0x0a, 0x06, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x12, 0x11, 0x2e, 0x6f, 0x74,
	0x70, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x11,
	0x2e, 0x6f, 0x74, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x52, 0x65,
	0x73, 0x12, 0x31, 0x0a, 0x07, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x6d, 0x73, 0x12, 0x12, 0x2e, 0x6f,
	0x74, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x6d, 0x73, 0x52, 0x65, 0x71,
	0x1a, 0x12, 0x2e, 0x6f, 0x74, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x6d,
	0x73, 0x52, 0x65, 0x73, 0x42, 0x2b, 0x5a, 0x29, 0x68, 0x61, 0x6c, 0x61, 0x6c, 0x70, 0x6c, 0x75,
	0x73, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2d, 0x73, 0x76, 0x63,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x74, 0x70, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x74, 0x70, 0x76,
	0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_otp_v1_otp_proto_rawDescOnce sync.Once
	file_otp_v1_otp_proto_rawDescData = file_otp_v1_otp_proto_rawDesc
)

func file_otp_v1_otp_proto_rawDescGZIP() []byte {
	file_otp_v1_otp_proto_rawDescOnce.Do(func() {
		file_otp_v1_otp_proto_rawDescData = protoimpl.X.CompressGZIP(file_otp_v1_otp_proto_rawDescData)
	})
	return file_otp_v1_otp_proto_rawDescData
}

var file_otp_v1_otp_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_otp_v1_otp_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_otp_v1_otp_proto_goTypes = []interface{}{
	(OtpType)(0),             // 0: otp.v1.OtpType
	(*SendSmsReq)(nil),       // 1: otp.v1.SendSmsReq
	(*SendSmsRes)(nil),       // 2: otp.v1.SendSmsRes
	(*VerifyReq)(nil),        // 3: otp.v1.VerifyReq
	(*VerifyRes)(nil),        // 4: otp.v1.VerifyRes
	(*common.PhoneInfo)(nil), // 5: common.PhoneInfo
	(*common.FrontInfo)(nil), // 6: common.FrontInfo
	(*common.Error)(nil),     // 7: common.Error
}
var file_otp_v1_otp_proto_depIdxs = []int32{
	5, // 0: otp.v1.SendSmsReq.phone_info:type_name -> common.PhoneInfo
	0, // 1: otp.v1.SendSmsReq.otp_type:type_name -> otp.v1.OtpType
	6, // 2: otp.v1.SendSmsReq.front_info:type_name -> common.FrontInfo
	7, // 3: otp.v1.SendSmsRes.error:type_name -> common.Error
	0, // 4: otp.v1.VerifyReq.otp_type:type_name -> otp.v1.OtpType
	6, // 5: otp.v1.VerifyReq.front_info:type_name -> common.FrontInfo
	7, // 6: otp.v1.VerifyRes.error:type_name -> common.Error
	3, // 7: otp.v1.OtpService.Verify:input_type -> otp.v1.VerifyReq
	1, // 8: otp.v1.OtpService.SendSms:input_type -> otp.v1.SendSmsReq
	4, // 9: otp.v1.OtpService.Verify:output_type -> otp.v1.VerifyRes
	2, // 10: otp.v1.OtpService.SendSms:output_type -> otp.v1.SendSmsRes
	9, // [9:11] is the sub-list for method output_type
	7, // [7:9] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_otp_v1_otp_proto_init() }
func file_otp_v1_otp_proto_init() {
	if File_otp_v1_otp_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_otp_v1_otp_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendSmsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_otp_v1_otp_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendSmsRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_otp_v1_otp_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_otp_v1_otp_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_otp_v1_otp_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_otp_v1_otp_proto_goTypes,
		DependencyIndexes: file_otp_v1_otp_proto_depIdxs,
		EnumInfos:         file_otp_v1_otp_proto_enumTypes,
		MessageInfos:      file_otp_v1_otp_proto_msgTypes,
	}.Build()
	File_otp_v1_otp_proto = out.File
	file_otp_v1_otp_proto_rawDesc = nil
	file_otp_v1_otp_proto_goTypes = nil
	file_otp_v1_otp_proto_depIdxs = nil
}
