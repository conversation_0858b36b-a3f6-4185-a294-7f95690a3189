// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package otpv1

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// OtpServiceClient is the client API for OtpService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OtpServiceClient interface {
	// 检查OTP
	Verify(ctx context.Context, in *VerifyReq, opts ...grpc.CallOption) (*VerifyRes, error)
	// 发送短信验证码
	// POST /api/notify/otp/v1/OtpService/SendSms
	SendSms(ctx context.Context, in *SendSmsReq, opts ...grpc.CallOption) (*SendSmsRes, error)
}

type otpServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewOtpServiceClient(cc grpc.ClientConnInterface) OtpServiceClient {
	return &otpServiceClient{cc}
}

func (c *otpServiceClient) Verify(ctx context.Context, in *VerifyReq, opts ...grpc.CallOption) (*VerifyRes, error) {
	out := new(VerifyRes)
	err := c.cc.Invoke(ctx, "/otp.v1.OtpService/Verify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *otpServiceClient) SendSms(ctx context.Context, in *SendSmsReq, opts ...grpc.CallOption) (*SendSmsRes, error) {
	out := new(SendSmsRes)
	err := c.cc.Invoke(ctx, "/otp.v1.OtpService/SendSms", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OtpServiceServer is the server API for OtpService service.
// All implementations must embed UnimplementedOtpServiceServer
// for forward compatibility
type OtpServiceServer interface {
	// 检查OTP
	Verify(context.Context, *VerifyReq) (*VerifyRes, error)
	// 发送短信验证码
	// POST /api/notify/otp/v1/OtpService/SendSms
	SendSms(context.Context, *SendSmsReq) (*SendSmsRes, error)
	mustEmbedUnimplementedOtpServiceServer()
}

// UnimplementedOtpServiceServer must be embedded to have forward compatible implementations.
type UnimplementedOtpServiceServer struct {
}

func (UnimplementedOtpServiceServer) Verify(context.Context, *VerifyReq) (*VerifyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Verify not implemented")
}
func (UnimplementedOtpServiceServer) SendSms(context.Context, *SendSmsReq) (*SendSmsRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendSms not implemented")
}
func (UnimplementedOtpServiceServer) mustEmbedUnimplementedOtpServiceServer() {}

// UnsafeOtpServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OtpServiceServer will
// result in compilation errors.
type UnsafeOtpServiceServer interface {
	mustEmbedUnimplementedOtpServiceServer()
}

func RegisterOtpServiceServer(s *grpc.Server, srv OtpServiceServer) {
	s.RegisterService(&_OtpService_serviceDesc, srv)
}

func _OtpService_Verify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OtpServiceServer).Verify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/otp.v1.OtpService/Verify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OtpServiceServer).Verify(ctx, req.(*VerifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OtpService_SendSms_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendSmsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OtpServiceServer).SendSms(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/otp.v1.OtpService/SendSms",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OtpServiceServer).SendSms(ctx, req.(*SendSmsReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _OtpService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "otp.v1.OtpService",
	HandlerType: (*OtpServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Verify",
			Handler:    _OtpService_Verify_Handler,
		},
		{
			MethodName: "SendSms",
			Handler:    _OtpService_SendSms_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "otp/v1/otp.proto",
}
