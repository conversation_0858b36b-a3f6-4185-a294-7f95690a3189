-- Banner广告管理表
CREATE TABLE `banner` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `language_id` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '语言ID: 0-中文, 1-英文, 2-印尼语',
  `title` varchar(255) NOT NULL DEFAULT '' COMMENT '广告标题',
  `description` text COMMENT '广告描述',
  `image_url` varchar(500) NOT NULL DEFAULT '' COMMENT '广告图片URL',
  `link_url` varchar(500) NOT NULL DEFAULT '' COMMENT '跳转链接URL',
  `sort_order` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '排序权重，数字越小越靠前',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态: 0-禁用, 1-启用',
  `start_time` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '开始时间戳(毫秒)',
  `end_time` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '结束时间戳(毫秒)',
  `admin_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建管理员ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_language_status_sort_order` (`language_id`, `status`, `sort_order`),
  KEY `idx_time_range` (`start_time`, `end_time`),
  KEY `idx_status_time` (`status`, `start_time`, `end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Banner广告表';

-- Banner统计表
CREATE TABLE `banner_stats` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `banner_id` int(11) unsigned NOT NULL COMMENT '广告ID',
  `user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID，0表示未登录用户',
  `device_id` varchar(100) NOT NULL DEFAULT '' COMMENT '设备唯一标识',
  `ip_address` varchar(45) NOT NULL DEFAULT '' COMMENT 'IP地址',
  `user_agent` varchar(500) NOT NULL DEFAULT '' COMMENT '用户代理信息',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  PRIMARY KEY (`id`),
  KEY `idx_banner_id` (`banner_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_banner_time` (`banner_id`, `created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Banner统计表';