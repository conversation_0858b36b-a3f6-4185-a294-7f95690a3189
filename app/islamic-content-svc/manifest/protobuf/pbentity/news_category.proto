// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message NewsCategory {
    uint32 Id         = 1;  //
    uint32 ParentId   = 2;  // 上级id，0表示顶级
    uint32 IsZh       = 3;  // 是否中文，0-否，1-是
    uint32 IsEn       = 4;  // 是否英文，0-否，1-是
    uint32 IsId       = 5;  // 是否印尼文，0-否，1-是
    uint32 Status     = 6;  // 状态，1启用，0关闭
    uint32 Sort       = 7;  // 排序，数字越小，排序越靠前
    uint32 AdminId    = 8;  // 分类负责人id
    string CoverImgs  = 9;  // 封面图
    string Remark     = 10; // 备注
    uint32 Creater    = 11; // 创建者id
    string CreateName = 12; // 创建者
    int64  CreateTime = 13; // 创建时间
    int64  UpdateTime = 14; // 修改时间
    int64  DeleteTime = 15; // 删除时间
}