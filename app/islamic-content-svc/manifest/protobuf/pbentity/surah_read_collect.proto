// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message SurahReadCollect {
    uint32 Id         = 1; //
    uint32 UserId     = 2; // 用户id
    uint32 AyahId     = 3; // ayah_id节id
    string SurahName  = 4; // 名称
    int64  CreateTime = 5; // 创建时间（注册时间）
    int64  UpdateTime = 6; // 更新时间，0代表创建后未更新
}