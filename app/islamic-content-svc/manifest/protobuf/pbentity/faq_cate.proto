// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";

import "google/protobuf/timestamp.proto";

message FaqCate {
    uint32                    Id            = 1; //
    int32                     IsOpen        = 2; // 状态 [ 1 启用 2 禁用]
    int32                     Sort          = 3; // 排序
    int32                     CateCount     = 4; // 分类下的文章总数
    google.protobuf.Timestamp CreatedAt     = 5; // 创建时间
    string                    CreateAccount = 6; // 创建者
    google.protobuf.Timestamp UpdatedAt     = 7; // 更新时间
    string                    UpdateAccount = 8; // 更新者
}