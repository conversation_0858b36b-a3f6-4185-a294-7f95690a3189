// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";

import "google/protobuf/timestamp.proto";

message BannerStats {
    uint64                    Id        = 1; // 主键ID                   
    uint32                    BannerId  = 2; // 广告ID                   
    uint64                    UserId    = 3; // 用户ID，0表示未登录用户  
    string                    DeviceId  = 4; // 设备唯一标识             
    string                    IpAddress = 5; // IP地址                   
    string                    UserAgent = 6; // 用户代理信息             
    google.protobuf.Timestamp CreatedAt = 7; // 操作时间                 
}