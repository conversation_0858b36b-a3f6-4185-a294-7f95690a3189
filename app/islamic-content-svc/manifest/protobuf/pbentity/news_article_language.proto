// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message NewsArticleLanguage {
    uint32 Id         = 1; //
    uint32 ArticleId  = 2; // 文章id
    uint32 LanguageId = 3; // 语言id,0-中文，1-英文，2-印尼语
    string Name       = 4; // 名称
    string Content    = 5; // 正文
    int64  CreateTime = 6; // 创建时间
    int64  UpdateTime = 7; // 修改时间
    int64  DeleteTime = 8; // 删除时间
}