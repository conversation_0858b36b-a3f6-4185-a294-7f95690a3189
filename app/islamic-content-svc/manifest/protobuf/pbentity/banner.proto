// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";

import "google/protobuf/timestamp.proto";

message Banner {
    uint32                    Id          = 1;  // 主键ID                            
    uint32                    LanguageId  = 2;  // 语言ID: 0-中文, 1-英文, 2-印尼语  
    string                    Title       = 3;  // 广告标题                          
    string                    Description = 4;  // 广告描述                          
    string                    ImageUrl    = 5;  // 广告图片URL                       
    string                    LinkUrl     = 6;  // 跳转链接URL                       
    uint32                    SortOrder   = 7;  // 排序权重，数字越小越靠前          
    uint32                    Status      = 8;  // 状态: 0-禁用, 1-启用              
    uint64                    StartTime   = 9;  // 开始时间戳(毫秒)                  
    uint64                    EndTime     = 10; // 结束时间戳(毫秒)                  
    uint32                    AdminId     = 11; // 创建管理员ID                      
    google.protobuf.Timestamp CreatedAt   = 12; // 创建时间                          
    google.protobuf.Timestamp UpdatedAt   = 13; // 更新时间                          
}