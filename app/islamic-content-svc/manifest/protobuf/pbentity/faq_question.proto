// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";

import "google/protobuf/timestamp.proto";

message FaqQuestion {
    uint32                    Id            = 1; //
    uint32                    FaqCateId     = 2; //
    int32                     IsOpen        = 3; // 状态 [ 1 启用 2 禁用]
    int32                     Sort          = 4; // 排序
    int32                     Views         = 5; // 浏览量
    google.protobuf.Timestamp CreatedAt     = 6; // 创建时间
    string                    CreateAccount = 7; // 创建者
    google.protobuf.Timestamp UpdatedAt     = 8; // 更新时间
    string                    UpdateAccount = 9; // 更新者
}