// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message NewsTopic {
    uint32 Id         = 1;  //
    uint32 Counts     = 2;  // 文章数量
    uint32 IsZh       = 3;  // 是否中文，0-否，1-是
    uint32 IsEn       = 4;  // 是否英文，0-否，1-是
    uint32 IsId       = 5;  // 是否印尼文，0-否，1-是
    uint32 Status     = 6;  // 是否显示，1启用，0关闭
    uint32 Sort       = 7;  // 排序，数字越小，排序越靠前
    uint32 AdminId    = 8;  // 分类负责人id
    string TopicImgs  = 9;  // 专题图片
    uint32 Creater    = 10; // 创建者id
    string CreateName = 11; // 创建者
    int64  CreateTime = 12; // 创建时间
    int64  UpdateTime = 13; // 修改时间
    int64  DeleteTime = 14; // 删除时间
}