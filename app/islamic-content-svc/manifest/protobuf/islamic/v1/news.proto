syntax = "proto3";

package islamic.v1;
option go_package = "halalplus/app/islamic-content-svc/api/islamic/v1;islamicv1";
import "pbentity/news_article.proto";
import "pbentity/news_article_language.proto";
import "pbentity/news_category.proto";
import "pbentity/news_category_language.proto";
import "pbentity/news_topic.proto";
import "pbentity/news_topic_language.proto";
import "common/front_info.proto";
import "common/base.proto";

message NewsCategoryListReq {
  uint32 language_id = 1;   // 语言id
  uint32 pid = 2;  //父类id
}



message CategoryInfo {
  uint32 id = 1;
  uint32 parent_id = 2;
  uint32 language_id = 3;
  string name = 4;
  string cover_imgs = 5;
}

message NewsCategoryListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  repeated CategoryInfo data = 4;
}


message NewsListByCateIdReq {
  uint32 cate_id = 1;   // 分类id
  uint32 language_id = 2;   // 语言id

}
message NewsListByCateIdRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  repeated ArticleInfo data = 4;

}



message NewsTopicListReq {
  uint32 language_id = 1;   // 语言id

}


message TopicInfo {
  uint32 topic_id = 1;               // 用户id
  uint32 language_id = 2;               // 用户id
  string name = 3;          // 账号
  string short_name = 4;          // 账号
  string topic_imgs  = 5;  // 专题图片


}

message NewsTopicListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  repeated TopicInfo data = 4; // 专题信息
}

message NewsListByTopicIdReq {
  string topic_id = 1;   // 话题id
  string language_id = 2;   // 语言id
}



message ArticleInfo {
  uint32 article_id = 1;
  uint32 language_id = 2;
  string name = 3;
  string content = 4;
  uint32 CategoryId  = 5;  // 分类id
  string CoverImgs   = 6;  // 专题图片
  string Author      = 7; // 创建人
  int64  PublishTime = 8; // 发布时间
}

message NewsListByTopicIdRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  repeated ArticleInfo data = 4;
}

service NewsService {
  // 新闻分类列表
rpc NewsCategoryList(NewsCategoryListReq) returns (NewsCategoryListRes);
  // 新闻列表通过分类ID
rpc NewsListByCateId(NewsListByCateIdReq) returns (NewsListByCateIdRes);
//新闻专题列表
rpc NewsTopicList(NewsTopicListReq) returns (NewsTopicListRes);
  // 新闻列表通过专题ID
rpc NewsListByTopicId(NewsListByTopicIdReq) returns (NewsListByTopicIdRes);
}
