syntax = "proto3";

package islamic.v1;
option go_package = "halalplus/app/islamic-content-svc/api/islamic/v1;islamicv1";

import "common/base.proto";


message FaqCateListReq {
  uint32 language_id = 1;   // 语言id
}

message FaqCateItem {
  uint32 id         = 1;
  int32  is_open    = 2;
  int32  sort       = 3;
  int32  cate_count = 4;
  string title      = 5;
  uint32  language_id = 6;
}

message FaqCateListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  repeated FaqCateItem data = 4; // 分类列表
}


message FaqListByCateIdReq {
  uint32 language_id = 1;   // 语言id
  uint32 cate_id = 2; // 分类id
  string keyword = 3; // 搜索关键字
}

message FaqListByCateIdRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  repeated FaqQuestionItem data = 4;
}


message FaqQuestionItem {
  uint32 id         = 1;
  int32  is_open    = 2;
  int32  sort       = 3;
  int32  views      = 4;
  string title      = 5;
  string desc       = 6;
  int32  language_id = 7;
  uint32 faq_cate_id = 8;
}

message FaqOneReq {
  uint32 language_id = 1;   // 语言id
  uint32 id = 2; // 分类id
}

message FaqOneRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  FaqQuestionOneItem data = 5;
}

message FaqQuestionOneItem{
  uint32 id         = 1;
  int32  is_open    = 2;
  int32  sort       = 3;
  int32  views      = 4;
  string title      = 5;
  string desc       = 6;
  int32  language_id = 7;
  uint32 faq_cate_id = 8;
  string faq_cate_title = 9;
}
service FaqService {
  // faq分类列表
  rpc FaqCategoryList(FaqCateListReq) returns (FaqCateListRes);
  // faq列表通过分类ID
  rpc FaqListByCateId(FaqListByCateIdReq) returns (FaqListByCateIdRes);
  // faq详情
  rpc FaqOne(FaqOneReq) returns (FaqOneRes);
}