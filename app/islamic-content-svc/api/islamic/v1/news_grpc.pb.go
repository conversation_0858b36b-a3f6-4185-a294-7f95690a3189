// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package islamicv1

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// NewsServiceClient is the client API for NewsService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NewsServiceClient interface {
	// 新闻分类列表
	NewsCategoryList(ctx context.Context, in *NewsCategoryListReq, opts ...grpc.CallOption) (*NewsCategoryListRes, error)
	// 新闻列表通过分类ID
	NewsListByCateId(ctx context.Context, in *NewsListByCateIdReq, opts ...grpc.CallOption) (*NewsListByCateIdRes, error)
	// 新闻专题列表
	NewsTopicList(ctx context.Context, in *NewsTopicListReq, opts ...grpc.CallOption) (*NewsTopicListRes, error)
	// 新闻列表通过专题ID
	NewsListByTopicId(ctx context.Context, in *NewsListByTopicIdReq, opts ...grpc.CallOption) (*NewsListByTopicIdRes, error)
}

type newsServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewNewsServiceClient(cc grpc.ClientConnInterface) NewsServiceClient {
	return &newsServiceClient{cc}
}

func (c *newsServiceClient) NewsCategoryList(ctx context.Context, in *NewsCategoryListReq, opts ...grpc.CallOption) (*NewsCategoryListRes, error) {
	out := new(NewsCategoryListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.NewsService/NewsCategoryList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newsServiceClient) NewsListByCateId(ctx context.Context, in *NewsListByCateIdReq, opts ...grpc.CallOption) (*NewsListByCateIdRes, error) {
	out := new(NewsListByCateIdRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.NewsService/NewsListByCateId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newsServiceClient) NewsTopicList(ctx context.Context, in *NewsTopicListReq, opts ...grpc.CallOption) (*NewsTopicListRes, error) {
	out := new(NewsTopicListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.NewsService/NewsTopicList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newsServiceClient) NewsListByTopicId(ctx context.Context, in *NewsListByTopicIdReq, opts ...grpc.CallOption) (*NewsListByTopicIdRes, error) {
	out := new(NewsListByTopicIdRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.NewsService/NewsListByTopicId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NewsServiceServer is the server API for NewsService service.
// All implementations must embed UnimplementedNewsServiceServer
// for forward compatibility
type NewsServiceServer interface {
	// 新闻分类列表
	NewsCategoryList(context.Context, *NewsCategoryListReq) (*NewsCategoryListRes, error)
	// 新闻列表通过分类ID
	NewsListByCateId(context.Context, *NewsListByCateIdReq) (*NewsListByCateIdRes, error)
	// 新闻专题列表
	NewsTopicList(context.Context, *NewsTopicListReq) (*NewsTopicListRes, error)
	// 新闻列表通过专题ID
	NewsListByTopicId(context.Context, *NewsListByTopicIdReq) (*NewsListByTopicIdRes, error)
	mustEmbedUnimplementedNewsServiceServer()
}

// UnimplementedNewsServiceServer must be embedded to have forward compatible implementations.
type UnimplementedNewsServiceServer struct {
}

func (UnimplementedNewsServiceServer) NewsCategoryList(context.Context, *NewsCategoryListReq) (*NewsCategoryListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewsCategoryList not implemented")
}
func (UnimplementedNewsServiceServer) NewsListByCateId(context.Context, *NewsListByCateIdReq) (*NewsListByCateIdRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewsListByCateId not implemented")
}
func (UnimplementedNewsServiceServer) NewsTopicList(context.Context, *NewsTopicListReq) (*NewsTopicListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewsTopicList not implemented")
}
func (UnimplementedNewsServiceServer) NewsListByTopicId(context.Context, *NewsListByTopicIdReq) (*NewsListByTopicIdRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewsListByTopicId not implemented")
}
func (UnimplementedNewsServiceServer) mustEmbedUnimplementedNewsServiceServer() {}

// UnsafeNewsServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NewsServiceServer will
// result in compilation errors.
type UnsafeNewsServiceServer interface {
	mustEmbedUnimplementedNewsServiceServer()
}

func RegisterNewsServiceServer(s *grpc.Server, srv NewsServiceServer) {
	s.RegisterService(&_NewsService_serviceDesc, srv)
}

func _NewsService_NewsCategoryList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewsCategoryListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewsServiceServer).NewsCategoryList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.NewsService/NewsCategoryList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewsServiceServer).NewsCategoryList(ctx, req.(*NewsCategoryListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewsService_NewsListByCateId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewsListByCateIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewsServiceServer).NewsListByCateId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.NewsService/NewsListByCateId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewsServiceServer).NewsListByCateId(ctx, req.(*NewsListByCateIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewsService_NewsTopicList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewsTopicListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewsServiceServer).NewsTopicList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.NewsService/NewsTopicList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewsServiceServer).NewsTopicList(ctx, req.(*NewsTopicListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewsService_NewsListByTopicId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewsListByTopicIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewsServiceServer).NewsListByTopicId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.NewsService/NewsListByTopicId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewsServiceServer).NewsListByTopicId(ctx, req.(*NewsListByTopicIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _NewsService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "islamic.v1.NewsService",
	HandlerType: (*NewsServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "NewsCategoryList",
			Handler:    _NewsService_NewsCategoryList_Handler,
		},
		{
			MethodName: "NewsListByCateId",
			Handler:    _NewsService_NewsListByCateId_Handler,
		},
		{
			MethodName: "NewsTopicList",
			Handler:    _NewsService_NewsTopicList_Handler,
		},
		{
			MethodName: "NewsListByTopicId",
			Handler:    _NewsService_NewsListByTopicId_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "islamic/v1/news.proto",
}
