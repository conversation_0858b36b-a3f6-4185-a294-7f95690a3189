// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: islamic/v1/faq.proto

package islamicv1

import (
	common "halalplus/api/common"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FaqCateListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LanguageId    uint32                 `protobuf:"varint,1,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言id"` // 语言id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FaqCateListReq) Reset() {
	*x = FaqCateListReq{}
	mi := &file_islamic_v1_faq_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaqCateListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaqCateListReq) ProtoMessage() {}

func (x *FaqCateListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_faq_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaqCateListReq.ProtoReflect.Descriptor instead.
func (*FaqCateListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_faq_proto_rawDescGZIP(), []int{0}
}

func (x *FaqCateListReq) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

type FaqCateItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	IsOpen        int32                  `protobuf:"varint,2,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	Sort          int32                  `protobuf:"varint,3,opt,name=sort,proto3" json:"sort,omitempty"`
	CateCount     int32                  `protobuf:"varint,4,opt,name=cate_count,json=cateCount,proto3" json:"cate_count,omitempty"`
	Title         string                 `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`
	LanguageId    uint32                 `protobuf:"varint,6,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FaqCateItem) Reset() {
	*x = FaqCateItem{}
	mi := &file_islamic_v1_faq_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaqCateItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaqCateItem) ProtoMessage() {}

func (x *FaqCateItem) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_faq_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaqCateItem.ProtoReflect.Descriptor instead.
func (*FaqCateItem) Descriptor() ([]byte, []int) {
	return file_islamic_v1_faq_proto_rawDescGZIP(), []int{1}
}

func (x *FaqCateItem) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *FaqCateItem) GetIsOpen() int32 {
	if x != nil {
		return x.IsOpen
	}
	return 0
}

func (x *FaqCateItem) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *FaqCateItem) GetCateCount() int32 {
	if x != nil {
		return x.CateCount
	}
	return 0
}

func (x *FaqCateItem) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *FaqCateItem) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

type FaqCateListRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          []*FaqCateItem         `protobuf:"bytes,4,rep,name=data,proto3" json:"data,omitempty" dc:"分类列表"` // 分类列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FaqCateListRes) Reset() {
	*x = FaqCateListRes{}
	mi := &file_islamic_v1_faq_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaqCateListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaqCateListRes) ProtoMessage() {}

func (x *FaqCateListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_faq_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaqCateListRes.ProtoReflect.Descriptor instead.
func (*FaqCateListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_faq_proto_rawDescGZIP(), []int{2}
}

func (x *FaqCateListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *FaqCateListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *FaqCateListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *FaqCateListRes) GetData() []*FaqCateItem {
	if x != nil {
		return x.Data
	}
	return nil
}

type FaqListByCateIdReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LanguageId    uint32                 `protobuf:"varint,1,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言id"` // 语言id
	CateId        uint32                 `protobuf:"varint,2,opt,name=cate_id,json=cateId,proto3" json:"cate_id,omitempty" dc:"分类id"`             // 分类id
	Keyword       string                 `protobuf:"bytes,3,opt,name=keyword,proto3" json:"keyword,omitempty" dc:"搜索关键字"`                         // 搜索关键字
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FaqListByCateIdReq) Reset() {
	*x = FaqListByCateIdReq{}
	mi := &file_islamic_v1_faq_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaqListByCateIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaqListByCateIdReq) ProtoMessage() {}

func (x *FaqListByCateIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_faq_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaqListByCateIdReq.ProtoReflect.Descriptor instead.
func (*FaqListByCateIdReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_faq_proto_rawDescGZIP(), []int{3}
}

func (x *FaqListByCateIdReq) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *FaqListByCateIdReq) GetCateId() uint32 {
	if x != nil {
		return x.CateId
	}
	return 0
}

func (x *FaqListByCateIdReq) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

type FaqListByCateIdRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          []*FaqQuestionItem     `protobuf:"bytes,4,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FaqListByCateIdRes) Reset() {
	*x = FaqListByCateIdRes{}
	mi := &file_islamic_v1_faq_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaqListByCateIdRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaqListByCateIdRes) ProtoMessage() {}

func (x *FaqListByCateIdRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_faq_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaqListByCateIdRes.ProtoReflect.Descriptor instead.
func (*FaqListByCateIdRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_faq_proto_rawDescGZIP(), []int{4}
}

func (x *FaqListByCateIdRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *FaqListByCateIdRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *FaqListByCateIdRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *FaqListByCateIdRes) GetData() []*FaqQuestionItem {
	if x != nil {
		return x.Data
	}
	return nil
}

type FaqQuestionItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	IsOpen        int32                  `protobuf:"varint,2,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	Sort          int32                  `protobuf:"varint,3,opt,name=sort,proto3" json:"sort,omitempty"`
	Views         int32                  `protobuf:"varint,4,opt,name=views,proto3" json:"views,omitempty"`
	Title         string                 `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`
	Desc          string                 `protobuf:"bytes,6,opt,name=desc,proto3" json:"desc,omitempty"`
	LanguageId    int32                  `protobuf:"varint,7,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty"`
	FaqCateId     uint32                 `protobuf:"varint,8,opt,name=faq_cate_id,json=faqCateId,proto3" json:"faq_cate_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FaqQuestionItem) Reset() {
	*x = FaqQuestionItem{}
	mi := &file_islamic_v1_faq_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaqQuestionItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaqQuestionItem) ProtoMessage() {}

func (x *FaqQuestionItem) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_faq_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaqQuestionItem.ProtoReflect.Descriptor instead.
func (*FaqQuestionItem) Descriptor() ([]byte, []int) {
	return file_islamic_v1_faq_proto_rawDescGZIP(), []int{5}
}

func (x *FaqQuestionItem) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *FaqQuestionItem) GetIsOpen() int32 {
	if x != nil {
		return x.IsOpen
	}
	return 0
}

func (x *FaqQuestionItem) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *FaqQuestionItem) GetViews() int32 {
	if x != nil {
		return x.Views
	}
	return 0
}

func (x *FaqQuestionItem) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *FaqQuestionItem) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *FaqQuestionItem) GetLanguageId() int32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *FaqQuestionItem) GetFaqCateId() uint32 {
	if x != nil {
		return x.FaqCateId
	}
	return 0
}

type FaqOneReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LanguageId    uint32                 `protobuf:"varint,1,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言id"` // 语言id
	Id            uint32                 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty" dc:"分类id"`                                   // 分类id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FaqOneReq) Reset() {
	*x = FaqOneReq{}
	mi := &file_islamic_v1_faq_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaqOneReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaqOneReq) ProtoMessage() {}

func (x *FaqOneReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_faq_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaqOneReq.ProtoReflect.Descriptor instead.
func (*FaqOneReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_faq_proto_rawDescGZIP(), []int{6}
}

func (x *FaqOneReq) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *FaqOneReq) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type FaqOneRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *FaqQuestionOneItem    `protobuf:"bytes,5,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FaqOneRes) Reset() {
	*x = FaqOneRes{}
	mi := &file_islamic_v1_faq_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaqOneRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaqOneRes) ProtoMessage() {}

func (x *FaqOneRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_faq_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaqOneRes.ProtoReflect.Descriptor instead.
func (*FaqOneRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_faq_proto_rawDescGZIP(), []int{7}
}

func (x *FaqOneRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *FaqOneRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *FaqOneRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *FaqOneRes) GetData() *FaqQuestionOneItem {
	if x != nil {
		return x.Data
	}
	return nil
}

type FaqQuestionOneItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	IsOpen        int32                  `protobuf:"varint,2,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	Sort          int32                  `protobuf:"varint,3,opt,name=sort,proto3" json:"sort,omitempty"`
	Views         int32                  `protobuf:"varint,4,opt,name=views,proto3" json:"views,omitempty"`
	Title         string                 `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`
	Desc          string                 `protobuf:"bytes,6,opt,name=desc,proto3" json:"desc,omitempty"`
	LanguageId    int32                  `protobuf:"varint,7,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty"`
	FaqCateId     uint32                 `protobuf:"varint,8,opt,name=faq_cate_id,json=faqCateId,proto3" json:"faq_cate_id,omitempty"`
	FaqCateTitle  string                 `protobuf:"bytes,9,opt,name=faq_cate_title,json=faqCateTitle,proto3" json:"faq_cate_title,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FaqQuestionOneItem) Reset() {
	*x = FaqQuestionOneItem{}
	mi := &file_islamic_v1_faq_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaqQuestionOneItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaqQuestionOneItem) ProtoMessage() {}

func (x *FaqQuestionOneItem) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_faq_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaqQuestionOneItem.ProtoReflect.Descriptor instead.
func (*FaqQuestionOneItem) Descriptor() ([]byte, []int) {
	return file_islamic_v1_faq_proto_rawDescGZIP(), []int{8}
}

func (x *FaqQuestionOneItem) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *FaqQuestionOneItem) GetIsOpen() int32 {
	if x != nil {
		return x.IsOpen
	}
	return 0
}

func (x *FaqQuestionOneItem) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *FaqQuestionOneItem) GetViews() int32 {
	if x != nil {
		return x.Views
	}
	return 0
}

func (x *FaqQuestionOneItem) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *FaqQuestionOneItem) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *FaqQuestionOneItem) GetLanguageId() int32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *FaqQuestionOneItem) GetFaqCateId() uint32 {
	if x != nil {
		return x.FaqCateId
	}
	return 0
}

func (x *FaqQuestionOneItem) GetFaqCateTitle() string {
	if x != nil {
		return x.FaqCateTitle
	}
	return ""
}

var File_islamic_v1_faq_proto protoreflect.FileDescriptor

const file_islamic_v1_faq_proto_rawDesc = "" +
	"\n" +
	"\x14islamic/v1/faq.proto\x12\n" +
	"islamic.v1\x1a\x11common/base.proto\"1\n" +
	"\x0eFaqCateListReq\x12\x1f\n" +
	"\vlanguage_id\x18\x01 \x01(\rR\n" +
	"languageId\"\xa0\x01\n" +
	"\vFaqCateItem\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\x12\x17\n" +
	"\ais_open\x18\x02 \x01(\x05R\x06isOpen\x12\x12\n" +
	"\x04sort\x18\x03 \x01(\x05R\x04sort\x12\x1d\n" +
	"\n" +
	"cate_count\x18\x04 \x01(\x05R\tcateCount\x12\x14\n" +
	"\x05title\x18\x05 \x01(\tR\x05title\x12\x1f\n" +
	"\vlanguage_id\x18\x06 \x01(\rR\n" +
	"languageId\"\x88\x01\n" +
	"\x0eFaqCateListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12+\n" +
	"\x04data\x18\x04 \x03(\v2\x17.islamic.v1.FaqCateItemR\x04data\"h\n" +
	"\x12FaqListByCateIdReq\x12\x1f\n" +
	"\vlanguage_id\x18\x01 \x01(\rR\n" +
	"languageId\x12\x17\n" +
	"\acate_id\x18\x02 \x01(\rR\x06cateId\x12\x18\n" +
	"\akeyword\x18\x03 \x01(\tR\akeyword\"\x90\x01\n" +
	"\x12FaqListByCateIdRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12/\n" +
	"\x04data\x18\x04 \x03(\v2\x1b.islamic.v1.FaqQuestionItemR\x04data\"\xcf\x01\n" +
	"\x0fFaqQuestionItem\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\x12\x17\n" +
	"\ais_open\x18\x02 \x01(\x05R\x06isOpen\x12\x12\n" +
	"\x04sort\x18\x03 \x01(\x05R\x04sort\x12\x14\n" +
	"\x05views\x18\x04 \x01(\x05R\x05views\x12\x14\n" +
	"\x05title\x18\x05 \x01(\tR\x05title\x12\x12\n" +
	"\x04desc\x18\x06 \x01(\tR\x04desc\x12\x1f\n" +
	"\vlanguage_id\x18\a \x01(\x05R\n" +
	"languageId\x12\x1e\n" +
	"\vfaq_cate_id\x18\b \x01(\rR\tfaqCateId\"<\n" +
	"\tFaqOneReq\x12\x1f\n" +
	"\vlanguage_id\x18\x01 \x01(\rR\n" +
	"languageId\x12\x0e\n" +
	"\x02id\x18\x02 \x01(\rR\x02id\"\x8a\x01\n" +
	"\tFaqOneRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x122\n" +
	"\x04data\x18\x05 \x01(\v2\x1e.islamic.v1.FaqQuestionOneItemR\x04data\"\xf8\x01\n" +
	"\x12FaqQuestionOneItem\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\x12\x17\n" +
	"\ais_open\x18\x02 \x01(\x05R\x06isOpen\x12\x12\n" +
	"\x04sort\x18\x03 \x01(\x05R\x04sort\x12\x14\n" +
	"\x05views\x18\x04 \x01(\x05R\x05views\x12\x14\n" +
	"\x05title\x18\x05 \x01(\tR\x05title\x12\x12\n" +
	"\x04desc\x18\x06 \x01(\tR\x04desc\x12\x1f\n" +
	"\vlanguage_id\x18\a \x01(\x05R\n" +
	"languageId\x12\x1e\n" +
	"\vfaq_cate_id\x18\b \x01(\rR\tfaqCateId\x12$\n" +
	"\x0efaq_cate_title\x18\t \x01(\tR\ffaqCateTitle2\xe2\x01\n" +
	"\n" +
	"FaqService\x12I\n" +
	"\x0fFaqCategoryList\x12\x1a.islamic.v1.FaqCateListReq\x1a\x1a.islamic.v1.FaqCateListRes\x12Q\n" +
	"\x0fFaqListByCateId\x12\x1e.islamic.v1.FaqListByCateIdReq\x1a\x1e.islamic.v1.FaqListByCateIdRes\x126\n" +
	"\x06FaqOne\x12\x15.islamic.v1.FaqOneReq\x1a\x15.islamic.v1.FaqOneResB<Z:halalplus/app/islamic-content-svc/api/islamic/v1;islamicv1b\x06proto3"

var (
	file_islamic_v1_faq_proto_rawDescOnce sync.Once
	file_islamic_v1_faq_proto_rawDescData []byte
)

func file_islamic_v1_faq_proto_rawDescGZIP() []byte {
	file_islamic_v1_faq_proto_rawDescOnce.Do(func() {
		file_islamic_v1_faq_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_islamic_v1_faq_proto_rawDesc), len(file_islamic_v1_faq_proto_rawDesc)))
	})
	return file_islamic_v1_faq_proto_rawDescData
}

var file_islamic_v1_faq_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_islamic_v1_faq_proto_goTypes = []any{
	(*FaqCateListReq)(nil),     // 0: islamic.v1.FaqCateListReq
	(*FaqCateItem)(nil),        // 1: islamic.v1.FaqCateItem
	(*FaqCateListRes)(nil),     // 2: islamic.v1.FaqCateListRes
	(*FaqListByCateIdReq)(nil), // 3: islamic.v1.FaqListByCateIdReq
	(*FaqListByCateIdRes)(nil), // 4: islamic.v1.FaqListByCateIdRes
	(*FaqQuestionItem)(nil),    // 5: islamic.v1.FaqQuestionItem
	(*FaqOneReq)(nil),          // 6: islamic.v1.FaqOneReq
	(*FaqOneRes)(nil),          // 7: islamic.v1.FaqOneRes
	(*FaqQuestionOneItem)(nil), // 8: islamic.v1.FaqQuestionOneItem
	(*common.Error)(nil),       // 9: common.Error
}
var file_islamic_v1_faq_proto_depIdxs = []int32{
	9, // 0: islamic.v1.FaqCateListRes.error:type_name -> common.Error
	1, // 1: islamic.v1.FaqCateListRes.data:type_name -> islamic.v1.FaqCateItem
	9, // 2: islamic.v1.FaqListByCateIdRes.error:type_name -> common.Error
	5, // 3: islamic.v1.FaqListByCateIdRes.data:type_name -> islamic.v1.FaqQuestionItem
	9, // 4: islamic.v1.FaqOneRes.error:type_name -> common.Error
	8, // 5: islamic.v1.FaqOneRes.data:type_name -> islamic.v1.FaqQuestionOneItem
	0, // 6: islamic.v1.FaqService.FaqCategoryList:input_type -> islamic.v1.FaqCateListReq
	3, // 7: islamic.v1.FaqService.FaqListByCateId:input_type -> islamic.v1.FaqListByCateIdReq
	6, // 8: islamic.v1.FaqService.FaqOne:input_type -> islamic.v1.FaqOneReq
	2, // 9: islamic.v1.FaqService.FaqCategoryList:output_type -> islamic.v1.FaqCateListRes
	4, // 10: islamic.v1.FaqService.FaqListByCateId:output_type -> islamic.v1.FaqListByCateIdRes
	7, // 11: islamic.v1.FaqService.FaqOne:output_type -> islamic.v1.FaqOneRes
	9, // [9:12] is the sub-list for method output_type
	6, // [6:9] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_islamic_v1_faq_proto_init() }
func file_islamic_v1_faq_proto_init() {
	if File_islamic_v1_faq_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_islamic_v1_faq_proto_rawDesc), len(file_islamic_v1_faq_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_islamic_v1_faq_proto_goTypes,
		DependencyIndexes: file_islamic_v1_faq_proto_depIdxs,
		MessageInfos:      file_islamic_v1_faq_proto_msgTypes,
	}.Build()
	File_islamic_v1_faq_proto = out.File
	file_islamic_v1_faq_proto_goTypes = nil
	file_islamic_v1_faq_proto_depIdxs = nil
}
