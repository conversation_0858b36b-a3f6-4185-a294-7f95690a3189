// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: pbentity/surah_read_collect.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SurahReadCollect struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         uint32 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`                                     //
	UserId     uint32 `protobuf:"varint,2,opt,name=UserId,proto3" json:"UserId,omitempty" dc:"用户id"`                   // 用户id
	AyahId     uint32 `protobuf:"varint,3,opt,name=AyahId,proto3" json:"AyahId,omitempty" dc:"ayah_id节id"`             // ayah_id节id
	SurahName  string `protobuf:"bytes,4,opt,name=SurahName,proto3" json:"SurahName,omitempty" dc:"名称"`                // 名称
	CreateTime int64  `protobuf:"varint,5,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间（注册时间）"`     // 创建时间（注册时间）
	UpdateTime int64  `protobuf:"varint,6,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"更新时间，0代表创建后未更新"` // 更新时间，0代表创建后未更新
}

func (x *SurahReadCollect) Reset() {
	*x = SurahReadCollect{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbentity_surah_read_collect_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurahReadCollect) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurahReadCollect) ProtoMessage() {}

func (x *SurahReadCollect) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_surah_read_collect_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurahReadCollect.ProtoReflect.Descriptor instead.
func (*SurahReadCollect) Descriptor() ([]byte, []int) {
	return file_pbentity_surah_read_collect_proto_rawDescGZIP(), []int{0}
}

func (x *SurahReadCollect) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SurahReadCollect) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *SurahReadCollect) GetAyahId() uint32 {
	if x != nil {
		return x.AyahId
	}
	return 0
}

func (x *SurahReadCollect) GetSurahName() string {
	if x != nil {
		return x.SurahName
	}
	return ""
}

func (x *SurahReadCollect) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *SurahReadCollect) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

var File_pbentity_surah_read_collect_proto protoreflect.FileDescriptor

var file_pbentity_surah_read_collect_proto_rawDesc = []byte{
	0x0a, 0x21, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x73, 0x75, 0x72, 0x61, 0x68,
	0x5f, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x08, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x22, 0xb0, 0x01,
	0x0a, 0x10, 0x53, 0x75, 0x72, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x41, 0x79,
	0x61, 0x68, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x41, 0x79, 0x61, 0x68,
	0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x75, 0x72, 0x61, 0x68, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x53, 0x75, 0x72, 0x61, 0x68, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x42, 0x30, 0x5a, 0x2e, 0x68, 0x61, 0x6c, 0x61, 0x6c, 0x70, 0x6c, 0x75, 0x73, 0x2f, 0x61, 0x70,
	0x70, 0x2f, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x2d, 0x73, 0x76, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbentity_surah_read_collect_proto_rawDescOnce sync.Once
	file_pbentity_surah_read_collect_proto_rawDescData = file_pbentity_surah_read_collect_proto_rawDesc
)

func file_pbentity_surah_read_collect_proto_rawDescGZIP() []byte {
	file_pbentity_surah_read_collect_proto_rawDescOnce.Do(func() {
		file_pbentity_surah_read_collect_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbentity_surah_read_collect_proto_rawDescData)
	})
	return file_pbentity_surah_read_collect_proto_rawDescData
}

var file_pbentity_surah_read_collect_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_surah_read_collect_proto_goTypes = []interface{}{
	(*SurahReadCollect)(nil), // 0: pbentity.SurahReadCollect
}
var file_pbentity_surah_read_collect_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_surah_read_collect_proto_init() }
func file_pbentity_surah_read_collect_proto_init() {
	if File_pbentity_surah_read_collect_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbentity_surah_read_collect_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SurahReadCollect); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbentity_surah_read_collect_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_surah_read_collect_proto_goTypes,
		DependencyIndexes: file_pbentity_surah_read_collect_proto_depIdxs,
		MessageInfos:      file_pbentity_surah_read_collect_proto_msgTypes,
	}.Build()
	File_pbentity_surah_read_collect_proto = out.File
	file_pbentity_surah_read_collect_proto_rawDesc = nil
	file_pbentity_surah_read_collect_proto_goTypes = nil
	file_pbentity_surah_read_collect_proto_depIdxs = nil
}
