// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: pbentity/news_topic_language.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type NewsTopicLanguage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         uint32 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`                                           //
	TopicId    uint32 `protobuf:"varint,2,opt,name=TopicId,proto3" json:"TopicId,omitempty" dc:"专题id"`                       // 专题id
	LanguageId uint32 `protobuf:"varint,3,opt,name=LanguageId,proto3" json:"LanguageId,omitempty" dc:"语言id,0-中文，1-英文，2-印尼语"` // 语言id,0-中文，1-英文，2-印尼语
	Name       string `protobuf:"bytes,4,opt,name=Name,proto3" json:"Name,omitempty" dc:"名称"`                                // 名称
	ShortName  string `protobuf:"bytes,5,opt,name=ShortName,proto3" json:"ShortName,omitempty" dc:"名称"`                      // 名称
	CreateTime int64  `protobuf:"varint,6,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间"`                 // 创建时间
	UpdateTime int64  `protobuf:"varint,7,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"修改时间"`                 // 修改时间
	DeleteTime int64  `protobuf:"varint,8,opt,name=DeleteTime,proto3" json:"DeleteTime,omitempty" dc:"删除时间"`                 // 删除时间
}

func (x *NewsTopicLanguage) Reset() {
	*x = NewsTopicLanguage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbentity_news_topic_language_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewsTopicLanguage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsTopicLanguage) ProtoMessage() {}

func (x *NewsTopicLanguage) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_news_topic_language_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsTopicLanguage.ProtoReflect.Descriptor instead.
func (*NewsTopicLanguage) Descriptor() ([]byte, []int) {
	return file_pbentity_news_topic_language_proto_rawDescGZIP(), []int{0}
}

func (x *NewsTopicLanguage) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *NewsTopicLanguage) GetTopicId() uint32 {
	if x != nil {
		return x.TopicId
	}
	return 0
}

func (x *NewsTopicLanguage) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *NewsTopicLanguage) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NewsTopicLanguage) GetShortName() string {
	if x != nil {
		return x.ShortName
	}
	return ""
}

func (x *NewsTopicLanguage) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *NewsTopicLanguage) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *NewsTopicLanguage) GetDeleteTime() int64 {
	if x != nil {
		return x.DeleteTime
	}
	return 0
}

var File_pbentity_news_topic_language_proto protoreflect.FileDescriptor

var file_pbentity_news_topic_language_proto_rawDesc = []byte{
	0x0a, 0x22, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x6e, 0x65, 0x77, 0x73, 0x5f,
	0x74, 0x6f, 0x70, 0x69, 0x63, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x22, 0xef,
	0x01, 0x0a, 0x11, 0x4e, 0x65, 0x77, 0x73, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x4c, 0x61, 0x6e, 0x67,
	0x75, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x02, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x49, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x49, 0x64, 0x12, 0x1e,
	0x0a, 0x0a, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0a, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x68, 0x6f, 0x72, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x53, 0x68, 0x6f, 0x72, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x42, 0x30, 0x5a, 0x2e, 0x68, 0x61, 0x6c, 0x61, 0x6c, 0x70, 0x6c, 0x75, 0x73, 0x2f, 0x61, 0x70,
	0x70, 0x2f, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x2d, 0x73, 0x76, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbentity_news_topic_language_proto_rawDescOnce sync.Once
	file_pbentity_news_topic_language_proto_rawDescData = file_pbentity_news_topic_language_proto_rawDesc
)

func file_pbentity_news_topic_language_proto_rawDescGZIP() []byte {
	file_pbentity_news_topic_language_proto_rawDescOnce.Do(func() {
		file_pbentity_news_topic_language_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbentity_news_topic_language_proto_rawDescData)
	})
	return file_pbentity_news_topic_language_proto_rawDescData
}

var file_pbentity_news_topic_language_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_news_topic_language_proto_goTypes = []interface{}{
	(*NewsTopicLanguage)(nil), // 0: pbentity.NewsTopicLanguage
}
var file_pbentity_news_topic_language_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_news_topic_language_proto_init() }
func file_pbentity_news_topic_language_proto_init() {
	if File_pbentity_news_topic_language_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbentity_news_topic_language_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewsTopicLanguage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbentity_news_topic_language_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_news_topic_language_proto_goTypes,
		DependencyIndexes: file_pbentity_news_topic_language_proto_depIdxs,
		MessageInfos:      file_pbentity_news_topic_language_proto_msgTypes,
	}.Build()
	File_pbentity_news_topic_language_proto = out.File
	file_pbentity_news_topic_language_proto_rawDesc = nil
	file_pbentity_news_topic_language_proto_goTypes = nil
	file_pbentity_news_topic_language_proto_depIdxs = nil
}
