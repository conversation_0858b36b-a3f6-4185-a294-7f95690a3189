// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.1
// source: pbentity/calendar_hijriah.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CalendarHijriah struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             int64                  `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"主键ID"`                                                               // 主键ID
	GregorianYear  int32                  `protobuf:"varint,2,opt,name=GregorianYear,proto3" json:"GregorianYear,omitempty" dc:"公历年"`                                          // 公历年
	GregorianMonth int32                  `protobuf:"varint,3,opt,name=GregorianMonth,proto3" json:"GregorianMonth,omitempty" dc:"公历月"`                                        // 公历月
	GregorianDay   int32                  `protobuf:"varint,4,opt,name=GregorianDay,proto3" json:"GregorianDay,omitempty" dc:"公历日"`                                            // 公历日
	HijriahYear    int32                  `protobuf:"varint,5,opt,name=HijriahYear,proto3" json:"HijriahYear,omitempty" dc:"Hijriah年"`                                         // Hijriah年
	HijriahMonth   int32                  `protobuf:"varint,6,opt,name=HijriahMonth,proto3" json:"HijriahMonth,omitempty" dc:"Hijriah月"`                                       // Hijriah月
	HijriahDay     int32                  `protobuf:"varint,7,opt,name=HijriahDay,proto3" json:"HijriahDay,omitempty" dc:"Hijriah日"`                                           // Hijriah日
	MethodCode     string                 `protobuf:"bytes,8,opt,name=MethodCode,proto3" json:"MethodCode,omitempty" dc:"计算方法代码，如：LFNU, UMMUL_QURA"`                           // 计算方法代码，如：LFNU, UMMUL_QURA
	Weekday        int32                  `protobuf:"varint,9,opt,name=Weekday,proto3" json:"Weekday,omitempty" dc:"星期：0-6 (0=Sunday, 1=Monday, ..., 6=Saturday)"`             // 星期：0-6 (0=Sunday, 1=Monday, ..., 6=Saturday)
	Pasaran        int32                  `protobuf:"varint,10,opt,name=Pasaran,proto3" json:"Pasaran,omitempty" dc:"Pasaran：0-4 (0=Kliwon, 1=Legi, 2=Pahing, 3=Pon, 4=Wage)"` // Pasaran：0-4 (0=Kliwon, 1=Legi, 2=Pahing, 3=Pon, 4=Wage)
	CreatedAt      *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=CreatedAt,proto3" json:"CreatedAt,omitempty" dc:"创建时间"`                                                 // 创建时间
	UpdatedAt      *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=UpdatedAt,proto3" json:"UpdatedAt,omitempty" dc:"更新时间"`                                                 // 更新时间
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CalendarHijriah) Reset() {
	*x = CalendarHijriah{}
	mi := &file_pbentity_calendar_hijriah_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CalendarHijriah) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalendarHijriah) ProtoMessage() {}

func (x *CalendarHijriah) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_calendar_hijriah_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalendarHijriah.ProtoReflect.Descriptor instead.
func (*CalendarHijriah) Descriptor() ([]byte, []int) {
	return file_pbentity_calendar_hijriah_proto_rawDescGZIP(), []int{0}
}

func (x *CalendarHijriah) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CalendarHijriah) GetGregorianYear() int32 {
	if x != nil {
		return x.GregorianYear
	}
	return 0
}

func (x *CalendarHijriah) GetGregorianMonth() int32 {
	if x != nil {
		return x.GregorianMonth
	}
	return 0
}

func (x *CalendarHijriah) GetGregorianDay() int32 {
	if x != nil {
		return x.GregorianDay
	}
	return 0
}

func (x *CalendarHijriah) GetHijriahYear() int32 {
	if x != nil {
		return x.HijriahYear
	}
	return 0
}

func (x *CalendarHijriah) GetHijriahMonth() int32 {
	if x != nil {
		return x.HijriahMonth
	}
	return 0
}

func (x *CalendarHijriah) GetHijriahDay() int32 {
	if x != nil {
		return x.HijriahDay
	}
	return 0
}

func (x *CalendarHijriah) GetMethodCode() string {
	if x != nil {
		return x.MethodCode
	}
	return ""
}

func (x *CalendarHijriah) GetWeekday() int32 {
	if x != nil {
		return x.Weekday
	}
	return 0
}

func (x *CalendarHijriah) GetPasaran() int32 {
	if x != nil {
		return x.Pasaran
	}
	return 0
}

func (x *CalendarHijriah) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *CalendarHijriah) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

var File_pbentity_calendar_hijriah_proto protoreflect.FileDescriptor

const file_pbentity_calendar_hijriah_proto_rawDesc = "" +
	"\n" +
	"\x1fpbentity/calendar_hijriah.proto\x12\bpbentity\x1a\x1fgoogle/protobuf/timestamp.proto\"\xc1\x03\n" +
	"\x0fCalendarHijriah\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\x03R\x02Id\x12$\n" +
	"\rGregorianYear\x18\x02 \x01(\x05R\rGregorianYear\x12&\n" +
	"\x0eGregorianMonth\x18\x03 \x01(\x05R\x0eGregorianMonth\x12\"\n" +
	"\fGregorianDay\x18\x04 \x01(\x05R\fGregorianDay\x12 \n" +
	"\vHijriahYear\x18\x05 \x01(\x05R\vHijriahYear\x12\"\n" +
	"\fHijriahMonth\x18\x06 \x01(\x05R\fHijriahMonth\x12\x1e\n" +
	"\n" +
	"HijriahDay\x18\a \x01(\x05R\n" +
	"HijriahDay\x12\x1e\n" +
	"\n" +
	"MethodCode\x18\b \x01(\tR\n" +
	"MethodCode\x12\x18\n" +
	"\aWeekday\x18\t \x01(\x05R\aWeekday\x12\x18\n" +
	"\aPasaran\x18\n" +
	" \x01(\x05R\aPasaran\x128\n" +
	"\tCreatedAt\x18\v \x01(\v2\x1a.google.protobuf.TimestampR\tCreatedAt\x128\n" +
	"\tUpdatedAt\x18\f \x01(\v2\x1a.google.protobuf.TimestampR\tUpdatedAtB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_calendar_hijriah_proto_rawDescOnce sync.Once
	file_pbentity_calendar_hijriah_proto_rawDescData []byte
)

func file_pbentity_calendar_hijriah_proto_rawDescGZIP() []byte {
	file_pbentity_calendar_hijriah_proto_rawDescOnce.Do(func() {
		file_pbentity_calendar_hijriah_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_calendar_hijriah_proto_rawDesc), len(file_pbentity_calendar_hijriah_proto_rawDesc)))
	})
	return file_pbentity_calendar_hijriah_proto_rawDescData
}

var file_pbentity_calendar_hijriah_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_calendar_hijriah_proto_goTypes = []any{
	(*CalendarHijriah)(nil),       // 0: pbentity.CalendarHijriah
	(*timestamppb.Timestamp)(nil), // 1: google.protobuf.Timestamp
}
var file_pbentity_calendar_hijriah_proto_depIdxs = []int32{
	1, // 0: pbentity.CalendarHijriah.CreatedAt:type_name -> google.protobuf.Timestamp
	1, // 1: pbentity.CalendarHijriah.UpdatedAt:type_name -> google.protobuf.Timestamp
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_pbentity_calendar_hijriah_proto_init() }
func file_pbentity_calendar_hijriah_proto_init() {
	if File_pbentity_calendar_hijriah_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_calendar_hijriah_proto_rawDesc), len(file_pbentity_calendar_hijriah_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_calendar_hijriah_proto_goTypes,
		DependencyIndexes: file_pbentity_calendar_hijriah_proto_depIdxs,
		MessageInfos:      file_pbentity_calendar_hijriah_proto_msgTypes,
	}.Build()
	File_pbentity_calendar_hijriah_proto = out.File
	file_pbentity_calendar_hijriah_proto_goTypes = nil
	file_pbentity_calendar_hijriah_proto_depIdxs = nil
}
