// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.1
// source: pbentity/calendar_events.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CalendarEvents struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             int64                  `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"主键ID"`                                                           // 主键ID
	EventType      string                 `protobuf:"bytes,2,opt,name=EventType,proto3" json:"EventType,omitempty" dc:"事件类型：HARI_BESAR-重大节日，LIBUR_NASIONAL-国定假日，PUASA-斋戒"` // 事件类型：HARI_BESAR-重大节日，LIBUR_NASIONAL-国定假日，PUASA-斋戒
	Title          string                 `protobuf:"bytes,3,opt,name=Title,proto3" json:"Title,omitempty" dc:"事件标题"`                                                      // 事件标题
	Description    string                 `protobuf:"bytes,4,opt,name=Description,proto3" json:"Description,omitempty" dc:"事件描述"`                                          // 事件描述
	GregorianYear  int32                  `protobuf:"varint,5,opt,name=GregorianYear,proto3" json:"GregorianYear,omitempty" dc:"公历年"`                                      // 公历年
	GregorianMonth int32                  `protobuf:"varint,6,opt,name=GregorianMonth,proto3" json:"GregorianMonth,omitempty" dc:"公历月"`                                    // 公历月
	GregorianDay   int32                  `protobuf:"varint,7,opt,name=GregorianDay,proto3" json:"GregorianDay,omitempty" dc:"公历日"`                                        // 公历日
	JumpUrl        string                 `protobuf:"bytes,8,opt,name=JumpUrl,proto3" json:"JumpUrl,omitempty" dc:"点击跳转链接"`                                                // 点击跳转链接
	DataSource     string                 `protobuf:"bytes,9,opt,name=DataSource,proto3" json:"DataSource,omitempty" dc:"数据来源：MANUAL-人工录入，CRAWLER-爬虫获取"`                   // 数据来源：MANUAL-人工录入，CRAWLER-爬虫获取
	IsActive       int32                  `protobuf:"varint,10,opt,name=IsActive,proto3" json:"IsActive,omitempty" dc:"是否启用：0-禁用，1-启用"`                                    // 是否启用：0-禁用，1-启用
	CreatedAt      *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=CreatedAt,proto3" json:"CreatedAt,omitempty" dc:"创建时间"`                                             // 创建时间
	UpdatedAt      *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=UpdatedAt,proto3" json:"UpdatedAt,omitempty" dc:"更新时间"`                                             // 更新时间
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CalendarEvents) Reset() {
	*x = CalendarEvents{}
	mi := &file_pbentity_calendar_events_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CalendarEvents) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalendarEvents) ProtoMessage() {}

func (x *CalendarEvents) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_calendar_events_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalendarEvents.ProtoReflect.Descriptor instead.
func (*CalendarEvents) Descriptor() ([]byte, []int) {
	return file_pbentity_calendar_events_proto_rawDescGZIP(), []int{0}
}

func (x *CalendarEvents) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CalendarEvents) GetEventType() string {
	if x != nil {
		return x.EventType
	}
	return ""
}

func (x *CalendarEvents) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *CalendarEvents) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CalendarEvents) GetGregorianYear() int32 {
	if x != nil {
		return x.GregorianYear
	}
	return 0
}

func (x *CalendarEvents) GetGregorianMonth() int32 {
	if x != nil {
		return x.GregorianMonth
	}
	return 0
}

func (x *CalendarEvents) GetGregorianDay() int32 {
	if x != nil {
		return x.GregorianDay
	}
	return 0
}

func (x *CalendarEvents) GetJumpUrl() string {
	if x != nil {
		return x.JumpUrl
	}
	return ""
}

func (x *CalendarEvents) GetDataSource() string {
	if x != nil {
		return x.DataSource
	}
	return ""
}

func (x *CalendarEvents) GetIsActive() int32 {
	if x != nil {
		return x.IsActive
	}
	return 0
}

func (x *CalendarEvents) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *CalendarEvents) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

var File_pbentity_calendar_events_proto protoreflect.FileDescriptor

const file_pbentity_calendar_events_proto_rawDesc = "" +
	"\n" +
	"\x1epbentity/calendar_events.proto\x12\bpbentity\x1a\x1fgoogle/protobuf/timestamp.proto\"\xb2\x03\n" +
	"\x0eCalendarEvents\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\x03R\x02Id\x12\x1c\n" +
	"\tEventType\x18\x02 \x01(\tR\tEventType\x12\x14\n" +
	"\x05Title\x18\x03 \x01(\tR\x05Title\x12 \n" +
	"\vDescription\x18\x04 \x01(\tR\vDescription\x12$\n" +
	"\rGregorianYear\x18\x05 \x01(\x05R\rGregorianYear\x12&\n" +
	"\x0eGregorianMonth\x18\x06 \x01(\x05R\x0eGregorianMonth\x12\"\n" +
	"\fGregorianDay\x18\a \x01(\x05R\fGregorianDay\x12\x18\n" +
	"\aJumpUrl\x18\b \x01(\tR\aJumpUrl\x12\x1e\n" +
	"\n" +
	"DataSource\x18\t \x01(\tR\n" +
	"DataSource\x12\x1a\n" +
	"\bIsActive\x18\n" +
	" \x01(\x05R\bIsActive\x128\n" +
	"\tCreatedAt\x18\v \x01(\v2\x1a.google.protobuf.TimestampR\tCreatedAt\x128\n" +
	"\tUpdatedAt\x18\f \x01(\v2\x1a.google.protobuf.TimestampR\tUpdatedAtB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_calendar_events_proto_rawDescOnce sync.Once
	file_pbentity_calendar_events_proto_rawDescData []byte
)

func file_pbentity_calendar_events_proto_rawDescGZIP() []byte {
	file_pbentity_calendar_events_proto_rawDescOnce.Do(func() {
		file_pbentity_calendar_events_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_calendar_events_proto_rawDesc), len(file_pbentity_calendar_events_proto_rawDesc)))
	})
	return file_pbentity_calendar_events_proto_rawDescData
}

var file_pbentity_calendar_events_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_calendar_events_proto_goTypes = []any{
	(*CalendarEvents)(nil),        // 0: pbentity.CalendarEvents
	(*timestamppb.Timestamp)(nil), // 1: google.protobuf.Timestamp
}
var file_pbentity_calendar_events_proto_depIdxs = []int32{
	1, // 0: pbentity.CalendarEvents.CreatedAt:type_name -> google.protobuf.Timestamp
	1, // 1: pbentity.CalendarEvents.UpdatedAt:type_name -> google.protobuf.Timestamp
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_pbentity_calendar_events_proto_init() }
func file_pbentity_calendar_events_proto_init() {
	if File_pbentity_calendar_events_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_calendar_events_proto_rawDesc), len(file_pbentity_calendar_events_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_calendar_events_proto_goTypes,
		DependencyIndexes: file_pbentity_calendar_events_proto_depIdxs,
		MessageInfos:      file_pbentity_calendar_events_proto_msgTypes,
	}.Build()
	File_pbentity_calendar_events_proto = out.File
	file_pbentity_calendar_events_proto_goTypes = nil
	file_pbentity_calendar_events_proto_depIdxs = nil
}
