// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.1
// source: pbentity/banner.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Banner struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"主键ID"`                                    // 主键ID
	LanguageId    uint32                 `protobuf:"varint,2,opt,name=LanguageId,proto3" json:"LanguageId,omitempty" dc:"语言ID: 0-中文, 1-英文, 2-印尼语"` // 语言ID: 0-中文, 1-英文, 2-印尼语
	Title         string                 `protobuf:"bytes,3,opt,name=Title,proto3" json:"Title,omitempty" dc:"广告标题"`                               // 广告标题
	Description   string                 `protobuf:"bytes,4,opt,name=Description,proto3" json:"Description,omitempty" dc:"广告描述"`                   // 广告描述
	ImageUrl      string                 `protobuf:"bytes,5,opt,name=ImageUrl,proto3" json:"ImageUrl,omitempty" dc:"广告图片URL"`                      // 广告图片URL
	LinkUrl       string                 `protobuf:"bytes,6,opt,name=LinkUrl,proto3" json:"LinkUrl,omitempty" dc:"跳转链接URL"`                        // 跳转链接URL
	SortOrder     uint32                 `protobuf:"varint,7,opt,name=SortOrder,proto3" json:"SortOrder,omitempty" dc:"排序权重，数字越小越靠前"`              // 排序权重，数字越小越靠前
	Status        uint32                 `protobuf:"varint,8,opt,name=Status,proto3" json:"Status,omitempty" dc:"状态: 0-禁用, 1-启用"`                  // 状态: 0-禁用, 1-启用
	StartTime     uint64                 `protobuf:"varint,9,opt,name=StartTime,proto3" json:"StartTime,omitempty" dc:"开始时间戳(毫秒)"`                 // 开始时间戳(毫秒)
	EndTime       uint64                 `protobuf:"varint,10,opt,name=EndTime,proto3" json:"EndTime,omitempty" dc:"结束时间戳(毫秒)"`                    // 结束时间戳(毫秒)
	AdminId       uint32                 `protobuf:"varint,11,opt,name=AdminId,proto3" json:"AdminId,omitempty" dc:"创建管理员ID"`                      // 创建管理员ID
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=CreatedAt,proto3" json:"CreatedAt,omitempty" dc:"创建时间"`                      // 创建时间
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=UpdatedAt,proto3" json:"UpdatedAt,omitempty" dc:"更新时间"`                      // 更新时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Banner) Reset() {
	*x = Banner{}
	mi := &file_pbentity_banner_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Banner) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Banner) ProtoMessage() {}

func (x *Banner) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_banner_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Banner.ProtoReflect.Descriptor instead.
func (*Banner) Descriptor() ([]byte, []int) {
	return file_pbentity_banner_proto_rawDescGZIP(), []int{0}
}

func (x *Banner) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Banner) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *Banner) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Banner) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Banner) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

func (x *Banner) GetLinkUrl() string {
	if x != nil {
		return x.LinkUrl
	}
	return ""
}

func (x *Banner) GetSortOrder() uint32 {
	if x != nil {
		return x.SortOrder
	}
	return 0
}

func (x *Banner) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *Banner) GetStartTime() uint64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *Banner) GetEndTime() uint64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *Banner) GetAdminId() uint32 {
	if x != nil {
		return x.AdminId
	}
	return 0
}

func (x *Banner) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Banner) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

var File_pbentity_banner_proto protoreflect.FileDescriptor

const file_pbentity_banner_proto_rawDesc = "" +
	"\n" +
	"\x15pbentity/banner.proto\x12\bpbentity\x1a\x1fgoogle/protobuf/timestamp.proto\"\xa2\x03\n" +
	"\x06Banner\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\rR\x02Id\x12\x1e\n" +
	"\n" +
	"LanguageId\x18\x02 \x01(\rR\n" +
	"LanguageId\x12\x14\n" +
	"\x05Title\x18\x03 \x01(\tR\x05Title\x12 \n" +
	"\vDescription\x18\x04 \x01(\tR\vDescription\x12\x1a\n" +
	"\bImageUrl\x18\x05 \x01(\tR\bImageUrl\x12\x18\n" +
	"\aLinkUrl\x18\x06 \x01(\tR\aLinkUrl\x12\x1c\n" +
	"\tSortOrder\x18\a \x01(\rR\tSortOrder\x12\x16\n" +
	"\x06Status\x18\b \x01(\rR\x06Status\x12\x1c\n" +
	"\tStartTime\x18\t \x01(\x04R\tStartTime\x12\x18\n" +
	"\aEndTime\x18\n" +
	" \x01(\x04R\aEndTime\x12\x18\n" +
	"\aAdminId\x18\v \x01(\rR\aAdminId\x128\n" +
	"\tCreatedAt\x18\f \x01(\v2\x1a.google.protobuf.TimestampR\tCreatedAt\x128\n" +
	"\tUpdatedAt\x18\r \x01(\v2\x1a.google.protobuf.TimestampR\tUpdatedAtB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_banner_proto_rawDescOnce sync.Once
	file_pbentity_banner_proto_rawDescData []byte
)

func file_pbentity_banner_proto_rawDescGZIP() []byte {
	file_pbentity_banner_proto_rawDescOnce.Do(func() {
		file_pbentity_banner_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_banner_proto_rawDesc), len(file_pbentity_banner_proto_rawDesc)))
	})
	return file_pbentity_banner_proto_rawDescData
}

var file_pbentity_banner_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_banner_proto_goTypes = []any{
	(*Banner)(nil),                // 0: pbentity.Banner
	(*timestamppb.Timestamp)(nil), // 1: google.protobuf.Timestamp
}
var file_pbentity_banner_proto_depIdxs = []int32{
	1, // 0: pbentity.Banner.CreatedAt:type_name -> google.protobuf.Timestamp
	1, // 1: pbentity.Banner.UpdatedAt:type_name -> google.protobuf.Timestamp
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_pbentity_banner_proto_init() }
func file_pbentity_banner_proto_init() {
	if File_pbentity_banner_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_banner_proto_rawDesc), len(file_pbentity_banner_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_banner_proto_goTypes,
		DependencyIndexes: file_pbentity_banner_proto_depIdxs,
		MessageInfos:      file_pbentity_banner_proto_msgTypes,
	}.Build()
	File_pbentity_banner_proto = out.File
	file_pbentity_banner_proto_goTypes = nil
	file_pbentity_banner_proto_depIdxs = nil
}
