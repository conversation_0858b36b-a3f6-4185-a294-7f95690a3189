// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: pbentity/surat_daftar.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SuratDaftar struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int32                  `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`                                  //
	Nomor       int32                  `protobuf:"varint,2,opt,name=Nomor,proto3" json:"Nomor,omitempty" dc:"章节编号 (1-114)"`          // 章节编号 (1-114)
	Nama        string                 `protobuf:"bytes,3,opt,name=Nama,proto3" json:"Nama,omitempty" dc:"阿拉伯语章节名"`                  // 阿拉伯语章节名
	NamaLatin   string                 `protobuf:"bytes,4,opt,name=NamaLatin,proto3" json:"NamaLatin,omitempty" dc:"拉丁化章节名"`         // 拉丁化章节名
	JumlahAyat  int32                  `protobuf:"varint,5,opt,name=JumlahAyat,proto3" json:"JumlahAyat,omitempty" dc:"经文数量"`        // 经文数量
	TempatTurun string                 `protobuf:"bytes,6,opt,name=TempatTurun,proto3" json:"TempatTurun,omitempty" dc:"降示地点"`       // 降示地点
	Arti        string                 `protobuf:"bytes,7,opt,name=Arti,proto3" json:"Arti,omitempty" dc:"章节含义"`                     // 章节含义
	Deskripsi   string                 `protobuf:"bytes,8,opt,name=Deskripsi,proto3" json:"Deskripsi,omitempty" dc:"章节描述"`           // 章节描述
	Audio       string                 `protobuf:"bytes,9,opt,name=Audio,proto3" json:"Audio,omitempty" dc:"音频文件URL"`                // 音频文件URL
	Status      int32                  `protobuf:"varint,10,opt,name=Status,proto3" json:"Status,omitempty" dc:"状态标识"`               // 状态标识
	CreatedAt   *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=CreatedAt,proto3" json:"CreatedAt,omitempty"`                    //
	UpdatedAt   *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=UpdatedAt,proto3" json:"UpdatedAt,omitempty"`                    //
	IsPopular   int32                  `protobuf:"varint,13,opt,name=IsPopular,proto3" json:"IsPopular,omitempty" dc:"是否热门章节 0否 1是"` // 是否热门章节 0否 1是
}

func (x *SuratDaftar) Reset() {
	*x = SuratDaftar{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbentity_surat_daftar_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SuratDaftar) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SuratDaftar) ProtoMessage() {}

func (x *SuratDaftar) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_surat_daftar_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SuratDaftar.ProtoReflect.Descriptor instead.
func (*SuratDaftar) Descriptor() ([]byte, []int) {
	return file_pbentity_surat_daftar_proto_rawDescGZIP(), []int{0}
}

func (x *SuratDaftar) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SuratDaftar) GetNomor() int32 {
	if x != nil {
		return x.Nomor
	}
	return 0
}

func (x *SuratDaftar) GetNama() string {
	if x != nil {
		return x.Nama
	}
	return ""
}

func (x *SuratDaftar) GetNamaLatin() string {
	if x != nil {
		return x.NamaLatin
	}
	return ""
}

func (x *SuratDaftar) GetJumlahAyat() int32 {
	if x != nil {
		return x.JumlahAyat
	}
	return 0
}

func (x *SuratDaftar) GetTempatTurun() string {
	if x != nil {
		return x.TempatTurun
	}
	return ""
}

func (x *SuratDaftar) GetArti() string {
	if x != nil {
		return x.Arti
	}
	return ""
}

func (x *SuratDaftar) GetDeskripsi() string {
	if x != nil {
		return x.Deskripsi
	}
	return ""
}

func (x *SuratDaftar) GetAudio() string {
	if x != nil {
		return x.Audio
	}
	return ""
}

func (x *SuratDaftar) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *SuratDaftar) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *SuratDaftar) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *SuratDaftar) GetIsPopular() int32 {
	if x != nil {
		return x.IsPopular
	}
	return 0
}

var File_pbentity_surat_daftar_proto protoreflect.FileDescriptor

var file_pbentity_surat_daftar_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x73, 0x75, 0x72, 0x61, 0x74,
	0x5f, 0x64, 0x61, 0x66, 0x74, 0x61, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x70,
	0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x99, 0x03, 0x0a, 0x0b, 0x53, 0x75, 0x72,
	0x61, 0x74, 0x44, 0x61, 0x66, 0x74, 0x61, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x4e, 0x6f, 0x6d, 0x6f,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x4e, 0x6f, 0x6d, 0x6f, 0x72, 0x12, 0x12,
	0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61,
	0x6d, 0x61, 0x12, 0x1c, 0x0a, 0x09, 0x4e, 0x61, 0x6d, 0x61, 0x4c, 0x61, 0x74, 0x69, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x4e, 0x61, 0x6d, 0x61, 0x4c, 0x61, 0x74, 0x69, 0x6e,
	0x12, 0x1e, 0x0a, 0x0a, 0x4a, 0x75, 0x6d, 0x6c, 0x61, 0x68, 0x41, 0x79, 0x61, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x4a, 0x75, 0x6d, 0x6c, 0x61, 0x68, 0x41, 0x79, 0x61, 0x74,
	0x12, 0x20, 0x0a, 0x0b, 0x54, 0x65, 0x6d, 0x70, 0x61, 0x74, 0x54, 0x75, 0x72, 0x75, 0x6e, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x54, 0x65, 0x6d, 0x70, 0x61, 0x74, 0x54, 0x75, 0x72,
	0x75, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x41, 0x72, 0x74, 0x69, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x41, 0x72, 0x74, 0x69, 0x12, 0x1c, 0x0a, 0x09, 0x44, 0x65, 0x73, 0x6b, 0x72, 0x69,
	0x70, 0x73, 0x69, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x44, 0x65, 0x73, 0x6b, 0x72,
	0x69, 0x70, 0x73, 0x69, 0x12, 0x14, 0x0a, 0x05, 0x41, 0x75, 0x64, 0x69, 0x6f, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x41, 0x75, 0x64, 0x69, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x38, 0x0a, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x38, 0x0a, 0x09,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x49, 0x73, 0x50, 0x6f, 0x70, 0x75,
	0x6c, 0x61, 0x72, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x49, 0x73, 0x50, 0x6f, 0x70,
	0x75, 0x6c, 0x61, 0x72, 0x42, 0x30, 0x5a, 0x2e, 0x68, 0x61, 0x6c, 0x61, 0x6c, 0x70, 0x6c, 0x75,
	0x73, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2d, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x76, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x62,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbentity_surat_daftar_proto_rawDescOnce sync.Once
	file_pbentity_surat_daftar_proto_rawDescData = file_pbentity_surat_daftar_proto_rawDesc
)

func file_pbentity_surat_daftar_proto_rawDescGZIP() []byte {
	file_pbentity_surat_daftar_proto_rawDescOnce.Do(func() {
		file_pbentity_surat_daftar_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbentity_surat_daftar_proto_rawDescData)
	})
	return file_pbentity_surat_daftar_proto_rawDescData
}

var file_pbentity_surat_daftar_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_surat_daftar_proto_goTypes = []interface{}{
	(*SuratDaftar)(nil),           // 0: pbentity.SuratDaftar
	(*timestamppb.Timestamp)(nil), // 1: google.protobuf.Timestamp
}
var file_pbentity_surat_daftar_proto_depIdxs = []int32{
	1, // 0: pbentity.SuratDaftar.CreatedAt:type_name -> google.protobuf.Timestamp
	1, // 1: pbentity.SuratDaftar.UpdatedAt:type_name -> google.protobuf.Timestamp
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_pbentity_surat_daftar_proto_init() }
func file_pbentity_surat_daftar_proto_init() {
	if File_pbentity_surat_daftar_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbentity_surat_daftar_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SuratDaftar); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbentity_surat_daftar_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_surat_daftar_proto_goTypes,
		DependencyIndexes: file_pbentity_surat_daftar_proto_depIdxs,
		MessageInfos:      file_pbentity_surat_daftar_proto_msgTypes,
	}.Build()
	File_pbentity_surat_daftar_proto = out.File
	file_pbentity_surat_daftar_proto_rawDesc = nil
	file_pbentity_surat_daftar_proto_goTypes = nil
	file_pbentity_surat_daftar_proto_depIdxs = nil
}
