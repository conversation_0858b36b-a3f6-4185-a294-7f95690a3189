// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.1
// source: pbentity/banner_stats.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BannerStats struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"主键ID"`                  // 主键ID
	BannerId      uint32                 `protobuf:"varint,2,opt,name=BannerId,proto3" json:"BannerId,omitempty" dc:"广告ID"`      // 广告ID
	UserId        uint64                 `protobuf:"varint,3,opt,name=UserId,proto3" json:"UserId,omitempty" dc:"用户ID，0表示未登录用户"` // 用户ID，0表示未登录用户
	DeviceId      string                 `protobuf:"bytes,4,opt,name=DeviceId,proto3" json:"DeviceId,omitempty" dc:"设备唯一标识"`     // 设备唯一标识
	IpAddress     string                 `protobuf:"bytes,5,opt,name=IpAddress,proto3" json:"IpAddress,omitempty" dc:"IP地址"`     // IP地址
	UserAgent     string                 `protobuf:"bytes,6,opt,name=UserAgent,proto3" json:"UserAgent,omitempty" dc:"用户代理信息"`   // 用户代理信息
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=CreatedAt,proto3" json:"CreatedAt,omitempty" dc:"操作时间"`     // 操作时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BannerStats) Reset() {
	*x = BannerStats{}
	mi := &file_pbentity_banner_stats_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BannerStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BannerStats) ProtoMessage() {}

func (x *BannerStats) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_banner_stats_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BannerStats.ProtoReflect.Descriptor instead.
func (*BannerStats) Descriptor() ([]byte, []int) {
	return file_pbentity_banner_stats_proto_rawDescGZIP(), []int{0}
}

func (x *BannerStats) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BannerStats) GetBannerId() uint32 {
	if x != nil {
		return x.BannerId
	}
	return 0
}

func (x *BannerStats) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *BannerStats) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *BannerStats) GetIpAddress() string {
	if x != nil {
		return x.IpAddress
	}
	return ""
}

func (x *BannerStats) GetUserAgent() string {
	if x != nil {
		return x.UserAgent
	}
	return ""
}

func (x *BannerStats) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

var File_pbentity_banner_stats_proto protoreflect.FileDescriptor

const file_pbentity_banner_stats_proto_rawDesc = "" +
	"\n" +
	"\x1bpbentity/banner_stats.proto\x12\bpbentity\x1a\x1fgoogle/protobuf/timestamp.proto\"\xe3\x01\n" +
	"\vBannerStats\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\x04R\x02Id\x12\x1a\n" +
	"\bBannerId\x18\x02 \x01(\rR\bBannerId\x12\x16\n" +
	"\x06UserId\x18\x03 \x01(\x04R\x06UserId\x12\x1a\n" +
	"\bDeviceId\x18\x04 \x01(\tR\bDeviceId\x12\x1c\n" +
	"\tIpAddress\x18\x05 \x01(\tR\tIpAddress\x12\x1c\n" +
	"\tUserAgent\x18\x06 \x01(\tR\tUserAgent\x128\n" +
	"\tCreatedAt\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\tCreatedAtB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_banner_stats_proto_rawDescOnce sync.Once
	file_pbentity_banner_stats_proto_rawDescData []byte
)

func file_pbentity_banner_stats_proto_rawDescGZIP() []byte {
	file_pbentity_banner_stats_proto_rawDescOnce.Do(func() {
		file_pbentity_banner_stats_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_banner_stats_proto_rawDesc), len(file_pbentity_banner_stats_proto_rawDesc)))
	})
	return file_pbentity_banner_stats_proto_rawDescData
}

var file_pbentity_banner_stats_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_banner_stats_proto_goTypes = []any{
	(*BannerStats)(nil),           // 0: pbentity.BannerStats
	(*timestamppb.Timestamp)(nil), // 1: google.protobuf.Timestamp
}
var file_pbentity_banner_stats_proto_depIdxs = []int32{
	1, // 0: pbentity.BannerStats.CreatedAt:type_name -> google.protobuf.Timestamp
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_pbentity_banner_stats_proto_init() }
func file_pbentity_banner_stats_proto_init() {
	if File_pbentity_banner_stats_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_banner_stats_proto_rawDesc), len(file_pbentity_banner_stats_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_banner_stats_proto_goTypes,
		DependencyIndexes: file_pbentity_banner_stats_proto_depIdxs,
		MessageInfos:      file_pbentity_banner_stats_proto_msgTypes,
	}.Build()
	File_pbentity_banner_stats_proto = out.File
	file_pbentity_banner_stats_proto_goTypes = nil
	file_pbentity_banner_stats_proto_depIdxs = nil
}
