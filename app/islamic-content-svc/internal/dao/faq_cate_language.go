// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// faqCateLanguageDao is the data access object for the table faq_cate_language.
// You can define custom methods on it to extend its functionality as needed.
type faqCateLanguageDao struct {
	*internal.FaqCateLanguageDao
}

var (
	// FaqCateLanguage is a globally accessible object for table faq_cate_language operations.
	FaqCateLanguage = faqCateLanguageDao{internal.NewFaqCateLanguageDao()}
)

// Add your custom methods and functionality below.
