// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// newsArticleDao is the data access object for the table news_article.
// You can define custom methods on it to extend its functionality as needed.
type newsArticleDao struct {
	*internal.NewsArticleDao
}

var (
	// NewsArticle is a globally accessible object for table news_article operations.
	NewsArticle = newsArticleDao{internal.NewNewsArticleDao()}
)

// Add your custom methods and functionality below.
