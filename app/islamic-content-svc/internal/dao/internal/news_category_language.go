// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// NewsCategoryLanguageDao is the data access object for the table news_category_language.
type NewsCategoryLanguageDao struct {
	table    string                      // table is the underlying table name of the DAO.
	group    string                      // group is the database configuration group name of the current DAO.
	columns  NewsCategoryLanguageColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler          // handlers for customized model modification.
}

// NewsCategoryLanguageColumns defines and stores column names for the table news_category_language.
type NewsCategoryLanguageColumns struct {
	Id         string //
	CategoryId string // 资讯分类id
	LanguageId string // 语言id,0-中文，1-英文，2-印尼语
	Name       string // 名称
	CreateTime string // 创建时间
	UpdateTime string // 修改时间
	DeleteTime string // 删除时间
}

// newsCategoryLanguageColumns holds the columns for the table news_category_language.
var newsCategoryLanguageColumns = NewsCategoryLanguageColumns{
	Id:         "id",
	CategoryId: "category_id",
	LanguageId: "language_id",
	Name:       "name",
	CreateTime: "create_time",
	UpdateTime: "update_time",
	DeleteTime: "delete_time",
}

// NewNewsCategoryLanguageDao creates and returns a new DAO object for table data access.
func NewNewsCategoryLanguageDao(handlers ...gdb.ModelHandler) *NewsCategoryLanguageDao {
	return &NewsCategoryLanguageDao{
		group:    "default",
		table:    "news_category_language",
		columns:  newsCategoryLanguageColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *NewsCategoryLanguageDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *NewsCategoryLanguageDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *NewsCategoryLanguageDao) Columns() NewsCategoryLanguageColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *NewsCategoryLanguageDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *NewsCategoryLanguageDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *NewsCategoryLanguageDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
