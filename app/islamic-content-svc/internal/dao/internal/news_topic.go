// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// NewsTopicDao is the data access object for the table news_topic.
type NewsTopicDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  NewsTopicColumns   // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// NewsTopicColumns defines and stores column names for the table news_topic.
type NewsTopicColumns struct {
	Id         string //
	Counts     string // 文章数量
	IsZh       string // 是否中文，0-否，1-是
	IsEn       string // 是否英文，0-否，1-是
	IsId       string // 是否印尼文，0-否，1-是
	Status     string // 是否显示，1启用，0关闭
	Sort       string // 排序，数字越小，排序越靠前
	AdminId    string // 分类负责人id
	TopicImgs  string // 专题图片
	Creater    string // 创建者id
	CreateName string // 创建者
	CreateTime string // 创建时间
	UpdateTime string // 修改时间
	DeleteTime string // 删除时间
}

// newsTopicColumns holds the columns for the table news_topic.
var newsTopicColumns = NewsTopicColumns{
	Id:         "id",
	Counts:     "counts",
	IsZh:       "is_zh",
	IsEn:       "is_en",
	IsId:       "is_id",
	Status:     "status",
	Sort:       "sort",
	AdminId:    "admin_id",
	TopicImgs:  "topic_imgs",
	Creater:    "creater",
	CreateName: "create_name",
	CreateTime: "create_time",
	UpdateTime: "update_time",
	DeleteTime: "delete_time",
}

// NewNewsTopicDao creates and returns a new DAO object for table data access.
func NewNewsTopicDao(handlers ...gdb.ModelHandler) *NewsTopicDao {
	return &NewsTopicDao{
		group:    "default",
		table:    "news_topic",
		columns:  newsTopicColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *NewsTopicDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *NewsTopicDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *NewsTopicDao) Columns() NewsTopicColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *NewsTopicDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *NewsTopicDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *NewsTopicDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
