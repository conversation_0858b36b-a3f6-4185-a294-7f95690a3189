// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// BannerDao is the data access object for table banner.
type BannerDao struct {
	table   string        // table is the underlying table name of the DAO.
	group   string        // group is the database configuration group name of current DAO.
	columns BannerColumns // columns contains all the column names of Table for convenient usage.
}

// BannerColumns defines and stores column names for table banner.
type BannerColumns struct {
	Id          string // 主键ID
	LanguageId  string // 语言ID: 0-中文, 1-英文, 2-印尼语
	Title       string // 广告标题
	Description string // 广告描述
	ImageUrl    string // 广告图片URL
	LinkUrl     string // 跳转链接URL
	SortOrder   string // 排序权重，数字越小越靠前
	Status      string // 状态: 0-禁用, 1-启用
	StartTime   string // 开始时间戳(毫秒)
	EndTime     string // 结束时间戳(毫秒)
	AdminId     string // 创建管理员ID
	CreatedAt   string // 创建时间
	UpdatedAt   string // 更新时间
}

// bannerColumns holds the columns for table banner.
var bannerColumns = BannerColumns{
	Id:          "id",
	LanguageId:  "language_id",
	Title:       "title",
	Description: "description",
	ImageUrl:    "image_url",
	LinkUrl:     "link_url",
	SortOrder:   "sort_order",
	Status:      "status",
	StartTime:   "start_time",
	EndTime:     "end_time",
	AdminId:     "admin_id",
	CreatedAt:   "created_at",
	UpdatedAt:   "updated_at",
}

// NewBannerDao creates and returns a new DAO object for table data access.
func NewBannerDao() *BannerDao {
	return &BannerDao{
		group:   "default",
		table:   "banner",
		columns: bannerColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *BannerDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *BannerDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *BannerDao) Columns() BannerColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *BannerDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *BannerDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *BannerDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
