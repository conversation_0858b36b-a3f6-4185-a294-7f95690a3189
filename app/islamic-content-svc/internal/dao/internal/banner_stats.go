// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// BannerStatsDao is the data access object for table banner_stats.
type BannerStatsDao struct {
	table   string             // table is the underlying table name of the DAO.
	group   string             // group is the database configuration group name of current DAO.
	columns BannerStatsColumns // columns contains all the column names of Table for convenient usage.
}

// BannerStatsColumns defines and stores column names for table banner_stats.
type BannerStatsColumns struct {
	Id        string // 主键ID
	BannerId  string // 广告ID
	UserId    string // 用户ID，0表示未登录用户
	DeviceId  string // 设备唯一标识
	IpAddress string // IP地址
	UserAgent string // 用户代理信息
	CreatedAt string // 操作时间
}

// bannerStatsColumns holds the columns for table banner_stats.
var bannerStatsColumns = BannerStatsColumns{
	Id:        "id",
	BannerId:  "banner_id",
	UserId:    "user_id",
	DeviceId:  "device_id",
	IpAddress: "ip_address",
	UserAgent: "user_agent",
	CreatedAt: "created_at",
}

// NewBannerStatsDao creates and returns a new DAO object for table data access.
func NewBannerStatsDao() *BannerStatsDao {
	return &BannerStatsDao{
		group:   "default",
		table:   "banner_stats",
		columns: bannerStatsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *BannerStatsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *BannerStatsDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *BannerStatsDao) Columns() BannerStatsColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *BannerStatsDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *BannerStatsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *BannerStatsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
