// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// suratDaftarDao is the data access object for the table surat_daftar.
// You can define custom methods on it to extend its functionality as needed.
type suratDaftarDao struct {
	*internal.SuratDaftarDao
}

var (
	// SuratDaftar is a globally accessible object for table surat_daftar operations.
	SuratDaftar = suratDaftarDao{internal.NewSuratDaftarDao()}
)

// Add your custom methods and functionality below.
