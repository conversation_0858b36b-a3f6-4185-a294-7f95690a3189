// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// newsTopicLanguageDao is the data access object for the table news_topic_language.
// You can define custom methods on it to extend its functionality as needed.
type newsTopicLanguageDao struct {
	*internal.NewsTopicLanguageDao
}

var (
	// NewsTopicLanguage is a globally accessible object for table news_topic_language operations.
	NewsTopicLanguage = newsTopicLanguageDao{internal.NewNewsTopicLanguageDao()}
)

// Add your custom methods and functionality below.
