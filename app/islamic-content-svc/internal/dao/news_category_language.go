// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// newsCategoryLanguageDao is the data access object for the table news_category_language.
// You can define custom methods on it to extend its functionality as needed.
type newsCategoryLanguageDao struct {
	*internal.NewsCategoryLanguageDao
}

var (
	// NewsCategoryLanguage is a globally accessible object for table news_category_language operations.
	NewsCategoryLanguage = newsCategoryLanguageDao{internal.NewNewsCategoryLanguageDao()}
)

// Add your custom methods and functionality below.
