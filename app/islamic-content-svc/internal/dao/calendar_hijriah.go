// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// calendarHijriahDao is the data access object for the table calendar_hijriah.
// You can define custom methods on it to extend its functionality as needed.
type calendarHijriahDao struct {
	*internal.CalendarHijriahDao
}

var (
	// CalendarHijriah is a globally accessible object for table calendar_hijriah operations.
	CalendarHijriah = calendarHijriahDao{internal.NewCalendarHijriahDao()}
)

// Add your custom methods and functionality below.
