// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// faqQuestionDao is the data access object for the table faq_question.
// You can define custom methods on it to extend its functionality as needed.
type faqQuestionDao struct {
	*internal.FaqQuestionDao
}

var (
	// FaqQuestion is a globally accessible object for table faq_question operations.
	FaqQuestion = faqQuestionDao{internal.NewFaqQuestionDao()}
)

// Add your custom methods and functionality below.
