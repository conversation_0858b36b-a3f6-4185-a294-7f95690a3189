// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// newsArticleLanguageDao is the data access object for the table news_article_language.
// You can define custom methods on it to extend its functionality as needed.
type newsArticleLanguageDao struct {
	*internal.NewsArticleLanguageDao
}

var (
	// NewsArticleLanguage is a globally accessible object for table news_article_language operations.
	NewsArticleLanguage = newsArticleLanguageDao{internal.NewNewsArticleLanguageDao()}
)

// Add your custom methods and functionality below.
