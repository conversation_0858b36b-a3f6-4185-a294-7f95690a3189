// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// internalSurahReadCollectDao is internal type for wrapping internal DAO implements.
type internalSurahReadCollectDao = *internal.SurahReadCollectDao

// surahReadCollectDao is the data access object for table surah_read_collect.
// You can define custom methods on it to extend its functionality as you wish.
type surahReadCollectDao struct {
	internalSurahReadCollectDao
}

var (
	// SurahReadCollect is globally public accessible object for table surah_read_collect operations.
	SurahReadCollect = surahReadCollectDao{
		internal.NewSurahReadCollectDao(),
	}
)

// Fill with you ideas below.
