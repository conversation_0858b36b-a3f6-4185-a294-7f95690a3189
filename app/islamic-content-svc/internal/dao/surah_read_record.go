// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// internalSurahReadRecordDao is internal type for wrapping internal DAO implements.
type internalSurahReadRecordDao = *internal.SurahReadRecordDao

// surahReadRecordDao is the data access object for table surah_read_record.
// You can define custom methods on it to extend its functionality as you wish.
type surahReadRecordDao struct {
	internalSurahReadRecordDao
}

var (
	// SurahReadRecord is globally public accessible object for table surah_read_record operations.
	SurahReadRecord = surahReadRecordDao{
		internal.NewSurahReadRecordDao(),
	}
)

// Fill with you ideas below.
