// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// bannerDao is the data access object for the table banner.
// You can define custom methods on it to extend its functionality as needed.
type bannerDao struct {
	*internal.BannerDao
}

var (
	// Banner is a globally accessible object for table banner operations.
	Banner = bannerDao{internal.NewBannerDao()}
)

// Add your custom methods and functionality below.
