package islamic

import (
	"context"
	"github.com/gogf/gf/contrib/rpc/grpcx/v2"
	"github.com/gogf/gf/v2/util/gconv"
	v1 "halalplus/app/islamic-content-svc/api/islamic/v1"
	"halalplus/app/islamic-content-svc/api/pbentity"
	"halalplus/app/islamic-content-svc/internal/model"
	"halalplus/app/islamic-content-svc/internal/service"
	"sort"
)

type Controller struct {
	v1.UnimplementedSurahServiceServer
}

type ControllerNews struct {
	v1.UnimplementedNewsServiceServer
}

func Register(s *grpcx.GrpcServer) {
	v1.RegisterSurahServiceServer(s.Server, &Controller{})
	v1.RegisterNewsServiceServer(s.Server, &ControllerNews{})
	v1.RegisterBannerServiceServer(s.Server, &ControllerBanner{})
	v1.RegisterCalendarServiceServer(s.Server, &ControllerCalendar{})
	v1.RegisterFaqServiceServer(s.Server, &ControllerFaq{})
	v1.RegisterPrayerServiceServer(s.Server, &ControllerPrayer{})
}

func (*Controller) SurahList(ctx context.Context, req *v1.SurahListReq) (res *v1.SurahListRes, err error) {
	res = &v1.SurahListRes{}
	res.Code = 200
	res.Msg = "success"
	surahParamInput := &model.SurahParamInput{
		Id:        gconv.Uint(req.Id),
		Name:      req.Name,
		IsPopular: uint(req.IsPopular),
	}
	surahList := service.Islamic().SurahList(ctx, surahParamInput)

	res.Data = make([]*pbentity.SuratDaftar, 0, len(surahList)) // 初始化切片，预分配空间
	for _, surah := range surahList {
		res.Data = append(res.Data, &pbentity.SuratDaftar{
			Id:          int32(gconv.Uint32(surah.Id)),
			NamaLatin:   surah.NameLatin,
			Nama:        surah.Name,
			JumlahAyat:  int32(surah.JumlahAyat),
			Arti:        surah.Arti,
			Nomor:       int32(surah.Nomor),
			TempatTurun: surah.TempatTurun,
		})
	}
	return res, nil
}

func (*Controller) JuzList(ctx context.Context, req *v1.JuzListReq) (res *v1.JuzListRes, err error) {
	res = &v1.JuzListRes{}
	res.Code = 200
	res.Msg = "success"
	JuzParamInput := &model.JuzParamInput{
		Name: req.Name,
	}
	juzList := service.Islamic().JuzList(ctx, JuzParamInput)

	Data := make([]*v1.JuzInfo, 0, 30)
	for _, juz := range juzList {
		Data = append(Data, &v1.JuzInfo{
			StartSurahId:   uint32(juz.StartSurahId),
			StartSurahName: juz.StartSurahName,
			EndSurahId:     uint32(juz.EndSurahId),
			EndSurahName:   juz.EndSurahName,
			StartAyahId:    uint32(juz.StartAyahId),
			EndAyahId:      uint32(juz.EndAyahId),
			Juz:            juz.Name,
			FirstWord:      juz.FirstWord,
		})
	}
	//Data 排序
	sort.Slice(Data, func(i, j int) bool {
		return Data[i].StartSurahId < Data[j].StartSurahId
	})
	res.Data = Data
	return res, nil

}

func (*Controller) AyahList(ctx context.Context, req *v1.AyahListReq) (res *v1.AyahListRes, err error) {

	res = &v1.AyahListRes{}
	res.Code = 200
	res.Msg = "success"
	JuzParamInput := &model.AyahParamInput{
		Id:      req.Id,
		SurahId: req.SurahId,
		JuzId:   req.JuzId,
		Page:    req.Page,
	}
	juzList := service.Islamic().AyahList(ctx, JuzParamInput)

	res.Data = make([]*pbentity.SuratAyat, 0, len(juzList)) // 初始化切片，预分配空间
	for _, surah := range juzList {
		res.Data = append(res.Data, &pbentity.SuratAyat{
			Id:      int32(surah.Id),
			AyatId:  int32(surah.Id),
			SurahId: int32(surah.SurahId),
			Nomor:   int32(surah.Nomor),
			Ar:      surah.Ar,
			Tr:      surah.Tr,
			Idn:     surah.Idn,
			Page:    int32(surah.Page),
			Juz:     int32(surah.Juz),
		})
	}
	return res, nil

}

// 阅读记录
func (*Controller) AyahReadRecord(ctx context.Context, req *v1.AyahReadRecordReq) (res *v1.AyahReadRecordRes, err error) {

	res = &v1.AyahReadRecordRes{}
	res.Code = 200
	res.Msg = "success"
	JuzParamInput := &model.AyahReadRecordInput{
		AyahId:   req.AyahId,
		IsUserOp: req.IsUserOp,
	}
	service.Islamic().AyahReadRecord(ctx, JuzParamInput)
	return res, nil

}
func (*Controller) AyahReadRecordList(ctx context.Context, req *v1.AyahReadRecordListReq) (res *v1.AyahReadRecordListRes, err error) {

	res = &v1.AyahReadRecordListRes{}
	res.Code = 200
	res.Msg = "success"
	JuzParamInput := &model.AyahReadRecordListInput{}
	juzList := service.Islamic().AyahReadRecordList(ctx, JuzParamInput)

	res.Data = make([]*v1.ReadInfo, 0, len(juzList)) // 初始化切片，预分配空间
	for _, surah := range juzList {
		res.Data = append(res.Data, &v1.ReadInfo{
			SurahId:   uint32(surah.SurahId),
			SurahName: surah.SurahName,
			AyahId:    uint32(surah.AyahId),
			JuzId:     uint32(surah.JuzId),
		})
	}
	return res, nil

}

// 阅读收藏
func (*Controller) AyahReadCollect(ctx context.Context, req *v1.AyahReadCollectReq) (res *v1.AyahReadCollectRes, err error) {

	res = &v1.AyahReadCollectRes{}
	res.Code = 200
	res.Msg = "success"
	JuzParamInput := &model.AyahReadCollectInput{
		AyahId: req.AyahId,
	}
	service.Islamic().AyahReadCollect(ctx, JuzParamInput)
	return res, nil
}
func (*Controller) CheckAyahReadCollectStatus(ctx context.Context, req *v1.CheckAyahReadCollectStatusReq) (res *v1.CheckAyahReadCollectStatusRes, err error) {

	res = &v1.CheckAyahReadCollectStatusRes{}
	res.Code = 200
	res.Msg = "success"
	JuzParamInput := &model.CheckAyahReadCollectStatusInput{
		AyahId: req.AyahId,
	}
	CollectStatus := service.Islamic().CheckAyahReadCollectStatus(ctx, JuzParamInput)
	res.IsCollect = int32(CollectStatus.IsCollect)
	return res, nil

}

// 阅读记录列表
func (*Controller) AyahReadCollectList(ctx context.Context, req *v1.AyahReadCollectListReq) (res *v1.AyahReadCollectListRes, err error) {

	res = &v1.AyahReadCollectListRes{}
	res.Code = 200
	res.Msg = "success"
	JuzParamInput := &model.AyahReadCollectListInput{}
	juzList := service.Islamic().AyahReadCollectList(ctx, JuzParamInput)

	res.Data = make([]*v1.ReadInfo, 0, len(juzList)) // 初始化切片，预分配空间
	for _, surah := range juzList {
		res.Data = append(res.Data, &v1.ReadInfo{
			SurahId:   uint32(surah.SurahId),
			SurahName: surah.SurahName,
			AyahId:    uint32(surah.AyahId),
			JuzId:     uint32(surah.JuzId),
		})
	}
	return res, nil

}

//func (*Controller) SurahInfo(ctx context.Context, req *v1.SurahInfoReq) (res *v1.SurahInfoRes, err error) {
//
//	res = &v1.SurahInfoRes{}
//	res.Code = 200
//	res.Msg = "success"
//	res.Data = &pbentity.SuratDaftar{
//		Id:          1,
//		Nama:        "212323",
//		Nomor:       1,
//		NamaLatin:   "Pembukaan",
//		JumlahAyat:  4,
//		TempatTurun: "Pembukaan",
//		Arti:        "Pembukaan",
//		Deskripsi:   "Pembukaan",
//		Audio:       "https://equran.nos.wjv-1.neo.id/audio-full/Misyari-Rasyid-Al-Afasi/012.mp3",
//		Status:      1,
//	}
//	return res, nil
//}
//
//func (*Controller) SurahDesc(ctx context.Context, req *v1.SurahDescReq) (res *v1.SurahDescRes, err error) {
//
//	res = &v1.SurahDescRes{}
//	res.Code = 200
//	res.Msg = "success"
//	res.Data = &pbentity.SuratTafsir{
//		Id:        1,
//		TafsirId:  1,
//		SurahId:   1,
//		AyatNomor: 1,
//		Tafsir:    "Pembukaan",
//	}
//	return res, nil
//}

func (*ControllerNews) NewsCategoryList(ctx context.Context, req *v1.NewsCategoryListReq) (res *v1.NewsCategoryListRes, err error) {

	res = &v1.NewsCategoryListRes{}
	res.Code = 200
	res.Msg = "success"
	ParamInput := &model.NewsCategoryListInput{
		LanguageId: req.LanguageId,
		Pid:        req.Pid,
	}
	CategoryList := service.Islamic().NewsCategoryList(ctx, ParamInput)

	res.Data = make([]*v1.CategoryInfo, 0, len(CategoryList)) // 初始化切片，预分配空间
	for _, cate := range CategoryList {
		res.Data = append(res.Data, &v1.CategoryInfo{
			Id:         cate.Id,
			ParentId:   cate.ParentId,
			LanguageId: cate.LanguageId,
			Name:       cate.Name,
			CoverImgs:  cate.CoverImgs,
		})
	}
	return res, nil
}

func (*ControllerNews) NewsListByCateId(ctx context.Context, req *v1.NewsListByCateIdReq) (res *v1.NewsListByCateIdRes, err error) {

	res = &v1.NewsListByCateIdRes{}
	//res.Code = 200
	//res.Msg = "success"
	//Data := make([]*v1.ArticleInfo, 0)
	//Data = append(Data, &v1.ArticleInfo{
	//	ArticleId:  1,
	//	LanguageId: 1,
	//	Name:       "Latest News",
	//	Content:    "This is the content of the latest news article.",
	//	Data: &pbentity.NewsArticle{
	//		Id:          1,
	//		CategoryId:  1,
	//		Author:      "Admin",
	//		CoverImgs:   "https://example.com/news-cover.jpg",
	//		IsTop:       1,
	//		IsRecommend: 1,
	//	},
	//})
	//res.Data = Data
	return res, nil
}

func (*ControllerNews) NewsTopicList(ctx context.Context, req *v1.NewsTopicListReq) (res *v1.NewsTopicListRes, err error) {

	res = &v1.NewsTopicListRes{}
	res.Code = 200
	res.Msg = "success"
	ParamInput := &model.NewsTopicListInput{
		LanguageId: req.LanguageId,
	}
	TopicList := service.Islamic().NewsTopicList(ctx, ParamInput)

	res.Data = make([]*v1.TopicInfo, 0, len(TopicList)) // 初始化切片，预分配空间
	for _, cate := range TopicList {
		res.Data = append(res.Data, &v1.TopicInfo{
			TopicId:    cate.Id,
			LanguageId: cate.LanguageId,
			Name:       cate.Name,
			ShortName:  cate.ShortName,
			TopicImgs:  cate.TopicImgs,
		})
	}
	return res, nil

}

func (*ControllerNews) NewsListByTopicId(ctx context.Context, req *v1.NewsListByTopicIdReq) (res *v1.NewsListByTopicIdRes, err error) {

	res = &v1.NewsListByTopicIdRes{}
	//res.Code = 200
	//res.Msg = "success"
	//Data := make([]*v1.ArticleInfo, 0)
	//Data = append(Data, &v1.ArticleInfo{
	//	ArticleId:  1,
	//	LanguageId: 1,
	//	Name:       "Latest News",
	//	Content:    "This is the content of the latest news article.",
	//	Data: &pbentity.NewsArticle{
	//		Id:          1,
	//		CategoryId:  1,
	//		Author:      "Admin",
	//		CoverImgs:   "https://example.com/news-cover.jpg",
	//		IsTop:       1,
	//		IsRecommend: 1,
	//	},
	//})
	//res.Data = Data
	return res, nil
}
