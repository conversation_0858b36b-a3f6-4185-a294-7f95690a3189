package islamic

import (
	"context"
	v1 "halalplus/app/islamic-content-svc/api/islamic/v1"
	"halalplus/app/islamic-content-svc/internal/model"
	"halalplus/app/islamic-content-svc/internal/service"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

type ControllerPrayer struct {
	v1.UnimplementedPrayerServiceServer
}

// GetDailyPrayerTimes 获取每日祷告时间
func (*ControllerPrayer) GetDailyPrayerTime(ctx context.Context, req *v1.GetDailyPrayerTimeReq) (res *v1.GetDailyPrayerTimeRes, err error) {
	dateTime, err := time.Parse("2006-01-02", req.Date)
	if err != nil {
		g.Log().Error(ctx, "invalid date format:", err)
		return nil, err
	}

	input := &model.DailyPrayerTimeInput{
		Year:           dateTime.Year(),
		Month:          int(dateTime.Month()),
		Day:            dateTime.Day(),
		Latitude:       req.Latitude,
		Longitude:      req.Longitude,
		Timezone:       req.Timezone,
		MethodCode:     req.MethodCode,
		DateAdjustment: int(req.DateAdjustment),
	}

	output, err := service.Prayer().GetDailyPrayerTime(ctx, input)
	if err != nil {
		g.Log().Error(ctx, "GetPrayerTimes error:", err)
		return nil, err
	}

	var prayerTimesData *v1.PrayerTimeData
	if err = gconv.Struct(output, &prayerTimesData); err != nil {
		g.Log().Error(ctx, "GetPrayerTimes error:", err)
		return nil, err
	}

	return &v1.GetDailyPrayerTimeRes{
		Code: 200,
		Msg:  "success",
		Data: prayerTimesData,
	}, nil
}

// GetMonthlyPrayerTimes 获取月度祷告时间
func (*ControllerPrayer) GetMonthlyPrayerTimes(ctx context.Context, req *v1.GetMonthlyPrayerTimesReq) (res *v1.GetMonthlyPrayerTimesRes, err error) {

	input := &model.MonthlyPrayerTimesInput{
		Year:           int(req.Year),
		Month:          int(req.Month),
		Latitude:       req.Latitude,
		Longitude:      req.Longitude,
		Timezone:       req.Timezone,
		MethodCode:     req.MethodCode,
		DateAdjustment: int(req.DateAdjustment),
	}

	output, err := service.Prayer().GetMonthlyPrayerTimes(ctx, input)
	if err != nil {
		g.Log().Error(ctx, "GetPrayerTimes error:", err)
		return nil, err
	}

	var timeList []*v1.PrayerTimeData
	if err = gconv.Structs(output, &timeList); err != nil {
		g.Log().Error(ctx, "GetPrayerTimes error:", err)
		return nil, err
	}

	return &v1.GetMonthlyPrayerTimesRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.MonthlyPrayerTimesData{
			List: timeList,
		},
	}, nil
}
