// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	"halalplus/app/islamic-content-svc/internal/model"
)

type (
	ICalendar interface {
		// GetCalendar 获取日历数据
		GetCalendar(ctx context.Context, input *model.CalendarGetInput) ([]*model.CalendarDateInfo, error)
		// ConvertToIslamicDate 转换公历日期为伊斯兰历
		ConvertToIslamicDate(ctx context.Context, gregorianDate string, methodCode string, adjustment int32) (*model.IslamicDate, error)
	}
)

var (
	localCalendar ICalendar
)

func Calendar() ICalendar {
	if localCalendar == nil {
		panic("implement not found for interface ICalendar, forgot register?")
	}
	return localCalendar
}

func RegisterCalendar(i ICalendar) {
	localCalendar = i
}
