// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// SuratAyat is the golang structure for table surat_ayat.
type SuratAyat struct {
	Id        int         `json:"id"        orm:"id"         description:""`          //
	AyatId    int         `json:"ayatId"    orm:"ayat_id"    description:"经文全局ID"`    // 经文全局ID
	SurahId   int         `json:"surahId"   orm:"surah_id"   description:"所属章节ID"`    // 所属章节ID
	Nomor     int         `json:"nomor"     orm:"nomor"      description:"经文在章节中的编号"` // 经文在章节中的编号
	Ar        string      `json:"ar"        orm:"ar"         description:"阿拉伯语经文"`    // 阿拉伯语经文
	Tr        string      `json:"tr"        orm:"tr"         description:"音译文本"`      // 音译文本
	Idn       string      `json:"idn"       orm:"idn"        description:"印尼语翻译"`     // 印尼语翻译
	CreatedAt *gtime.Time `json:"createdAt" orm:"created_at" description:""`          //
	UpdatedAt *gtime.Time `json:"updatedAt" orm:"updated_at" description:""`          //
	Juz       int         `json:"juz"       orm:"juz"        description:"juz编号"`     // juz编号
	Page      int         `json:"page"      orm:"page"       description:"所在页码"`      // 所在页码
}
