// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// NewsArticleLanguage is the golang structure for table news_article_language.
type NewsArticleLanguage struct {
	Id         uint   `json:"id"         orm:"id"          description:""`                     //
	ArticleId  uint   `json:"articleId"  orm:"article_id"  description:"文章id"`                 // 文章id
	LanguageId uint   `json:"languageId" orm:"language_id" description:"语言id,0-中文，1-英文，2-印尼语"` // 语言id,0-中文，1-英文，2-印尼语
	Name       string `json:"name"       orm:"name"        description:"名称"`                   // 名称
	Content    string `json:"content"    orm:"content"     description:"正文"`                   // 正文
	CreateTime int64  `json:"createTime" orm:"create_time" description:"创建时间"`                 // 创建时间
	UpdateTime int64  `json:"updateTime" orm:"update_time" description:"修改时间"`                 // 修改时间
	DeleteTime int64  `json:"deleteTime" orm:"delete_time" description:"删除时间"`                 // 删除时间
}
