// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// CalendarHijriah is the golang structure for table calendar_hijriah.
type CalendarHijriah struct {
	Id             int64       `json:"id"             orm:"id"              description:"主键ID"`                                                    // 主键ID
	GregorianYear  int         `json:"gregorianYear"  orm:"gregorian_year"  description:"公历年"`                                                     // 公历年
	GregorianMonth int         `json:"gregorianMonth" orm:"gregorian_month" description:"公历月"`                                                     // 公历月
	GregorianDay   int         `json:"gregorianDay"   orm:"gregorian_day"   description:"公历日"`                                                     // 公历日
	HijriahYear    int         `json:"hijriahYear"    orm:"hijriah_year"    description:"Hijriah年"`                                                // Hijriah年
	HijriahMonth   int         `json:"hijriahMonth"   orm:"hijriah_month"   description:"Hijriah月"`                                                // Hijriah月
	HijriahDay     int         `json:"hijriahDay"     orm:"hijriah_day"     description:"Hijriah日"`                                                // Hijriah日
	MethodCode     string      `json:"methodCode"     orm:"method_code"     description:"计算方法代码，如：LFNU, UMMUL_QURA"`                               // 计算方法代码，如：LFNU, UMMUL_QURA
	Weekday        int         `json:"weekday"        orm:"weekday"         description:"星期：0-6 (0=Sunday, 1=Monday, ..., 6=Saturday)"`            // 星期：0-6 (0=Sunday, 1=Monday, ..., 6=Saturday)
	Pasaran        int         `json:"pasaran"        orm:"pasaran"         description:"Pasaran：0-4 (0=Kliwon, 1=Legi, 2=Pahing, 3=Pon, 4=Wage)"` // Pasaran：0-4 (0=Kliwon, 1=Legi, 2=Pahing, 3=Pon, 4=Wage)
	CreatedAt      *gtime.Time `json:"createdAt"      orm:"created_at"      description:"创建时间"`                                                    // 创建时间
	UpdatedAt      *gtime.Time `json:"updatedAt"      orm:"updated_at"      description:"更新时间"`                                                    // 更新时间
}
