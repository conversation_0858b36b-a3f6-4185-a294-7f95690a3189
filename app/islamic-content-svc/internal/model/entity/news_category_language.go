// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// NewsCategoryLanguage is the golang structure for table news_category_language.
type NewsCategoryLanguage struct {
	Id         uint   `json:"id"         orm:"id"          description:""`                     //
	CategoryId uint   `json:"categoryId" orm:"category_id" description:"资讯分类id"`               // 资讯分类id
	LanguageId uint   `json:"languageId" orm:"language_id" description:"语言id,0-中文，1-英文，2-印尼语"` // 语言id,0-中文，1-英文，2-印尼语
	Name       string `json:"name"       orm:"name"        description:"名称"`                   // 名称
	CreateTime int64  `json:"createTime" orm:"create_time" description:"创建时间"`                 // 创建时间
	UpdateTime int64  `json:"updateTime" orm:"update_time" description:"修改时间"`                 // 修改时间
	DeleteTime int64  `json:"deleteTime" orm:"delete_time" description:"删除时间"`                 // 删除时间
}
