// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// NewsTopicLanguage is the golang structure for table news_topic_language.
type NewsTopicLanguage struct {
	Id         uint   `json:"id"         orm:"id"          description:""`                     //
	TopicId    uint   `json:"topicId"    orm:"topic_id"    description:"专题id"`                 // 专题id
	LanguageId uint   `json:"languageId" orm:"language_id" description:"语言id,0-中文，1-英文，2-印尼语"` // 语言id,0-中文，1-英文，2-印尼语
	Name       string `json:"name"       orm:"name"        description:"名称"`                   // 名称
	ShortName  string `json:"shortName"  orm:"short_name"  description:"名称"`                   // 名称
	CreateTime int64  `json:"createTime" orm:"create_time" description:"创建时间"`                 // 创建时间
	UpdateTime int64  `json:"updateTime" orm:"update_time" description:"修改时间"`                 // 修改时间
	DeleteTime int64  `json:"deleteTime" orm:"delete_time" description:"删除时间"`                 // 删除时间
}
