// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// NewsTopicArticle is the golang structure of table news_topic_article for DAO operations like Where/Data.
type NewsTopicArticle struct {
	g.Meta      `orm:"table:news_topic_article, do:true"`
	Id          interface{} //
	TopicId     interface{} // topic id
	ArticleId   interface{} // 文章id
	ArticleName interface{} // 文章name
	CreateTime  interface{} // 创建时间
	UpdateTime  interface{} // 修改时间
	DeleteTime  interface{} // 删除时间
}
