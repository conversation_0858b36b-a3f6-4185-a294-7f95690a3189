// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// FaqCate is the golang structure of table faq_cate for DAO operations like Where/Data.
type FaqCate struct {
	g.Meta        `orm:"table:faq_cate, do:true"`
	Id            interface{} //
	IsOpen        interface{} // 状态 [ 1 启用  2 禁用]
	Sort          interface{} // 排序
	CateCount     interface{} // 分类下的文章总数
	CreatedAt     *gtime.Time // 创建时间
	CreateAccount interface{} // 创建者
	UpdatedAt     *gtime.Time // 更新时间
	UpdateAccount interface{} // 更新者
}
