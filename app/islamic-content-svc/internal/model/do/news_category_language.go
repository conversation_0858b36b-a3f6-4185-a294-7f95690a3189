// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// NewsCategoryLanguage is the golang structure of table news_category_language for DAO operations like Where/Data.
type NewsCategoryLanguage struct {
	g.Meta     `orm:"table:news_category_language, do:true"`
	Id         interface{} //
	CategoryId interface{} // 资讯分类id
	LanguageId interface{} // 语言id,0-中文，1-英文，2-印尼语
	Name       interface{} // 名称
	CreateTime interface{} // 创建时间
	UpdateTime interface{} // 修改时间
	DeleteTime interface{} // 删除时间
}
