// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// SuratTafsir is the golang structure of table surat_tafsir for DAO operations like Where/Data.
type SuratTafsir struct {
	g.Meta    `orm:"table:surat_tafsir, do:true"`
	Id        interface{} //
	TafsirId  interface{} // 注释全局ID
	SurahId   interface{} // 所属章节ID
	AyatNomor interface{} // 对应经文编号
	Tafsir    interface{} // 注释内容
	CreatedAt *gtime.Time //
	UpdatedAt *gtime.Time //
}
