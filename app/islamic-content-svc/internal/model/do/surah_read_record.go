// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// SurahReadRecord is the golang structure of table surah_read_record for DAO operations like Where/Data.
type SurahReadRecord struct {
	g.Meta     `orm:"table:surah_read_record, do:true"`
	Id         interface{} //
	UserId     interface{} // 用户id
	AyahId     interface{} // ayah_id节id
	SurahName  interface{} // 名称
	IsUserOp   interface{} // 是否用户操作，1是 0否
	CreateTime interface{} // 创建时间（注册时间）
	UpdateTime interface{} // 更新时间，0代表创建后未更新
}
