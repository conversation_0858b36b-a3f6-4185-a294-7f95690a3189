// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// NewsArticleLanguage is the golang structure of table news_article_language for DAO operations like Where/Data.
type NewsArticleLanguage struct {
	g.Meta     `orm:"table:news_article_language, do:true"`
	Id         interface{} //
	ArticleId  interface{} // 文章id
	LanguageId interface{} // 语言id,0-中文，1-英文，2-印尼语
	Name       interface{} // 名称
	Content    interface{} // 正文
	CreateTime interface{} // 创建时间
	UpdateTime interface{} // 修改时间
	DeleteTime interface{} // 删除时间
}
