// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// NewsTopicLanguage is the golang structure of table news_topic_language for DAO operations like Where/Data.
type NewsTopicLanguage struct {
	g.Meta     `orm:"table:news_topic_language, do:true"`
	Id         interface{} //
	TopicId    interface{} // 专题id
	LanguageId interface{} // 语言id,0-中文，1-英文，2-印尼语
	Name       interface{} // 名称
	ShortName  interface{} // 名称
	CreateTime interface{} // 创建时间
	UpdateTime interface{} // 修改时间
	DeleteTime interface{} // 删除时间
}
