package islamic

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	v1 "halalplus/app/islamic-content-svc/api/islamic/v1"
	"halalplus/app/islamic-content-svc/internal/dao"
	"halalplus/app/islamic-content-svc/internal/model"
	"halalplus/app/islamic-content-svc/internal/model/do"
	"halalplus/app/islamic-content-svc/internal/model/entity"
	"halalplus/app/islamic-content-svc/internal/service"
	token "halalplus/utility/token"
	"strconv"
)

type (
	sIslamic struct {
		//signInMsg<PERSON>han        chan *model.SignInLogInput
		//attrsBatchUpdateChan chan *model.AttrsToUpdate
		//attrsNoDelayChan     chan *model.AttrsToUpdate
		//quit                 chan struct{}
		//
		//ConfigCaptcha    string
		//signInRecord<PERSON>han chan *do.UserSigninLog
		//accountSet       *bloom.Filter
		//transferSet      *bloom.Filter
		//phoneSet         *bloom.Filter
	}
)

func init() {
	service.RegisterIslamic(New())
}

func New() service.IIslamic {
	u := &sIslamic{
		//signInMsgChan:        make(chan *model.SignInLogInput, 1000),
		//attrsBatchUpdateChan: make(chan *model.AttrsToUpdate, 10000),
		//attrsNoDelayChan:     make(chan *model.AttrsToUpdate, 100),
		//
		//signInRecordChan: make(chan *do.UserSigninLog, 1000),
		//quit:             make(chan struct{}, 1),
	}
	//u.cronJobCreateSignInLog()
	//u.cronJobUpdateUserAttrs()
	//u.cronJobUpdateUserAttrsNoDelay()

	//u.ConfigCaptcha = u.getSmsOptConfig(context.Background())

	//g.Go(gctx.New(), func(ctx context.Context) {
	//	time.Sleep(3 * time.Second)
	//	// u.convertTopLevelDomain(ctx)
	//}, nil)
	return u
}

func (s *sIslamic) SurahList(ctx context.Context, in *model.SurahParamInput) (out []*model.SurahParamOutput) {
	var quranList []*entity.SuratDaftar
	query := dao.SuratDaftar.Ctx(ctx)
	if in.Id > 0 {
		query = query.Where(dao.SuratDaftar.Columns().Id, in.Id)
	}
	if in.IsPopular > 0 {
		query = query.Where(dao.SuratDaftar.Columns().IsPopular, in.IsPopular)
	}
	if in.Name != "" {
		query = query.WhereLike(dao.SuratDaftar.Columns().NamaLatin, "%"+in.Name+"%")
	}
	err := query.Scan(&quranList)
	if err != nil || len(quranList) == 0 {
		return out
	}
	for _, quran := range quranList {
		one := &model.SurahParamOutput{
			Id:          uint(quran.Id),
			Name:        quran.Nama,
			NameLatin:   quran.NamaLatin,
			JumlahAyat:  quran.JumlahAyat,
			Arti:        quran.Arti,
			TempatTurun: quran.TempatTurun,
			Nomor:       quran.Nomor,
		}
		out = append(out, one)
	}
	return out
}

func (s *sIslamic) JuzList(ctx context.Context, in *model.JuzParamInput) (out []*model.JuzParamOutput) {

	var surahList []*entity.SuratDaftar
	querySurah := dao.SuratDaftar.Ctx(ctx)
	errSurah := querySurah.Scan(&surahList)
	if errSurah != nil || len(surahList) == 0 {
		return out
	}
	// 初始化一个map 获取所有的SurahId和SurahName
	surahMap := make(map[int]*model.SurahParamOutput)
	for _, surah := range surahList {
		surahMap[surah.Id] = &model.SurahParamOutput{
			Name:      surah.Nama,
			NameLatin: surah.NamaLatin,
		}
	}

	var ayahList []*entity.SuratAyat
	query := dao.SuratAyat.Ctx(ctx).WhereGT(dao.SuratAyat.Columns().Juz, 0)

	if in.Name != "" {
	}
	err := query.Scan(&ayahList)
	if err != nil || len(ayahList) == 0 {
		return out
	}

	//根据quran.Juz进行分组
	juzMap := make(map[int]*v1.JuzInfo)
	for _, quran := range ayahList {
		if _, ok := juzMap[quran.Juz]; !ok {
			juzMap[quran.Juz] = &v1.JuzInfo{
				StartSurahId:   uint32(quran.SurahId),
				StartSurahName: surahMap[quran.SurahId].NameLatin,
				EndSurahId:     uint32(quran.SurahId),
				EndSurahName:   surahMap[quran.SurahId].NameLatin,
				StartAyahId:    uint32(quran.AyatId),
				EndAyahId:      uint32(quran.AyatId),
				FirstWord:      quran.Ar,
			}
		} else {
			EndSurahName := surahMap[quran.SurahId].NameLatin
			//更新EndSurahId和EndAyahId
			juzMap[quran.Juz].EndSurahId = uint32(quran.SurahId)
			juzMap[quran.Juz].EndSurahName = EndSurahName
			juzMap[quran.Juz].EndAyahId = uint32(quran.AyatId)
		}
	}
	//juzMap 排序
	for key, juz := range juzMap {
		out = append(out, &model.JuzParamOutput{
			StartSurahId:   uint(juz.StartSurahId),
			StartSurahName: juz.StartSurahName,
			EndSurahId:     uint(juz.EndSurahId),
			EndSurahName:   juz.EndSurahName,
			StartAyahId:    uint(juz.StartAyahId),
			EndAyahId:      uint(juz.EndAyahId),
			Name:           "Juz " + strconv.Itoa(key), // 这里可以根据需要设置Juz的名称
			FirstWord:      juz.FirstWord,
		})
	}
	return out
}

func (s *sIslamic) QuerySurahByAyahId(ayahId int) (out *entity.SuratDaftar) {
	// 查询SurahId
	var surahAyah *entity.SuratAyat
	err := dao.SuratAyat.Ctx(context.Background()).Where(dao.SuratAyat.Columns().Id, ayahId).Scan(&surahAyah)
	if err != nil || surahAyah == nil {
		return nil
	}
	// 查询Surah信息
	var surah *entity.SuratDaftar
	err = dao.SuratDaftar.Ctx(context.Background()).Where(dao.SuratDaftar.Columns().Id, surahAyah.SurahId).Scan(&surah)
	if err != nil || surah == nil {
		return nil
	}
	return surah
}

func (s *sIslamic) QueryAyahByAyahId(ayahId int) (out *entity.SuratAyat) {
	// 查询SurahId
	var surahAyah *entity.SuratAyat
	err := dao.SuratAyat.Ctx(context.Background()).Where(dao.SuratAyat.Columns().Id, ayahId).Scan(&surahAyah)
	if err != nil || surahAyah == nil {
		return nil
	}
	return surahAyah
}

func (s *sIslamic) AyahList(ctx context.Context, in *model.AyahParamInput) (out []*model.AyahParamOutPut) {
	var ayahList []*entity.SuratAyat

	query := dao.SuratAyat.Ctx(ctx)
	if in.Id > 0 {
		query = query.Where(dao.SuratAyat.Columns().Id, in.Id)
	}
	if gconv.Int(in.SurahId) > 0 {
		query = query.Where(dao.SuratAyat.Columns().SurahId, in.SurahId)
	}
	if gconv.Int(in.JuzId) > 0 {
		query = query.Where(dao.SuratAyat.Columns().Juz, in.JuzId)
	}
	if gconv.Int(in.Page) > 0 {
		query = query.Where(dao.SuratAyat.Columns().Page, in.Page)
	}

	err := query.Scan(&ayahList)
	if err != nil || len(ayahList) == 0 {
		return out
	}
	for _, ayah := range ayahList {
		one := &model.AyahParamOutPut{
			Id:      uint(ayah.Id),
			SurahId: uint(ayah.SurahId),
			Tr:      ayah.Tr,
			Ar:      ayah.Ar,
			Idn:     ayah.Idn,
			Nomor:   uint(ayah.Nomor),
			Juz:     uint(ayah.Juz),
			Page:    uint(ayah.Page),
		}
		out = append(out, one)
	}
	return out
}

func (s *sIslamic) AyahReadRecord(ctx context.Context, in *model.AyahReadRecordInput) (out []*model.AyahReadRecordOutput) {

	//headerMap := grpcx.Ctx.IncomingMap(ctx)
	//g.Log().Line().Debug(ctx, in, headerMap.Map())

	uid, err := token.GetUserIdByToken(ctx)
	if err != nil {
		return out
	}
	SuratDaftar := s.QuerySurahByAyahId(int(in.AyahId))
	rcdData := &do.SurahReadRecord{
		UserId:    uid,
		AyahId:    in.AyahId,
		SurahName: SuratDaftar.NamaLatin,
		IsUserOp:  in.IsUserOp,
	}
	_, err = dao.SurahReadRecord.Ctx(ctx).Data(rcdData).Insert()
	if err != nil {
		g.Log().Error(ctx, "记录SurahReadRecord失败:", err)
		return
	}
	return out
}

func (s *sIslamic) AyahReadCollectList(ctx context.Context, in *model.AyahReadCollectListInput) (out []*model.AyahReadCollectListOutput) {
	var collectList []*entity.SurahReadCollect
	uid, err := token.GetUserIdByToken(ctx)
	if err != nil {
		return out
	}
	query := dao.SurahReadCollect.Ctx(ctx)
	query = query.Where(dao.SurahReadCollect.Columns().UserId, uid)
	err = query.Scan(&collectList)
	if err != nil || len(collectList) == 0 {
		return out
	}
	for _, ayah := range collectList {
		ayahInfo := s.QueryAyahByAyahId(int(ayah.Id))
		surahInfo := s.QuerySurahByAyahId(int(ayah.Id))
		one := &model.AyahReadCollectListOutput{
			SurahId:   uint(surahInfo.Id),
			SurahName: ayah.SurahName,
			AyahId:    ayah.Id,
			JuzId:     uint(ayahInfo.Juz),
		}
		out = append(out, one)
	}
	return out
}

func (s *sIslamic) AyahReadCollect(ctx context.Context, in *model.AyahReadCollectInput) (out *model.AyahReadCollectOutput) {
	uid, err := token.GetUserIdByToken(ctx)
	if err != nil {
		return out
	}
	// 检查用户是否已收藏该经文
	collectCount, err := dao.SurahReadCollect.Ctx(ctx).Where(dao.SurahReadCollect.Columns().UserId, uid).
		Where(dao.SurahReadCollect.Columns().AyahId, in.AyahId).
		Count()
	if collectCount == 0 {
		SuratDaftar := s.QuerySurahByAyahId(int(in.AyahId))
		rcdData := &do.SurahReadCollect{
			UserId:    uid,
			AyahId:    in.AyahId,
			SurahName: SuratDaftar.NamaLatin,
		}
		_, err = dao.SurahReadCollect.Ctx(ctx).Data(rcdData).Insert()
		if err != nil {
			g.Log().Error(ctx, "记录AyahReadCollectStatus失败:", err)
			return
		}
	} else {
		// 删除收藏记录
		_, err = dao.SurahReadCollect.Ctx(ctx).Where(dao.SurahReadCollect.Columns().UserId, uid).Where(dao.SurahReadCollect.Columns().AyahId, in.AyahId).
			Delete()
	}

	return out
}

func (s *sIslamic) CheckAyahReadCollectStatus(ctx context.Context, in *model.CheckAyahReadCollectStatusInput) (out *model.CheckAyahReadCollectStatusOutput) {
	uid, err := token.GetUserIdByToken(ctx)
	if err != nil {
		return out
	}
	// 检查用户是否已收藏该经文
	collectCount, err := dao.SurahReadCollect.Ctx(ctx).Where(dao.SurahReadCollect.Columns().UserId, uid).
		Where(dao.SurahReadCollect.Columns().AyahId, in.AyahId).
		Count()
	if err != nil {
		return out
	}
	if collectCount == 0 {
		out = &model.CheckAyahReadCollectStatusOutput{
			IsCollect: 0,
		}
	} else {
		out = &model.CheckAyahReadCollectStatusOutput{
			IsCollect: 1,
		}
	}

	return out
}
func (s *sIslamic) AyahReadRecordList(ctx context.Context, in *model.AyahReadRecordListInput) (out []*model.AyahReadRecordListOutput) {
	var recordList []*entity.SurahReadRecord
	uid, err := token.GetUserIdByToken(ctx)
	if err != nil {
		return out
	}
	query := dao.SurahReadRecord.Ctx(ctx)
	query = query.Where(dao.SurahReadRecord.Columns().UserId, uid)
	err = query.Scan(&recordList)
	if err != nil || len(recordList) == 0 {
		return out
	}
	for _, ayah := range recordList {
		ayahInfo := s.QueryAyahByAyahId(int(ayah.Id))
		surahInfo := s.QuerySurahByAyahId(int(ayah.Id))
		one := &model.AyahReadRecordListOutput{
			SurahId:   uint(surahInfo.Id),
			SurahName: ayah.SurahName,
			AyahId:    ayah.Id,
			JuzId:     uint(ayahInfo.Juz),
		}
		out = append(out, one)
	}
	return out
}

// ---------news分类相关逻辑---------------------------------------

func (s *sIslamic) QueryCateLanguageByIdAndLanId(CategoryId uint, LanguageId uint) (out *entity.NewsCategoryLanguage) {
	// 查询SurahId
	var newsCategoryLanguage *entity.NewsCategoryLanguage
	// 查询Surah信息
	err := dao.NewsCategoryLanguage.Ctx(context.Background()).Where(dao.NewsCategoryLanguage.Columns().CategoryId, CategoryId).Where(dao.NewsCategoryLanguage.Columns().LanguageId, LanguageId).Scan(&newsCategoryLanguage)
	if err != nil || newsCategoryLanguage == nil {
		return nil
	}
	return newsCategoryLanguage
}

func (s *sIslamic) NewsCategoryList(ctx context.Context, in *model.NewsCategoryListInput) (out []*model.NewsCategoryListOutput) {
	var cateList []*entity.NewsCategory
	query := dao.NewsCategory.Ctx(ctx).Where(dao.NewsCategory.Columns().Status, 1)
	if gconv.Int(in.Pid) > 0 {
		query = query.Where(dao.NewsCategory.Columns().ParentId, in.Pid)
	}
	switch in.LanguageId {
	case 0: //
		query = query.Where(dao.NewsCategory.Columns().IsZh, 1)
	case 1: //
		query = query.Where(dao.NewsCategory.Columns().IsEn, 1)
	case 2: //
		query = query.Where(dao.NewsCategory.Columns().IsId, 1)
	default: //
		query = query.Where(dao.NewsCategory.Columns().IsId, 1) // 默认阿拉伯语
	}
	err := query.Scan(&cateList)
	if err != nil || len(cateList) == 0 {
		return out
	}
	for _, cate := range cateList {
		CateLanguage := s.QueryCateLanguageByIdAndLanId(cate.Id, uint(in.LanguageId))
		one := &model.NewsCategoryListOutput{
			Id:         uint32(cate.Id),
			ParentId:   uint32(cate.ParentId),
			LanguageId: in.LanguageId,
			Name:       CateLanguage.Name,
			CoverImgs:  cate.CoverImgs,
		}
		out = append(out, one)
	}

	return out
}

func (s *sIslamic) QueryTopicLanguageByIdAndLanId(CategoryId uint, LanguageId uint) (out *entity.NewsTopicLanguage) {
	// 查询SurahId
	var newsTopicLanguage *entity.NewsTopicLanguage
	// 查询Surah信息
	err := dao.NewsTopicLanguage.Ctx(context.Background()).Where(dao.NewsTopicLanguage.Columns().TopicId, CategoryId).Where(dao.NewsTopicLanguage.Columns().LanguageId, LanguageId).Scan(&newsTopicLanguage)
	if err != nil || newsTopicLanguage == nil {
		return nil
	}
	return newsTopicLanguage
}
func (s *sIslamic) NewsTopicList(ctx context.Context, in *model.NewsTopicListInput) (out []*model.NewsTopicListOutput) {
	var cateList []*entity.NewsTopic
	query := dao.NewsTopic.Ctx(ctx).Where(dao.NewsTopic.Columns().Status, 1)
	//if gconv.Int(in.Pid) > 0 {
	//	query = query.Where(dao.NewsTopic.Columns().ParentId, in.Pid)
	//}
	switch in.LanguageId {
	case 0: //
		query = query.Where(dao.NewsTopic.Columns().IsZh, 1)
	case 1: //
		query = query.Where(dao.NewsTopic.Columns().IsEn, 1)
	case 2: //
		query = query.Where(dao.NewsTopic.Columns().IsId, 1)
	default: //
		query = query.Where(dao.NewsTopic.Columns().IsId, 1) // 默认阿拉伯语
	}
	err := query.Scan(&cateList)
	if err != nil || len(cateList) == 0 {
		return out
	}
	for _, cate := range cateList {
		CateLanguage := s.QueryTopicLanguageByIdAndLanId(cate.Id, uint(in.LanguageId))
		one := &model.NewsTopicListOutput{
			Id:         uint32(cate.Id),
			LanguageId: in.LanguageId,
			Name:       CateLanguage.Name,
			ShortName:  CateLanguage.ShortName,
			TopicImgs:  cate.TopicImgs,
		}
		out = append(out, one)
	}

	return out
}
