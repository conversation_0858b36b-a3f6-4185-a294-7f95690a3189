package banner

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"

	"halalplus/app/islamic-content-svc/internal/dao"
	"halalplus/app/islamic-content-svc/internal/model"
	"halalplus/app/islamic-content-svc/internal/model/do"
	"halalplus/app/islamic-content-svc/internal/model/entity"
	"halalplus/app/islamic-content-svc/internal/service"
)

type sBanner struct{}

func init() {
	service.RegisterBanner(New())
}

func New() service.IBanner {
	return &sBanner{}
}

var cl = dao.Banner.Columns()

// BannerList 获取Banner列表
func (s *sBanner) BannerList(ctx context.Context, languageId uint32) ([]*model.BannerInfo, error) {
	query := dao.Banner.Ctx(ctx).Where(cl.Status, 1) // 只查询启用状态的Banner

	// 按语言筛选
	query = query.Where(cl.LanguageId, languageId)

	// 排序 asc
	query = query.Order(cl.SortOrder)

	var banners []*entity.Banner
	err := query.Scan(&banners)
	if err != nil {
		g.Log().Error(ctx, "查询Banner列表失败:", err)
		return nil, err
	}

	// 转换为响应格式
	var bannerInfos []*model.BannerInfo
	for _, banner := range banners {
		bannerInfo := &model.BannerInfo{
			Id:          uint32(banner.Id),
			LanguageId:  uint32(banner.LanguageId),
			Title:       banner.Title,
			Description: banner.Description,
			ImageUrl:    banner.ImageUrl,
			LinkUrl:     banner.LinkUrl,
			SortOrder:   uint32(banner.SortOrder),
		}
		bannerInfos = append(bannerInfos, bannerInfo)
	}

	return bannerInfos, nil
}

// BannerClick 记录Banner点击统计
// 这个是记录点击统计的，不返回任何结果
func (s *sBanner) BannerClick(ctx context.Context, in *model.BannerClickInput) {
	// 验证Banner是否存在
	bannerExists, err := dao.Banner.Ctx(ctx).Where(cl.Id, in.BannerId).Count()
	if err != nil || bannerExists == 0 {
		g.Log().Error(ctx, "查询Banner失败:", err)
		return
	}

	// 获取客户端IP和User-Agent
	clientIP := g.RequestFromCtx(ctx).GetClientIp()
	userAgent := g.RequestFromCtx(ctx).Header.Get("User-Agent")

	// 如果从请求中获取不到，使用传入的device_id作为标识
	if clientIP == "" {
		clientIP = "unknown"
	}
	if userAgent == "" {
		userAgent = "unknown"
	}

	// 记录点击统计
	statsData := &do.BannerStats{
		BannerId:  in.BannerId,
		UserId:    0,
		DeviceId:  in.DeviceId,
		IpAddress: clientIP,
		UserAgent: userAgent,
		CreatedAt: gtime.Now(),
	}

	_, err = dao.BannerStats.Ctx(ctx).Data(statsData).Insert()
	if err != nil {
		g.Log().Error(ctx, "记录Banner点击统计失败:", err)
		return
	}

	// log做统计用？
	g.Log().Info(ctx, "Banner点击统计记录成功", g.Map{
		"banner_id": in.BannerId,
		"user_id":   0,
		"device_id": in.DeviceId,
		"ip":        clientIP,
	})
}
