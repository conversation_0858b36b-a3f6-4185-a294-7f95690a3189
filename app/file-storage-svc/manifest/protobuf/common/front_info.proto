syntax = "proto3";

package common;

option go_package = "halalplus/api/common;common";

// 前端信息采集
message FrontInfo {
  string device_id = 1;          // 设备号（设备指纹）
  string device_os = 2;          // 设备操作系统（android, ios, windows, mac, ...）
  string device_os_version = 3;  // 设备操作系统版本号
  int32 device_type = 4;         // 设备类型（1:mobile手机, 2:desktop台式, 3:pad平板, 4:其他）
  int32 app_type = 5;            // 应用类型（1:android, 2:ios, 3:h5, 4:web, 5:其他）
  string app_version = 6;        // 应用版本号
}
