// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	"halalplus/app/file-storage-svc/internal/model"
)

type (
	IFile interface {
		// SingleUpload 上传单文件
		SingleUpload(ctx context.Context, in model.SingleUploadInput) (*model.SingleUploadOutput, error)
		// GetObjectName 获取对象名称
		// 格式: pub/ad/3hn1300cyj7r4t47ugp100t6jmkszl1y.jpg 临时目录: tmp/pub/ad/3hn1300cyj7r4t47ugp100t6jmkszl1y.jpg
		GetObjectName(ctx context.Context, in model.GetObjectNameInput) string
		// GetModuleNamePrefix 获取模块应该有的前缀
		// 格式: pub/ad/  in/ad/  tmp/pub/ad/ tmp/in/ad/   ad为模块名
		GetModuleNamePrefix(ctx context.Context, moduleName string) (res []string)
		// HandleUpload 处理单个已上传的图片
		HandleUpload(ctx context.Context, moduleName string, srcObjectName string) string
		// HandleUploads 批量处理上传的图片, 2.系统模块要复制到对应的模块  3.
		HandleUploads(ctx context.Context, moduleName string, srcObjectNames ...string) (map[string]string, error)
		// BatchCopy 批量复制文件
		BatchCopy(ctx context.Context, objectNames map[string]string) error
		// Copy 复制文件
		Copy(ctx context.Context, destObjectName string, srcObjectName string) error
		// Remove 删除文件
		Remove(ctx context.Context, objectNames ...string) error
		// RemoveObject 删除单个文件
		RemoveObject(ctx context.Context, objectName string) error
		// RemoveObjects 删除多个文件
		RemoveObjects(ctx context.Context, objectNames []string) error
		// GetBackUpUrl 获取管理后理的文件链接
		GetBackUpUrl(ctx context.Context, objectName string) string
		// GetFrontendUrl 获取前台的文件链接
		GetFrontendUrl(ctx context.Context, objectName string) string
		// GetUrl 根据前缀决定是否返回参数
		GetUrl(ctx context.Context, in model.GetUrlInput) (*model.GetUrlOutput, error)
		// GetCanDeleteFile 返回可删除文件
		GetCanDeleteFile(ctx context.Context, data map[string]interface{}, moduleName ...string) []string
	}
)

var (
	localFile IFile
)

func File() IFile {
	if localFile == nil {
		panic("implement not found for interface IFile, forgot register?")
	}
	return localFile
}

func RegisterFile(i IFile) {
	localFile = i
}
