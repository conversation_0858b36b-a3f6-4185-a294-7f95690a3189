package file

import (
	"context"
	_ "github.com/gogf/gf/contrib/drivers/mysql/v2"
	_ "github.com/gogf/gf/contrib/nosql/redis/v2"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcache"
	"github.com/gogf/gf/v2/os/gcfg"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/gfile"
	"idnuapi/internal/consts"
	_ "idnuapi/internal/logic/dbCache"
	_ "idnuapi/internal/logic/sys"
	"idnuapi/internal/model"
	"idnuapi/internal/service"
	"os"
	"path"
	"testing"
)

var (
	ctx context.Context
	s   service.IFile
)

func TestMain(m *testing.M) {
	p, err := os.Getwd()
	if err != nil {
		panic(err)
	}
	cfgPath := path.Join(p, "../../../manifest/config")
	err = g.Cfg().GetAdapter().(*gcfg.AdapterFile).AddPath(cfgPath)
	if err != nil {
		panic(err)
	}
	// 为数据库设置redis缓存
	redisCache := gcache.NewAdapterRedis(g.Redis())
	g.DB().GetCache().SetAdapter(redisCache)
	ctx = gctx.GetInitCtx()
	s = New()

	m.Run()
}

func Test_sFile_GetUrl(t *testing.T) {

	in := model.GetUrlInput{
		ObjectName: "test/99.svg",
	}
	out, err := s.GetUrl(ctx, in)
	if err != nil {
		t.Error(ctx, err)
	}
	t.Log(out)

	pubIn := model.GetUrlInput{
		ObjectName: "pub/bank/1.svg",
	}
	out, err = s.GetUrl(ctx, pubIn)
	if err != nil {
		t.Error(ctx, err)
	}
	t.Log(out)

}

func Test_sFile_GetFrontendUrl(t *testing.T) {
	inObjectName := "test/99.svg"
	out := s.GetFrontendUrl(ctx, inObjectName)
	t.Log(out)
	pubOjbect := "pub/bank/1.svg"
	out = s.GetFrontendUrl(ctx, pubOjbect)
	t.Log(out)
}

// Test_sFile_BankUpload 将本地的bank的图标上传到minio
func Test_sFile_BankUpload(t *testing.T) {
	p, err := os.Getwd()
	if err != nil {
		t.Fatal(err)
	}

	files, err := gfile.ScanDir(path.Join(p, "bank"), "*", false)
	if err != nil {
		t.Fatal(err)
	}
	for _, file := range files {
		f, ferr := os.Open(file)
		if ferr != nil {
			t.Fatal(ferr)
		}
		info, err := s.SingleUpload(ctx, model.SingleUploadInput{
			File:       f,
			Prefix:     consts.FilePrefixPublicDir,
			ModuleName: consts.FileModuleDefault,
			Name:       gfile.Basename(file),
			// NameExt:    "png",
		})
		if err != nil {
			t.Fatal(err)
		}
		t.Log(info)
	}

	/*
		t.Log(out)*/
}

func Test_sFile_Copy(t *testing.T) {
	err := s.Copy(ctx, "pub/default/658.png", "pub/default/657.png")
	if err != nil {
		t.Error(ctx, err)
	}
}

func Test_sFile_BatchCopy(t *testing.T) {
	objectNames := make(map[string]string)
	objectNames["test/tt.svg"] = "test/tt2.svg"
	objectNames["test/tt5.svg"] = "test/tt6.svg"
	err := s.BatchCopy(ctx, objectNames)
	if err != nil {
		t.Error(ctx, err)
	}
}

func Test_sFile_RemoveObject(t *testing.T) {
	objectName := "pub/default/658.png"
	err := s.RemoveObject(ctx, objectName)
	if err != nil {
		t.Error(err)
	}
}

func Test_sFile_RemoveObjects(t *testing.T) {
	objectName := []string{"test/tt2.svg", "test/tt6.svg"}
	err := s.RemoveObjects(ctx, objectName)
	if err != nil {
		t.Error(err)
	}
}
