package libfile

import (
	"context"
	"fmt"
	"github.com/aws/aws-sdk-go-v2/aws"
	awsConfig "github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/aws/smithy-go/ptr"
	"github.com/gogf/gf/v2/frame/g"
	"halalplus/utility/mimedb"
	"io"
	"net/url"
	"path/filepath"
	"time"
)

type AwsS3Client struct {
	client        *s3.Client
	PresignClient *s3.PresignClient
	config        AwsS3Config
}

var _ S3Client = &AwsS3Client{}

const S3Aws = "aws"

// AwsS3Config aws s3的配置
type AwsS3Config struct {
	Region          string `json:"region"`
	AccessKeyID     string `json:"accessKeyID"`
	SecretAccessKey string `json:"secretAccessKey"`
	SessionToken    string `json:"sessionToken"`
}

func NewAwsS3Client(ctx context.Context, config AwsS3Config) (*AwsS3Client, error) {
	optFns := make([]func(*awsConfig.LoadOptions) error, 0)
	if len(config.Region) > 0 {
		optFns = append(optFns, awsConfig.WithRegion(config.Region))
	}
	if len(config.AccessKeyID) > 0 && len(config.SecretAccessKey) > 0 {
		optFns = append(optFns, awsConfig.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(config.AccessKeyID, config.SecretAccessKey, config.SessionToken)))
	}
	sdkConfig, err := awsConfig.LoadDefaultConfig(ctx, optFns...)
	if err != nil {
		return nil, err
	}
	s3Client := s3.NewFromConfig(sdkConfig)
	client := &AwsS3Client{
		client:        s3Client,
		config:        config,
		PresignClient: s3.NewPresignClient(s3Client),
	}

	return client, nil
}

// GetObjectURL  获取对象的访问网址(只支持公开访问的)
func (c *AwsS3Client) GetObjectURL(ctx context.Context, bucketName, objectName string) *url.URL {
	var params = s3.EndpointParameters{
		Region:              ptr.String(c.config.Region),
		UseFIPS:             aws.Bool(false),
		UseDualStack:        aws.Bool(false),
		Accelerate:          aws.Bool(false),
		DisableAccessPoints: aws.Bool(false),
		Bucket:              &bucketName,
	}
	resolver := s3.NewDefaultEndpointResolverV2()
	res, err := resolver.ResolveEndpoint(context.Background(), params)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil
	}
	res.URI.Path = objectName
	return &res.URI
}

// PutObject 上传对象(小文件)
func (c *AwsS3Client) PutObject(ctx context.Context, bucketName, objectName string, reader io.Reader) error {
	_, err := c.client.PutObject(ctx, &s3.PutObjectInput{
		Bucket:      &bucketName,
		Key:         &objectName,
		Body:        reader,
		ContentType: aws.String(mimedb.TypeByExtension(filepath.Ext(objectName))),
	})
	if err != nil {
		return err
	}
	return nil
}

// CopyObject 复制对象
func (c *AwsS3Client) CopyObject(ctx context.Context, bucketName string, destObjectName string, srcObjectName string) error {
	_, err := c.client.CopyObject(ctx, &s3.CopyObjectInput{
		Bucket:     &bucketName,
		CopySource: aws.String(fmt.Sprintf("%v/%v", bucketName, srcObjectName)),
		Key:        &destObjectName,
	})
	if err != nil {
		return err
	}
	return nil
}

// RemoveObject 删除单个对象
func (c *AwsS3Client) RemoveObject(ctx context.Context, bucketName string, objectName string) error {
	_, err := c.client.DeleteObject(ctx, &s3.DeleteObjectInput{
		Bucket: &bucketName,
		Key:    &objectName,
		// BypassGovernanceRetention: aws.Bool(true),
	})
	if err != nil {
		return err
	}
	return nil
}

// RemoveObjects 批量删除对象
func (c *AwsS3Client) RemoveObjects(ctx context.Context, bucketName string, objectNames []string) error {
	deletes := types.Delete{
		Objects: make([]types.ObjectIdentifier, 0, len(objectNames)),
	}
	for _, objectName := range objectNames {
		newObjectName := objectName
		deletes.Objects = append(deletes.Objects, types.ObjectIdentifier{Key: &newObjectName})
	}
	_, err := c.client.DeleteObjects(ctx, &s3.DeleteObjectsInput{
		Bucket: &bucketName,
		Delete: &deletes,
		// BypassGovernanceRetention: aws.Bool(true),
	})
	if err != nil {
		return err
	}
	return nil
}

// PresignedGetObject 获取对象的访问授权链接
func (c *AwsS3Client) PresignedGetObject(ctx context.Context, bucketName string, objectName string, expires time.Duration) (u *url.URL, err error) {
	// responseContentDisposition := fmt.Sprintf("attachment; filename=\"%s\"", objectName)
	out, err := c.PresignClient.PresignGetObject(ctx, &s3.GetObjectInput{
		Bucket: &bucketName,
		Key:    &objectName,
		// ResponseContentDisposition: &responseContentDisposition,
	}, func(opts *s3.PresignOptions) {
		opts.Expires = expires
		/*opts.ClientOptions = append(opts.ClientOptions, func(op *s3.Options) {
			op.BaseEndpoint = aws.String("https://tt.com/")
		})*/
	})
	if err != nil {
		return
	}
	u, err = url.Parse(out.URL)
	if err != nil {
		return
	}
	return
}
