package libfile

import (
	"context"
	"errors"
	"fmt"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/util/gutil"
	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
	"halalplus/utility/mimedb"
	"io"
	"net/url"
	"path"
	"path/filepath"
	"time"
)

type MinioClient struct {
	client *minio.Client
	config MinioConfig
}

var _ S3Client = &MinioClient{}

const S3Minio = "minio"

// MinioConfig minio的配置
type MinioConfig struct {
	Endpoint        string `json:"endpoint"`
	AccessKeyID     string `json:"accessKeyID"`
	SecretAccessKey string `json:"secretAccessKey"`
	SessionToken    string `json:"sessionToken"`
	UseSSL          bool   `json:"useSSL"`
}

func NewMinioClient(ctx context.Context, config MinioConfig) (*MinioClient, error) {
	c, err := minio.New(config.Endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(config.AccessKeyID, config.SecretAccessKey, config.SessionToken),
		Secure: config.UseSSL,
	})
	if err != nil {
		return nil, err
	}
	ins := &MinioClient{
		client: c,
		config: config,
	}
	return ins, nil
}

// GetObjectURL  获取对象的访问网址(只支持公开访问的)
func (c *MinioClient) GetObjectURL(ctx context.Context, bucketName, objectName string) *url.URL {
	u := c.client.EndpointURL()
	u.Path = path.Join(bucketName, objectName)
	return u
}

// PutObject 上传对象(小文件)
// https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types
// https://www.iana.org/assignments/media-types/media-types.xhtml
func (c *MinioClient) PutObject(ctx context.Context, bucketName, objectName string, reader io.Reader) error {
	_, err := c.client.PutObject(ctx, bucketName, objectName, reader, -1, minio.PutObjectOptions{
		ContentType: mimedb.TypeByExtension(filepath.Ext(objectName)),
	})
	if err != nil {
		return err
	}
	return nil
}

// CopyObject 复制对象
func (c *MinioClient) CopyObject(ctx context.Context, bucketName string, destObjectName string, srcObjectName string) error {
	_, err := c.client.CopyObject(ctx, minio.CopyDestOptions{
		Bucket: bucketName,
		Object: destObjectName,
	}, minio.CopySrcOptions{
		Bucket: bucketName,
		Object: srcObjectName,
	})
	if err != nil {
		return err
	}
	return nil
}

// RemoveObject 删除单个对象
func (c *MinioClient) RemoveObject(ctx context.Context, bucketName string, objectName string) error {
	err := c.client.RemoveObject(ctx, bucketName, objectName, minio.RemoveObjectOptions{
		GovernanceBypass: true,
	})
	if err != nil {
		return err
	}
	return nil
}

// RemoveObjects 批量删除对象
func (c *MinioClient) RemoveObjects(ctx context.Context, bucketName string, objectNames []string) error {
	opts := minio.RemoveObjectsOptions{
		GovernanceBypass: true,
	}
	objectsCh := make(chan minio.ObjectInfo)
	gutil.Go(ctx, func(ctx context.Context) {
		defer func() {
			close(objectsCh)
		}()
		for _, objectName := range objectNames {
			objectsCh <- minio.ObjectInfo{
				Key: objectName,
			}
		}
	}, nil)
	var removeErr error
	for removeRes := range c.client.RemoveObjects(ctx, bucketName, objectsCh, opts) {
		if removeRes.Err != nil {
			removeErr = errors.Join(removeErr, removeRes.Err, gerror.Newf("MinioClient.RemoveObjects, objectName:%s", removeRes.ObjectName))
		}
	}
	if removeErr != nil {
		return removeErr
	}
	return nil
}

// PresignedGetObject 获取对象的访问授权链接
func (c *MinioClient) PresignedGetObject(ctx context.Context, bucketName string, objectName string, expires time.Duration) (u *url.URL, err error) {
	reqParams := make(url.Values)
	reqParams.Set("response-content-disposition", fmt.Sprintf("attachment; filename=\"%s\"", objectName))
	u, err = c.client.PresignedGetObject(ctx, bucketName, objectName, expires, reqParams)
	if err != nil {
		return
	}
	return
}
