package libfile

import (
	"context"
	"io"
	"net/url"
	"time"
)

type S3Client interface {
	GetObjectURL(ctx context.Context, bucketName, objectName string) *url.URL
	PutObject(ctx context.Context, bucketName, objectName string, reader io.Reader) error
	CopyObject(ctx context.Context, bucketName string, destObjectName string, srcObjectName string) error
	RemoveObject(ctx context.Context, bucketName string, objectName string) error
	RemoveObjects(ctx context.Context, bucketName string, objectNames []string) error
	PresignedGetObject(ctx context.Context, bucketName string, objectName string, expires time.Duration) (u *url.URL, err error)
}
