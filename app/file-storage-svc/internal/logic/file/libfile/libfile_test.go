package libfile

import (
	"context"
	"fmt"
	"os"
	"path"
	"testing"
	"time"
)

var awsS3Client *AwsS3Client
var minioClient *MinioClient

const bucketName = "catalogs"

var c S3Client

func newAwsS3ClientTest() {
	config := AwsS3Config{
		Region:          "eu-west-3",
		AccessKeyID:     "",
		SecretAccessKey: "",
		SessionToken:    "",
	}
	var err error
	awsS3Client, err = NewAwsS3Client(context.Background(), config)
	if err != nil {
		fmt.Println(err)
		return
	}
}

func newMinioClientTest() {
	config := MinioConfig{
		Endpoint:        "s3.eu-west-3.amazonaws.com",
		AccessKeyID:     "",
		SecretAccessKey: "",
		SessionToken:    "",
		UseSSL:          false,
	}
	var err error
	minioClient, err = NewMinioClient(context.Background(), config)
	if err != nil {
		fmt.Println(err)
		return
	}
}

func TestMain(m *testing.M) {
	newAwsS3ClientTest()
	newMinioClientTest()
	c = awsS3Client // TODO 测试哪个客户端就切换哪个
	m.Run()
}

func TestGetObjectURL(t *testing.T) {
	uri := c.GetObjectURL(context.Background(), bucketName, "test/tt5.svg")
	if uri == nil {
		t.Error("uri is nil")
		return
	}
	t.Log(uri.String())
}

func TestPutObject(t *testing.T) {
	wd, err := os.Getwd()
	if err != nil {
		panic(err)
	}
	reader, err := os.Open(path.Join(wd, "test_99.svg"))
	if err != nil {
		t.Error(err)
		return
	}
	err = c.PutObject(context.Background(), bucketName, "test/tt5.svg", reader)
	if err != nil {
		t.Error(err)
		return
	}
}

func TestCopyObject(t *testing.T) {
	err := c.CopyObject(context.Background(), bucketName, "test/tt2.svg", "test/tt.svg")
	if err != nil {
		t.Error(err)
		return
	}
}

func TestRemoveObject(t *testing.T) {
	err := c.RemoveObject(context.Background(), bucketName, "test/tt2.svg")
	if err != nil {
		t.Error(err)
		return
	}
}

func TestRRemoveObjects(t *testing.T) {
	err := c.RemoveObjects(context.Background(), bucketName, []string{"test/tt4.svg", "test/tt5.svg"})
	if err != nil {
		t.Error(err)
		return
	}
}

func TestPresignedGetObject(t *testing.T) {
	u, err := c.PresignedGetObject(context.Background(), bucketName, "test/tt.svg", time.Second*time.Duration(86400))
	if err != nil {
		t.Error(err)
		return
	}
	t.Log(u)
}
