package cmd

import (
	"context"
	"github.com/gogf/gf/contrib/rpc/grpcx/v2"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
	"halalplus/utility"
	"halalplus/utility/gf-registry-consul"
	"halalplus/utility/unary"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcmd"
)

var (
	Main = gcmd.Command{
		Name:  "file-storage-svc",
		Usage: "file-storage-svc",
		Brief: "start file-storage-svc grpc server",
		Func: func(ctx context.Context, parser *gcmd.Parser) (err error) {
			registry, err := consul.New(consul.WithAddress("127.0.0.1:8500"))
			if err != nil {
				g.Log().Fatal(context.Background(), err)
			}

			grpcx.Resolver.Register(registry)
			c := grpcx.Server.NewConfig()
			c.Name = "file-storage-svc"
			if g.<PERSON>(c.Address) {
				c.Address = utility.GetLocalLANIP() + ":0"
			}
			c.Options = append(c.Options, []grpc.ServerOption{
				grpcx.Server.ChainUnary(
					unary.UnaryCommonError,
					grpcx.Server.UnaryValidate,
				)}...,
			)
			s := grpcx.Server.New(c)
			// 注册grpc的controller
			//xxx.Register(s)

			// NOTICE: 一行代码，注册反射服务
			reflection.Register(s.Server)

			s.Run()
			return nil
		},
	}
)
