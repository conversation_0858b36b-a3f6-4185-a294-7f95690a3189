package model

import (
	"io"
	"net/url"
)

type SingleUploadInput struct {
	// File       *ghttp.UploadFile // 上传文件对象
	File       io.Reader // 上传文件对象
	Prefix     string    // 自定义前缀[pub, in, tmp]
	ModuleName string    // 自定义模块名称,用于二级路径名称,达到图片分类的目的,请不要使用特殊字符,没有则默认为default
	Name       string    // 自定义文件名称(一般情况下不传)
	NameExt    string    // 文件名后缀(必传)
}

type FileUploadInput struct {
	File       io.Reader // 上传文件对象
	Prefix     string    // 自定义前缀[pub, in, tmp]
	ModuleName string    // 自定义模块名称,用于二级路径名称,达到图片分类的目的,请不要使用特殊字符,没有则默认为default
	Name       string    // 自定义文件名称, 请注意文件后缀也需要带上
}

type SingleUploadOutput struct {
	ObjectName string `json:"objectName"` // oss对象名称
}

type GetObjectNameInput struct {
	Prefix     string // 前缀
	ModuleName string // 模块名称
	Name       string // 文件名称(可选)
	NameExt    string // 文件后缀(上传的),当name有值时,nameExt不生效
	IsTmp      bool   // 是否临时
}

// GetUrlInput 获取文件链接
type GetUrlInput struct {
	ObjectName string // 对象名称
}

// GetUrlOutput 获取文件链接的返回结果
type GetUrlOutput struct {
	Url *url.URL
}
