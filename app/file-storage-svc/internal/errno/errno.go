package errno

import (
	"context"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/i18n/gi18n"
)

type Errno struct {
	gerror.Error
}

func T(ctx context.Context, code gcode.Code) error {
	return gerror.NewCode(code, gi18n.T(ctx, code.Message()))
}

func Tf(ctx context.Context, code gcode.Code, values ...interface{}) error {
	return gerror.NewCode(code, gi18n.Tf(ctx, code.Message(), values...))
}
