package errno

import (
	"github.com/gogf/gf/v2/i18n/gi18n"
	"github.com/gogf/gf/v2/os/gctx"
	. "github.com/smartystreets/goconvey/convey"
	"testing"
)

func TestErr(t *testing.T) {
	<PERSON><PERSON>("ERR", t, func() {
		<PERSON><PERSON>("zh-CN", func() {
			ctx := gi18n.WithLanguage(gctx.New(), "zh-CN")
			err := Err(ctx, "common.process")
			So(err.<PERSON>rror(), ShouldEqual, "进行中")
		})
		<PERSON><PERSON>("id", func() {
			ctx := gi18n.WithLanguage(gctx.New(), "id")
			err := Err(ctx, "common.process")
			So(err.<PERSON><PERSON><PERSON>(), ShouldE<PERSON>l, "Pengolahan")
		})
		<PERSON>vey("Multi", func() {
			ctx := gi18n.WithLanguage(gctx.New(), "zh-CN")
			err := Err(ctx, "game.id.notExist", 1)
			So(err.<PERSON><PERSON>r(), ShouldEqual, "游戏id 1 不存在")
		})

	})

}
