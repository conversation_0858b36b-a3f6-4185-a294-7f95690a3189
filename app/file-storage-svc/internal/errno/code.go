package errno

import (
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
)

var (
	CodeRecodeNotExist     = gcode.New(1300, "error.record.not.exist", nil)      // 记录找不到
	CodeRecodeIdNotExist   = gcode.New(1301, "error.record.id.not.exist", nil)   // 记录ID:%d找不到
	CodeRecodeDeleteFailed = gcode.New(1302, "error.record.delete.failed", nil)  // 删除记录失败
	CodeRecodeCountLimit   = gcode.New(1303, "account.count.exceeds.limit", nil) // 限制操作数
	CodeInfoExpired        = gcode.New(1304, "common.info.expired", nil)         // 信息过期,请重新刷新页面
)

var (
	ErrorRecodeNotExist = gerror.NewCode(CodeRecodeNotExist)
)
