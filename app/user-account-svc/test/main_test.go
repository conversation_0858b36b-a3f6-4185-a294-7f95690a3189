package test

import (
	"github.com/gogf/gf/contrib/rpc/grpcx/v2"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"google.golang.org/grpc"
	"testing"
)

var userAccountSvcConn *grpc.ClientConn

func TestMain(m *testing.M) {
	g.Log().Info(nil, "######TestMain")
	var err error
	ctx := gctx.GetInitCtx()
	userAccountSvcConn, err = grpcx.Client.NewGrpcClientConn("0.0.0.0:9200")
	if err != nil {
		g.Log().Error(ctx, err)
	}
	m.Run()
}
