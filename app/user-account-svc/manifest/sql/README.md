# golang-migrate工具管理SQL迁移脚本

* https://github.com/golang-migrate/migrate

# 创建新的迁移脚本
migrate create -ext sql -dir manifest/sql/mysql -seq create_users_table

# 执行升级迁移
migrate -path manifest/sql/mysql -database 'mysql://admin:Ru5.KcTC%25GT%2AxD~%5D%2Br%2BbGi6bjF@tcp(localhost:3406)/user_account_svc?multiStatements=true' up

# 回滚最近一次迁移
migrate -path manifest/sql/mysql -database 'mysql://admin:Ru5.KcTC%25GT%2AxD~%5D%2Br%2BbGi6bjF@tcp(localhost:3406)/user_account_svc?multiStatements=true' down 1

# 回滚到特定版本
migrate -path manifest/sql/mysql -database 'mysql://admin:Ru5.KcTC%25GT%2AxD~%5D%2Br%2BbGi6bjF@tcp(localhost:3406)/user_account_svc?multiStatements=true' goto **************

# 强制设置版本（在迁移出错后使用）
migrate -path manifest/sql/mysql -database 'mysql://admin:Ru5.KcTC%25GT%2AxD~%5D%2Br%2BbGi6bjF@tcp(localhost:3406)/user_account_svc?multiStatements=true' force **************

# 查看当前版本
migrate -path manifest/sql/mysql -database "mysql://admin:Ru5.KcTC%GT*xD~]+r+bGi6bjF*&@tcp(localhost:3306)/hl_user_account?multiStatements=true" version
