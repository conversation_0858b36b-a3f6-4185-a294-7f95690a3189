// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/user-account-svc/api/pbentity";



message User {
    uint32 Id                    = 1;  //
    uint32 AgentId               = 2;  // 上级（代理）的id
    string AgentCode             = 3;  // 注册时填写的代理码（选填）
    string Account               = 4;  // 账号
    string Password              = 5;  // 密码
    string PayPassword           = 6;  // 支付密码
    int64  PasswordModifyTime    = 7;  // 登录密码支付密码最近修改时间
    string AreaCode              = 8;  // 手机国际区号，如：86
    string PhoneNum              = 9;  // 手机号
    int64  BindPhoneTime         = 10; // 手机号绑定时间
    string Email                 = 11; // 邮箱地址
    int64  BindEmailTime         = 12; // 邮箱绑定时间
    int64  BindRealNameTime      = 13; // 真实姓名绑定时间
    int32  VipLevel              = 14; // vip等级
    uint32 LevelId               = 15; // 会员层级id
    int32  IsBanned              = 16; // 账号封号状态： 1 正常 2 封号
    int32  IsProhibit            = 17; // 提取状态：1 正常 2 禁提
    int32  IsOnline              = 18; // 是否在线：1是 2 否
    int64  OnlineDuration        = 19; // 在线时长（单位：秒）
    int32  SigninCount           = 20; // 登录次数
    int64  LastSigninTime        = 21; // 最后一次登录时间
    string LastSigninIp          = 22; // 最后登录ip
    string LastSigninDeviceId    = 23; // 最后登录设备号
    int32  LastSigninAppType     = 24; // 最近登录应用类型（1:android 2: ios，3:h5，4:web，5:其他）
    string LastSigninAppVersion  = 25; // 最近登录应用类型版本号
    string SignupIp              = 26; // 注册ip
    string SignupIpRegion        = 27; // 注册IP地理区域
    string SignupDeviceId        = 28; // 注册设备号（设备指纹）
    string SignupDeviceOs        = 29; // 注册设备系统（android,ios,windows,mac,...）
    string SignupDeviceOsVersion = 30; // 注册设备系统版本号
    int32  SignupDeviceType      = 31; // 注册设备类型（1:mobile手机，2:desktop台式，3:pad平板，。。。其他）
    int32  SignupAppType         = 32; // 应用类型（1:android 2: ios，3:h5，4:web，5:其他）
    string SignupAppVersion      = 33; // 注册应用类型版本号
    string SignupHost            = 34; // 注册域名(接口域名)
    string SignupDomain          = 35; // 注册域名(页面原始域名)
    string SignupDomain2         = 36; // 注册域名(页面域名)
    int32  LastSigninLogId       = 37; // 最后一次登录日志id（如果有分表的话，要考虑时间）
    string DeviceTokenIos        = 38; // IOS推送token
    string DeviceTokenAndroid    = 39; // android推送token(FCM)
    string SecurityPassword      = 40; // 安全密码，修改个人绑定信息时要验证
    int32  Version               = 41; // 该记录的版本号
    int32  IsTest                = 42; // 测试账号： 1 是 ，其他值：否
    int64  LimitStartTime        = 43; // 限制登录开始时间
    int64  LimitEndTime          = 44; // 限制登录结束时间
    int64  CreateTime            = 45; // 创建时间（注册时间）
    int64  UpdateTime            = 46; // 更新时间，0代表创建后未被修改过（哪些字段的更新会触发这个？）
    string CreateAccount         = 47; // 创建者账号
    int32  CreateType            = 48; // 创建者来源(注册来源)（1会员注册user，2代理添加agent，3管理员添加admin）
    string UpdateAccount         = 49; // 更新者账号
    int32  UpdateType            = 50; // 更新者来源
    string InviteCode            = 51; // 邀请码
    string TransferCode          = 52; // 转线码
    int64  NoobTaskFinishTime    = 53; // 新手任务完成时间
    int32  DataType              = 54; // 数据类型:1正式数据;2测试数据
    string PixelId               = 55; // 像素id
    int32  Source                = 56; // 注册来源( 1直客，2代理，3邀请，4后台）
}