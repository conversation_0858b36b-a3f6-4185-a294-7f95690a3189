syntax = "proto3";

package data.v1;

option go_package = "halalplus/app/user-account-svc/api/data/v1;datav1";

import "common/base.proto"; // 假设 common.Error 定义在这

// === 数据结构 ===
// 用户的json文档，每个文档限制最大256kb
message UserKV {
  string key_path = 1; // 以 dot 分隔的键路径，例如 "settings.theme.color"
  string value_data = 2; // 对应键的 JSON 数据值，支持任意结构
}

// 获取用户数据
// 用户键路径，使用 dot 分隔的 JSON 路径结构。
//
// 规则说明：
// - `key_path` 的第一级表示一个独立的 JSON 文档名（如 "settings"），限制最大256kb
// - 后续部分表示文档内的字段路径（如 "settings.theme.color" 表示 settings 文档中的 $.theme.color）
//
// 示例：
// - key_path = "settings"              → 返回完整 settings JSON 文档
// - key_path = "settings.theme"        → 返回 settings 文档中 $.theme 的值
// - key_path = "settings.theme.color"  → 返回 settings 文档中 $.theme.color 的值
//
// 更新请求遵循相同规则。
message GetDataReq {
  string key_path = 1; // 以 dot 分隔的键路径，例如 "settings.theme.color"
}

message GetDataRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  UserKV kv = 4;
}

// 更新数据
// 用户键路径，使用 dot 分隔的 JSON 路径结构。
//
// 规则说明：
// - `key_path` 的第一级表示一个独立的 JSON 文档名（如 "settings"），限制最大256kb
// - 后续部分表示文档内的字段路径（如 "settings.theme.color" 表示 settings 文档中的 $.theme.color）
//
// 示例：
// - key_path = "settings"              → 覆盖 JSON 文档
// - key_path = "settings.theme"        → 更新 $.theme 的值
// - key_path = "settings.theme.color"  → 更新 $.theme.color 的值
//
// 清空文档
// - key_path = "settings"
// - value_data = "{}"
//
message UpdateDataReq {
  string key_path = 1; // 以 dot 分隔的键路径，例如 "settings.theme.color"
  string value_data = 2; // 对应键的 JSON 数据值，数组的数据以'['开头，json object数据以'{'开头，布尔值是true false, null表示空值
}

message UpdateDataRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
}

// === 用户数据服务接口 ===
service DataService {
  // 获取用户的私有数据，文档不存在时返回空
  // Get /api/user-account/data/v1/dataService/Get
  rpc Get(GetDataReq) returns (GetDataRes);

  // 修改用户的私有数据
  // POST /api/user-account/data/v1/dataService/Update
  rpc Update(UpdateDataReq) returns (UpdateDataRes);
}
