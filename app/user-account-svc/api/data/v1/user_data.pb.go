// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: data/v1/user_data.proto

package datav1

import (
	common "halalplus/api/common"
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// === 数据结构 ===
// 用户的json文档，每个文档限制最大256kb
type UserKV struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	KeyPath   string `protobuf:"bytes,1,opt,name=key_path,json=keyPath,proto3" json:"key_path,omitempty" dc:"以 dot 分隔的键路径，例如 'profile.theme.color'"` // 以 dot 分隔的键路径，例如 "profile.theme.color"
	ValueData string `protobuf:"bytes,2,opt,name=value_data,json=valueData,proto3" json:"value_data,omitempty" dc:"对应键的 JSON 数据值，支持任意结构"`            // 对应键的 JSON 数据值，支持任意结构
}

func (x *UserKV) Reset() {
	*x = UserKV{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_v1_user_data_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserKV) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserKV) ProtoMessage() {}

func (x *UserKV) ProtoReflect() protoreflect.Message {
	mi := &file_data_v1_user_data_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserKV.ProtoReflect.Descriptor instead.
func (*UserKV) Descriptor() ([]byte, []int) {
	return file_data_v1_user_data_proto_rawDescGZIP(), []int{0}
}

func (x *UserKV) GetKeyPath() string {
	if x != nil {
		return x.KeyPath
	}
	return ""
}

func (x *UserKV) GetValueData() string {
	if x != nil {
		return x.ValueData
	}
	return ""
}

// 获取用户数据
// 用户键路径，使用 dot 分隔的 JSON 路径结构。
//
// 规则说明：
// - `key_path` 的第一级表示一个独立的 JSON 文档名（如 "profile"），限制最大256kb
// - 后续部分表示文档内的字段路径（如 "profile.theme.color" 表示 profile 文档中的 $.theme.color）
//
// 示例：
// - key_path = "profile"              → 返回完整 profile JSON 文档
// - key_path = "profile.theme"        → 返回 profile 文档中 $.theme 的值
// - key_path = "profile.theme.color"  → 返回 profile 文档中 $.theme.color 的值
//
// 更新请求遵循相同规则。
type GetDataReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	KeyPath string `protobuf:"bytes,1,opt,name=key_path,json=keyPath,proto3" json:"key_path,omitempty" dc:"以 dot 分隔的键路径，例如 'profile.theme.color'"` // 以 dot 分隔的键路径，例如 "profile.theme.color"
}

func (x *GetDataReq) Reset() {
	*x = GetDataReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_v1_user_data_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDataReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDataReq) ProtoMessage() {}

func (x *GetDataReq) ProtoReflect() protoreflect.Message {
	mi := &file_data_v1_user_data_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDataReq.ProtoReflect.Descriptor instead.
func (*GetDataReq) Descriptor() ([]byte, []int) {
	return file_data_v1_user_data_proto_rawDescGZIP(), []int{1}
}

func (x *GetDataReq) GetKeyPath() string {
	if x != nil {
		return x.KeyPath
	}
	return ""
}

type GetDataRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Kv    *UserKV       `protobuf:"bytes,4,opt,name=kv,proto3" json:"kv,omitempty"`
}

func (x *GetDataRes) Reset() {
	*x = GetDataRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_v1_user_data_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDataRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDataRes) ProtoMessage() {}

func (x *GetDataRes) ProtoReflect() protoreflect.Message {
	mi := &file_data_v1_user_data_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDataRes.ProtoReflect.Descriptor instead.
func (*GetDataRes) Descriptor() ([]byte, []int) {
	return file_data_v1_user_data_proto_rawDescGZIP(), []int{2}
}

func (x *GetDataRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetDataRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetDataRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *GetDataRes) GetKv() *UserKV {
	if x != nil {
		return x.Kv
	}
	return nil
}

// 更新数据
// 用户键路径，使用 dot 分隔的 JSON 路径结构。
//
// 规则说明：
// - `key_path` 的第一级表示一个独立的 JSON 文档名（如 "profile"），限制最大256kb
// - 后续部分表示文档内的字段路径（如 "profile.theme.color" 表示 profile 文档中的 $.theme.color）
//
// 示例：
// - key_path = "profile"              → 覆盖 JSON 文档
// - key_path = "profile.theme"        → 更新 $.theme 的值
// - key_path = "profile.theme.color"  → 更新 $.theme.color 的值
//
// 清空文档
// - key_path = "profile"
// - value_data = "{}"
type UpdateDataReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	KeyPath   string `protobuf:"bytes,1,opt,name=key_path,json=keyPath,proto3" json:"key_path,omitempty" dc:"以 dot 分隔的键路径，例如 'profile.theme.color'"`                                        // 以 dot 分隔的键路径，例如 "profile.theme.color"
	ValueData string `protobuf:"bytes,2,opt,name=value_data,json=valueData,proto3" json:"value_data,omitempty" dc:"对应键的 JSON 数据值，数组的数据以'['开头，json object数据以'{'开头，布尔值是true false, null表示空值"` // 对应键的 JSON 数据值，数组的数据以'['开头，json object数据以'{'开头，布尔值是true false, null表示空值
}

func (x *UpdateDataReq) Reset() {
	*x = UpdateDataReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_v1_user_data_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDataReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDataReq) ProtoMessage() {}

func (x *UpdateDataReq) ProtoReflect() protoreflect.Message {
	mi := &file_data_v1_user_data_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDataReq.ProtoReflect.Descriptor instead.
func (*UpdateDataReq) Descriptor() ([]byte, []int) {
	return file_data_v1_user_data_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateDataReq) GetKeyPath() string {
	if x != nil {
		return x.KeyPath
	}
	return ""
}

func (x *UpdateDataReq) GetValueData() string {
	if x != nil {
		return x.ValueData
	}
	return ""
}

type UpdateDataRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *UpdateDataRes) Reset() {
	*x = UpdateDataRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_v1_user_data_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDataRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDataRes) ProtoMessage() {}

func (x *UpdateDataRes) ProtoReflect() protoreflect.Message {
	mi := &file_data_v1_user_data_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDataRes.ProtoReflect.Descriptor instead.
func (*UpdateDataRes) Descriptor() ([]byte, []int) {
	return file_data_v1_user_data_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateDataRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UpdateDataRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *UpdateDataRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_data_v1_user_data_proto protoreflect.FileDescriptor

var file_data_v1_user_data_proto_rawDesc = []byte{
	0x0a, 0x17, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x64, 0x61, 0x74, 0x61, 0x2e,
	0x76, 0x31, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x42, 0x0a, 0x06, 0x55, 0x73, 0x65, 0x72, 0x4b, 0x56, 0x12,
	0x19, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6b, 0x65, 0x79, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x44, 0x61, 0x74, 0x61, 0x22, 0x27, 0x0a, 0x0a, 0x47, 0x65, 0x74,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x12, 0x19, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x5f, 0x70,
	0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6b, 0x65, 0x79, 0x50, 0x61,
	0x74, 0x68, 0x22, 0x78, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x1f, 0x0a, 0x02, 0x6b,
	0x76, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x4b, 0x56, 0x52, 0x02, 0x6b, 0x76, 0x22, 0x49, 0x0a, 0x0d,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x12, 0x19, 0x0a,
	0x08, 0x6b, 0x65, 0x79, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6b, 0x65, 0x79, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x44, 0x61, 0x74, 0x61, 0x22, 0x5a, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23,
	0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x32, 0x78, 0x0a, 0x0b, 0x44, 0x61, 0x74, 0x61, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x2f, 0x0a, 0x03, 0x47, 0x65, 0x74, 0x12, 0x13, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x1a, 0x13,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x65, 0x73, 0x12, 0x38, 0x0a, 0x06, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x16, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x42, 0x33, 0x5a,
	0x31, 0x68, 0x61, 0x6c, 0x61, 0x6c, 0x70, 0x6c, 0x75, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x75,
	0x73, 0x65, 0x72, 0x2d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2d, 0x73, 0x76, 0x63, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x3b, 0x64, 0x61, 0x74, 0x61,
	0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_data_v1_user_data_proto_rawDescOnce sync.Once
	file_data_v1_user_data_proto_rawDescData = file_data_v1_user_data_proto_rawDesc
)

func file_data_v1_user_data_proto_rawDescGZIP() []byte {
	file_data_v1_user_data_proto_rawDescOnce.Do(func() {
		file_data_v1_user_data_proto_rawDescData = protoimpl.X.CompressGZIP(file_data_v1_user_data_proto_rawDescData)
	})
	return file_data_v1_user_data_proto_rawDescData
}

var file_data_v1_user_data_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_data_v1_user_data_proto_goTypes = []interface{}{
	(*UserKV)(nil),        // 0: data.v1.UserKV
	(*GetDataReq)(nil),    // 1: data.v1.GetDataReq
	(*GetDataRes)(nil),    // 2: data.v1.GetDataRes
	(*UpdateDataReq)(nil), // 3: data.v1.UpdateDataReq
	(*UpdateDataRes)(nil), // 4: data.v1.UpdateDataRes
	(*common.Error)(nil),  // 5: common.Error
}
var file_data_v1_user_data_proto_depIdxs = []int32{
	5, // 0: data.v1.GetDataRes.error:type_name -> common.Error
	0, // 1: data.v1.GetDataRes.kv:type_name -> data.v1.UserKV
	5, // 2: data.v1.UpdateDataRes.error:type_name -> common.Error
	1, // 3: data.v1.DataService.Get:input_type -> data.v1.GetDataReq
	3, // 4: data.v1.DataService.Update:input_type -> data.v1.UpdateDataReq
	2, // 5: data.v1.DataService.Get:output_type -> data.v1.GetDataRes
	4, // 6: data.v1.DataService.Update:output_type -> data.v1.UpdateDataRes
	5, // [5:7] is the sub-list for method output_type
	3, // [3:5] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_data_v1_user_data_proto_init() }
func file_data_v1_user_data_proto_init() {
	if File_data_v1_user_data_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_data_v1_user_data_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserKV); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_v1_user_data_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDataReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_v1_user_data_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDataRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_v1_user_data_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDataReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_v1_user_data_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDataRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_data_v1_user_data_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_data_v1_user_data_proto_goTypes,
		DependencyIndexes: file_data_v1_user_data_proto_depIdxs,
		MessageInfos:      file_data_v1_user_data_proto_msgTypes,
	}.Build()
	File_data_v1_user_data_proto = out.File
	file_data_v1_user_data_proto_rawDesc = nil
	file_data_v1_user_data_proto_goTypes = nil
	file_data_v1_user_data_proto_depIdxs = nil
}
