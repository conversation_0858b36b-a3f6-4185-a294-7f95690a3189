// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: pbentity/user.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                    uint32 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`                                                                                //
	AgentId               uint32 `protobuf:"varint,2,opt,name=AgentId,proto3" json:"AgentId,omitempty" dc:"上级（代理）的id"`                                                       // 上级（代理）的id
	AgentCode             string `protobuf:"bytes,3,opt,name=AgentCode,proto3" json:"AgentCode,omitempty" dc:"注册时填写的代理码（选填）"`                                                // 注册时填写的代理码（选填）
	Account               string `protobuf:"bytes,4,opt,name=Account,proto3" json:"Account,omitempty" dc:"账号"`                                                               // 账号
	Password              string `protobuf:"bytes,5,opt,name=Password,proto3" json:"Password,omitempty" dc:"密码"`                                                             // 密码
	PayPassword           string `protobuf:"bytes,6,opt,name=PayPassword,proto3" json:"PayPassword,omitempty" dc:"支付密码"`                                                     // 支付密码
	PasswordModifyTime    int64  `protobuf:"varint,7,opt,name=PasswordModifyTime,proto3" json:"PasswordModifyTime,omitempty" dc:"登录密码支付密码最近修改时间"`                            // 登录密码支付密码最近修改时间
	AreaCode              string `protobuf:"bytes,8,opt,name=AreaCode,proto3" json:"AreaCode,omitempty" dc:"手机国际区号，如：86"`                                                    // 手机国际区号，如：86
	PhoneNum              string `protobuf:"bytes,9,opt,name=PhoneNum,proto3" json:"PhoneNum,omitempty" dc:"手机号"`                                                            // 手机号
	BindPhoneTime         int64  `protobuf:"varint,10,opt,name=BindPhoneTime,proto3" json:"BindPhoneTime,omitempty" dc:"手机号绑定时间"`                                            // 手机号绑定时间
	Email                 string `protobuf:"bytes,11,opt,name=Email,proto3" json:"Email,omitempty" dc:"邮箱地址"`                                                                // 邮箱地址
	BindEmailTime         int64  `protobuf:"varint,12,opt,name=BindEmailTime,proto3" json:"BindEmailTime,omitempty" dc:"邮箱绑定时间"`                                             // 邮箱绑定时间
	BindRealNameTime      int64  `protobuf:"varint,13,opt,name=BindRealNameTime,proto3" json:"BindRealNameTime,omitempty" dc:"真实姓名绑定时间"`                                     // 真实姓名绑定时间
	VipLevel              int32  `protobuf:"varint,14,opt,name=VipLevel,proto3" json:"VipLevel,omitempty" dc:"vip等级"`                                                        // vip等级
	LevelId               uint32 `protobuf:"varint,15,opt,name=LevelId,proto3" json:"LevelId,omitempty" dc:"会员层级id"`                                                         // 会员层级id
	IsBanned              int32  `protobuf:"varint,16,opt,name=IsBanned,proto3" json:"IsBanned,omitempty" dc:"账号封号状态： 1 正常 2 封号"`                                            // 账号封号状态： 1 正常 2 封号
	IsProhibit            int32  `protobuf:"varint,17,opt,name=IsProhibit,proto3" json:"IsProhibit,omitempty" dc:"提取状态：1 正常 2 禁提"`                                           // 提取状态：1 正常 2 禁提
	IsOnline              int32  `protobuf:"varint,18,opt,name=IsOnline,proto3" json:"IsOnline,omitempty" dc:"是否在线：1是 2 否"`                                                  // 是否在线：1是 2 否
	OnlineDuration        int64  `protobuf:"varint,19,opt,name=OnlineDuration,proto3" json:"OnlineDuration,omitempty" dc:"在线时长（单位：秒）"`                                       // 在线时长（单位：秒）
	SigninCount           int32  `protobuf:"varint,20,opt,name=SigninCount,proto3" json:"SigninCount,omitempty" dc:"登录次数"`                                                   // 登录次数
	LastSigninTime        int64  `protobuf:"varint,21,opt,name=LastSigninTime,proto3" json:"LastSigninTime,omitempty" dc:"最后一次登录时间"`                                         // 最后一次登录时间
	LastSigninIp          string `protobuf:"bytes,22,opt,name=LastSigninIp,proto3" json:"LastSigninIp,omitempty" dc:"最后登录ip"`                                                // 最后登录ip
	LastSigninDeviceId    string `protobuf:"bytes,23,opt,name=LastSigninDeviceId,proto3" json:"LastSigninDeviceId,omitempty" dc:"最后登录设备号"`                                   // 最后登录设备号
	LastSigninAppType     int32  `protobuf:"varint,24,opt,name=LastSigninAppType,proto3" json:"LastSigninAppType,omitempty" dc:"最近登录应用类型（1:android 2: ios，3:h5，4:web，5:其他）"` // 最近登录应用类型（1:android 2: ios，3:h5，4:web，5:其他）
	LastSigninAppVersion  string `protobuf:"bytes,25,opt,name=LastSigninAppVersion,proto3" json:"LastSigninAppVersion,omitempty" dc:"最近登录应用类型版本号"`                           // 最近登录应用类型版本号
	SignupIp              string `protobuf:"bytes,26,opt,name=SignupIp,proto3" json:"SignupIp,omitempty" dc:"注册ip"`                                                          // 注册ip
	SignupIpRegion        string `protobuf:"bytes,27,opt,name=SignupIpRegion,proto3" json:"SignupIpRegion,omitempty" dc:"注册IP地理区域"`                                          // 注册IP地理区域
	SignupDeviceId        string `protobuf:"bytes,28,opt,name=SignupDeviceId,proto3" json:"SignupDeviceId,omitempty" dc:"注册设备号（设备指纹）"`                                       // 注册设备号（设备指纹）
	SignupDeviceOs        string `protobuf:"bytes,29,opt,name=SignupDeviceOs,proto3" json:"SignupDeviceOs,omitempty" dc:"注册设备系统（android,ios,windows,mac,...）"`               // 注册设备系统（android,ios,windows,mac,...）
	SignupDeviceOsVersion string `protobuf:"bytes,30,opt,name=SignupDeviceOsVersion,proto3" json:"SignupDeviceOsVersion,omitempty" dc:"注册设备系统版本号"`                           // 注册设备系统版本号
	SignupDeviceType      int32  `protobuf:"varint,31,opt,name=SignupDeviceType,proto3" json:"SignupDeviceType,omitempty" dc:"注册设备类型（1:mobile手机，2:desktop台式，3:pad平板，。。。其他）"` // 注册设备类型（1:mobile手机，2:desktop台式，3:pad平板，。。。其他）
	SignupAppType         int32  `protobuf:"varint,32,opt,name=SignupAppType,proto3" json:"SignupAppType,omitempty" dc:"应用类型（1:android 2: ios，3:h5，4:web，5:其他）"`             // 应用类型（1:android 2: ios，3:h5，4:web，5:其他）
	SignupAppVersion      string `protobuf:"bytes,33,opt,name=SignupAppVersion,proto3" json:"SignupAppVersion,omitempty" dc:"注册应用类型版本号"`                                     // 注册应用类型版本号
	SignupHost            string `protobuf:"bytes,34,opt,name=SignupHost,proto3" json:"SignupHost,omitempty" dc:"注册域名(接口域名)"`                                                // 注册域名(接口域名)
	SignupDomain          string `protobuf:"bytes,35,opt,name=SignupDomain,proto3" json:"SignupDomain,omitempty" dc:"注册域名(页面原始域名)"`                                          // 注册域名(页面原始域名)
	SignupDomain2         string `protobuf:"bytes,36,opt,name=SignupDomain2,proto3" json:"SignupDomain2,omitempty" dc:"注册域名(页面域名)"`                                          // 注册域名(页面域名)
	LastSigninLogId       int32  `protobuf:"varint,37,opt,name=LastSigninLogId,proto3" json:"LastSigninLogId,omitempty" dc:"最后一次登录日志id（如果有分表的话，要考虑时间）"`                      // 最后一次登录日志id（如果有分表的话，要考虑时间）
	DeviceTokenIos        string `protobuf:"bytes,38,opt,name=DeviceTokenIos,proto3" json:"DeviceTokenIos,omitempty" dc:"IOS推送token"`                                        // IOS推送token
	DeviceTokenAndroid    string `protobuf:"bytes,39,opt,name=DeviceTokenAndroid,proto3" json:"DeviceTokenAndroid,omitempty" dc:"android推送token(FCM)"`                       // android推送token(FCM)
	SecurityPassword      string `protobuf:"bytes,40,opt,name=SecurityPassword,proto3" json:"SecurityPassword,omitempty" dc:"安全密码，修改个人绑定信息时要验证"`                             // 安全密码，修改个人绑定信息时要验证
	Version               int32  `protobuf:"varint,41,opt,name=Version,proto3" json:"Version,omitempty" dc:"该记录的版本号"`                                                        // 该记录的版本号
	IsTest                int32  `protobuf:"varint,42,opt,name=IsTest,proto3" json:"IsTest,omitempty" dc:"测试账号： 1 是 ，其他值：否"`                                                 // 测试账号： 1 是 ，其他值：否
	LimitStartTime        int64  `protobuf:"varint,43,opt,name=LimitStartTime,proto3" json:"LimitStartTime,omitempty" dc:"限制登录开始时间"`                                         // 限制登录开始时间
	LimitEndTime          int64  `protobuf:"varint,44,opt,name=LimitEndTime,proto3" json:"LimitEndTime,omitempty" dc:"限制登录结束时间"`                                             // 限制登录结束时间
	CreateTime            int64  `protobuf:"varint,45,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间（注册时间）"`                                               // 创建时间（注册时间）
	UpdateTime            int64  `protobuf:"varint,46,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"更新时间，0代表创建后未被修改过（哪些字段的更新会触发这个？）"`                          // 更新时间，0代表创建后未被修改过（哪些字段的更新会触发这个？）
	CreateAccount         string `protobuf:"bytes,47,opt,name=CreateAccount,proto3" json:"CreateAccount,omitempty" dc:"创建者账号"`                                               // 创建者账号
	CreateType            int32  `protobuf:"varint,48,opt,name=CreateType,proto3" json:"CreateType,omitempty" dc:"创建者来源(注册来源)（1会员注册user，2代理添加agent，3管理员添加admin）"`            // 创建者来源(注册来源)（1会员注册user，2代理添加agent，3管理员添加admin）
	UpdateAccount         string `protobuf:"bytes,49,opt,name=UpdateAccount,proto3" json:"UpdateAccount,omitempty" dc:"更新者账号"`                                               // 更新者账号
	UpdateType            int32  `protobuf:"varint,50,opt,name=UpdateType,proto3" json:"UpdateType,omitempty" dc:"更新者来源"`                                                    // 更新者来源
	InviteCode            string `protobuf:"bytes,51,opt,name=InviteCode,proto3" json:"InviteCode,omitempty" dc:"邀请码"`                                                       // 邀请码
	TransferCode          string `protobuf:"bytes,52,opt,name=TransferCode,proto3" json:"TransferCode,omitempty" dc:"转线码"`                                                   // 转线码
	NoobTaskFinishTime    int64  `protobuf:"varint,53,opt,name=NoobTaskFinishTime,proto3" json:"NoobTaskFinishTime,omitempty" dc:"新手任务完成时间"`                                 // 新手任务完成时间
	DataType              int32  `protobuf:"varint,54,opt,name=DataType,proto3" json:"DataType,omitempty" dc:"数据类型:1正式数据;2测试数据"`                                             // 数据类型:1正式数据;2测试数据
	PixelId               string `protobuf:"bytes,55,opt,name=PixelId,proto3" json:"PixelId,omitempty" dc:"像素id"`                                                            // 像素id
	Source                int32  `protobuf:"varint,56,opt,name=Source,proto3" json:"Source,omitempty" dc:"注册来源( 1直客，2代理，3邀请，4后台）"`                                           // 注册来源( 1直客，2代理，3邀请，4后台）
}

func (x *User) Reset() {
	*x = User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbentity_user_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_user_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_pbentity_user_proto_rawDescGZIP(), []int{0}
}

func (x *User) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *User) GetAgentId() uint32 {
	if x != nil {
		return x.AgentId
	}
	return 0
}

func (x *User) GetAgentCode() string {
	if x != nil {
		return x.AgentCode
	}
	return ""
}

func (x *User) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *User) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *User) GetPayPassword() string {
	if x != nil {
		return x.PayPassword
	}
	return ""
}

func (x *User) GetPasswordModifyTime() int64 {
	if x != nil {
		return x.PasswordModifyTime
	}
	return 0
}

func (x *User) GetAreaCode() string {
	if x != nil {
		return x.AreaCode
	}
	return ""
}

func (x *User) GetPhoneNum() string {
	if x != nil {
		return x.PhoneNum
	}
	return ""
}

func (x *User) GetBindPhoneTime() int64 {
	if x != nil {
		return x.BindPhoneTime
	}
	return 0
}

func (x *User) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *User) GetBindEmailTime() int64 {
	if x != nil {
		return x.BindEmailTime
	}
	return 0
}

func (x *User) GetBindRealNameTime() int64 {
	if x != nil {
		return x.BindRealNameTime
	}
	return 0
}

func (x *User) GetVipLevel() int32 {
	if x != nil {
		return x.VipLevel
	}
	return 0
}

func (x *User) GetLevelId() uint32 {
	if x != nil {
		return x.LevelId
	}
	return 0
}

func (x *User) GetIsBanned() int32 {
	if x != nil {
		return x.IsBanned
	}
	return 0
}

func (x *User) GetIsProhibit() int32 {
	if x != nil {
		return x.IsProhibit
	}
	return 0
}

func (x *User) GetIsOnline() int32 {
	if x != nil {
		return x.IsOnline
	}
	return 0
}

func (x *User) GetOnlineDuration() int64 {
	if x != nil {
		return x.OnlineDuration
	}
	return 0
}

func (x *User) GetSigninCount() int32 {
	if x != nil {
		return x.SigninCount
	}
	return 0
}

func (x *User) GetLastSigninTime() int64 {
	if x != nil {
		return x.LastSigninTime
	}
	return 0
}

func (x *User) GetLastSigninIp() string {
	if x != nil {
		return x.LastSigninIp
	}
	return ""
}

func (x *User) GetLastSigninDeviceId() string {
	if x != nil {
		return x.LastSigninDeviceId
	}
	return ""
}

func (x *User) GetLastSigninAppType() int32 {
	if x != nil {
		return x.LastSigninAppType
	}
	return 0
}

func (x *User) GetLastSigninAppVersion() string {
	if x != nil {
		return x.LastSigninAppVersion
	}
	return ""
}

func (x *User) GetSignupIp() string {
	if x != nil {
		return x.SignupIp
	}
	return ""
}

func (x *User) GetSignupIpRegion() string {
	if x != nil {
		return x.SignupIpRegion
	}
	return ""
}

func (x *User) GetSignupDeviceId() string {
	if x != nil {
		return x.SignupDeviceId
	}
	return ""
}

func (x *User) GetSignupDeviceOs() string {
	if x != nil {
		return x.SignupDeviceOs
	}
	return ""
}

func (x *User) GetSignupDeviceOsVersion() string {
	if x != nil {
		return x.SignupDeviceOsVersion
	}
	return ""
}

func (x *User) GetSignupDeviceType() int32 {
	if x != nil {
		return x.SignupDeviceType
	}
	return 0
}

func (x *User) GetSignupAppType() int32 {
	if x != nil {
		return x.SignupAppType
	}
	return 0
}

func (x *User) GetSignupAppVersion() string {
	if x != nil {
		return x.SignupAppVersion
	}
	return ""
}

func (x *User) GetSignupHost() string {
	if x != nil {
		return x.SignupHost
	}
	return ""
}

func (x *User) GetSignupDomain() string {
	if x != nil {
		return x.SignupDomain
	}
	return ""
}

func (x *User) GetSignupDomain2() string {
	if x != nil {
		return x.SignupDomain2
	}
	return ""
}

func (x *User) GetLastSigninLogId() int32 {
	if x != nil {
		return x.LastSigninLogId
	}
	return 0
}

func (x *User) GetDeviceTokenIos() string {
	if x != nil {
		return x.DeviceTokenIos
	}
	return ""
}

func (x *User) GetDeviceTokenAndroid() string {
	if x != nil {
		return x.DeviceTokenAndroid
	}
	return ""
}

func (x *User) GetSecurityPassword() string {
	if x != nil {
		return x.SecurityPassword
	}
	return ""
}

func (x *User) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *User) GetIsTest() int32 {
	if x != nil {
		return x.IsTest
	}
	return 0
}

func (x *User) GetLimitStartTime() int64 {
	if x != nil {
		return x.LimitStartTime
	}
	return 0
}

func (x *User) GetLimitEndTime() int64 {
	if x != nil {
		return x.LimitEndTime
	}
	return 0
}

func (x *User) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *User) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *User) GetCreateAccount() string {
	if x != nil {
		return x.CreateAccount
	}
	return ""
}

func (x *User) GetCreateType() int32 {
	if x != nil {
		return x.CreateType
	}
	return 0
}

func (x *User) GetUpdateAccount() string {
	if x != nil {
		return x.UpdateAccount
	}
	return ""
}

func (x *User) GetUpdateType() int32 {
	if x != nil {
		return x.UpdateType
	}
	return 0
}

func (x *User) GetInviteCode() string {
	if x != nil {
		return x.InviteCode
	}
	return ""
}

func (x *User) GetTransferCode() string {
	if x != nil {
		return x.TransferCode
	}
	return ""
}

func (x *User) GetNoobTaskFinishTime() int64 {
	if x != nil {
		return x.NoobTaskFinishTime
	}
	return 0
}

func (x *User) GetDataType() int32 {
	if x != nil {
		return x.DataType
	}
	return 0
}

func (x *User) GetPixelId() string {
	if x != nil {
		return x.PixelId
	}
	return ""
}

func (x *User) GetSource() int32 {
	if x != nil {
		return x.Source
	}
	return 0
}

var File_pbentity_user_proto protoreflect.FileDescriptor

var file_pbentity_user_proto_rawDesc = []byte{
	0x0a, 0x13, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x22,
	0xbe, 0x0f, 0x0a, 0x04, 0x55, 0x73, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x61,
	0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x61,
	0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x50, 0x61, 0x79, 0x50, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x50, 0x61, 0x79,
	0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x2e, 0x0a, 0x12, 0x50, 0x61, 0x73, 0x73,
	0x77, 0x6f, 0x72, 0x64, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x4d, 0x6f,
	0x64, 0x69, 0x66, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x41, 0x72, 0x65, 0x61,
	0x43, 0x6f, 0x64, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x41, 0x72, 0x65, 0x61,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d,
	0x12, 0x24, 0x0a, 0x0d, 0x42, 0x69, 0x6e, 0x64, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x42, 0x69, 0x6e, 0x64, 0x50, 0x68, 0x6f,
	0x6e, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x24, 0x0a, 0x0d,
	0x42, 0x69, 0x6e, 0x64, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0d, 0x42, 0x69, 0x6e, 0x64, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x42, 0x69, 0x6e, 0x64, 0x52, 0x65, 0x61, 0x6c, 0x4e, 0x61,
	0x6d, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x42, 0x69,
	0x6e, 0x64, 0x52, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x56, 0x69, 0x70, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x56, 0x69, 0x70, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x4c, 0x65,
	0x76, 0x65, 0x6c, 0x49, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x4c, 0x65, 0x76,
	0x65, 0x6c, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x49, 0x73, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x64,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x49, 0x73, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x64,
	0x12, 0x1e, 0x0a, 0x0a, 0x49, 0x73, 0x50, 0x72, 0x6f, 0x68, 0x69, 0x62, 0x69, 0x74, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x49, 0x73, 0x50, 0x72, 0x6f, 0x68, 0x69, 0x62, 0x69, 0x74,
	0x12, 0x1a, 0x0a, 0x08, 0x49, 0x73, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x49, 0x73, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x26, 0x0a, 0x0e,
	0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x13,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x53, 0x69, 0x67, 0x6e, 0x69,
	0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x4c, 0x61, 0x73, 0x74, 0x53, 0x69,
	0x67, 0x6e, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e,
	0x4c, 0x61, 0x73, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22,
	0x0a, 0x0c, 0x4c, 0x61, 0x73, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x49, 0x70, 0x18, 0x16,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x4c, 0x61, 0x73, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e,
	0x49, 0x70, 0x12, 0x2e, 0x0a, 0x12, 0x4c, 0x61, 0x73, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12,
	0x4c, 0x61, 0x73, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x64, 0x12, 0x2c, 0x0a, 0x11, 0x4c, 0x61, 0x73, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e,
	0x41, 0x70, 0x70, 0x54, 0x79, 0x70, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x4c,
	0x61, 0x73, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x41, 0x70, 0x70, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x32, 0x0a, 0x14, 0x4c, 0x61, 0x73, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x41, 0x70,
	0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14,
	0x4c, 0x61, 0x73, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x53, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x49, 0x70,
	0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x53, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x49, 0x70,
	0x12, 0x26, 0x0a, 0x0e, 0x53, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x49, 0x70, 0x52, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x53, 0x69, 0x67, 0x6e, 0x75, 0x70,
	0x49, 0x70, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0e, 0x53, 0x69, 0x67, 0x6e,
	0x75, 0x70, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x53, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x26, 0x0a, 0x0e, 0x53, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x4f, 0x73, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x53, 0x69, 0x67, 0x6e, 0x75, 0x70,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x73, 0x12, 0x34, 0x0a, 0x15, 0x53, 0x69, 0x67, 0x6e,
	0x75, 0x70, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x53, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2a,
	0x0a, 0x10, 0x53, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x53, 0x69, 0x67, 0x6e, 0x75, 0x70,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x53, 0x69,
	0x67, 0x6e, 0x75, 0x70, 0x41, 0x70, 0x70, 0x54, 0x79, 0x70, 0x65, 0x18, 0x20, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0d, 0x53, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x41, 0x70, 0x70, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x2a, 0x0a, 0x10, 0x53, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x53, 0x69, 0x67, 0x6e,
	0x75, 0x70, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a,
	0x53, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x48, 0x6f, 0x73, 0x74, 0x18, 0x22, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x53, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x48, 0x6f, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x0c,
	0x53, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x23, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x53, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x12, 0x24, 0x0a, 0x0d, 0x53, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x32, 0x18, 0x24, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x53, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x44,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x32, 0x12, 0x28, 0x0a, 0x0f, 0x4c, 0x61, 0x73, 0x74, 0x53, 0x69,
	0x67, 0x6e, 0x69, 0x6e, 0x4c, 0x6f, 0x67, 0x49, 0x64, 0x18, 0x25, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0f, 0x4c, 0x61, 0x73, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x4c, 0x6f, 0x67, 0x49, 0x64,
	0x12, 0x26, 0x0a, 0x0e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x49,
	0x6f, 0x73, 0x18, 0x26, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x6f, 0x73, 0x12, 0x2e, 0x0a, 0x12, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x41, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x18, 0x27,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x41, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x10, 0x53, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x79, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x28, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x10, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x50, 0x61, 0x73, 0x73,
	0x77, 0x6f, 0x72, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x29, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x16,
	0x0a, 0x06, 0x49, 0x73, 0x54, 0x65, 0x73, 0x74, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x49, 0x73, 0x54, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22,
	0x0a, 0x0c, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x2c,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x45, 0x6e, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x2d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x2e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x2f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x30, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x31, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1e,
	0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x32, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e,
	0x0a, 0x0a, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x33, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x22,
	0x0a, 0x0c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x34,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x2e, 0x0a, 0x12, 0x4e, 0x6f, 0x6f, 0x62, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x69,
	0x6e, 0x69, 0x73, 0x68, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x35, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12,
	0x4e, 0x6f, 0x6f, 0x62, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x18, 0x36,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x50, 0x69, 0x78, 0x65, 0x6c, 0x49, 0x64, 0x18, 0x37, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x50, 0x69, 0x78, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x18, 0x38, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x42, 0x2d, 0x5a, 0x2b, 0x68, 0x61, 0x6c, 0x61, 0x6c, 0x70, 0x6c, 0x75, 0x73, 0x2f, 0x61, 0x70,
	0x70, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2d, 0x73,
	0x76, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbentity_user_proto_rawDescOnce sync.Once
	file_pbentity_user_proto_rawDescData = file_pbentity_user_proto_rawDesc
)

func file_pbentity_user_proto_rawDescGZIP() []byte {
	file_pbentity_user_proto_rawDescOnce.Do(func() {
		file_pbentity_user_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbentity_user_proto_rawDescData)
	})
	return file_pbentity_user_proto_rawDescData
}

var file_pbentity_user_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_user_proto_goTypes = []interface{}{
	(*User)(nil), // 0: pbentity.User
}
var file_pbentity_user_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_user_proto_init() }
func file_pbentity_user_proto_init() {
	if File_pbentity_user_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbentity_user_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbentity_user_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_user_proto_goTypes,
		DependencyIndexes: file_pbentity_user_proto_depIdxs,
		MessageInfos:      file_pbentity_user_proto_msgTypes,
	}.Build()
	File_pbentity_user_proto = out.File
	file_pbentity_user_proto_rawDesc = nil
	file_pbentity_user_proto_goTypes = nil
	file_pbentity_user_proto_depIdxs = nil
}
