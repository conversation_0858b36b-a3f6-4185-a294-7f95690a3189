// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	"halalplus/app/user-account-svc/internal/model"
)

type (
	IUserData interface {
		GetUserData(ctx context.Context, uid uint64, keyPath string) (*model.UserKvData, error)
		UpdateUserData(ctx context.Context, userKvData *model.UserKvData) error
	}
)

var (
	localUserData IUserData
)

func UserData() IUserData {
	if localUserData == nil {
		panic("implement not found for interface IUserData, forgot register?")
	}
	return localUserData
}

func RegisterUserData(i IUserData) {
	localUserData = i
}
