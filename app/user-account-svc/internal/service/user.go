// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	"halalplus/app/user-account-svc/internal/model"
	"halalplus/app/user-account-svc/internal/model/do"
	"halalplus/app/user-account-svc/internal/model/entity"
)

type (
	IUser interface {
		// CreateByPhone 用户注册（手机号）
		CreateByPhone(ctx context.Context, in *model.SignUpByPhoneReq) (userId uint64, err error)
		NewUser(ctx context.Context, in *model.FrontInfo, originalDomain string) (ret do.User)
		ComposeUser(ctx context.Context, user *entity.User) (userModel *model.User)
		PushUserAttr(itm *model.AttrsToUpdate, delay bool)
		SignInByPhoneNum(ctx context.Context, areaCode string, phoneNum string, frontInfo *model.FrontInfo) (userModel *model.User, token string, err error)
		PushSignInLog(ctx context.Context, usr *entity.User, front *model.FrontInfo)
		// IsSignedIn checks and returns whether current user is already signed-in.
		IsSignedIn(ctx context.Context) bool
		IsPhoneAccountExist(ctx context.Context, phoneNum string, account string) (isUnique bool, err error)
		// GetUserInfo 获取用户信息
		GetUserInfo(ctx context.Context, uid uint64) (userInfo *model.UserInfo, err error)
	}
)

var (
	localUser IUser
)

func User() IUser {
	if localUser == nil {
		panic("implement not found for interface IUser, forgot register?")
	}
	return localUser
}

func RegisterUser(i IUser) {
	localUser = i
}
