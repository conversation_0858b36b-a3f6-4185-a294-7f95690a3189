// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	"halalplus/app/user-account-svc/internal/model/entity"

	"github.com/gogf/gf/v2/database/gdb"
)

type (
	IUserAppSettings interface {
		SelfByUid(ctx context.Context, uid uint64, isFromDb bool) (rec entity.UserAppSettings, err error)
		InitUserAppSettings(ctx context.Context, uid uint64, account string, lang string, homeDisplay int, tx gdb.TX) (err error)
	}
)

var (
	localUserAppSettings IUserAppSettings
)

func UserAppSettings() IUserAppSettings {
	if localUserAppSettings == nil {
		panic("implement not found for interface IUserAppSettings, forgot register?")
	}
	return localUserAppSettings
}

func RegisterUserAppSettings(i IUserAppSettings) {
	localUserAppSettings = i
}
