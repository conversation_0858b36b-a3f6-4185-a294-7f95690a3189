// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	"halalplus/app/user-account-svc/internal/model"

	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/net/ghttp"
)

type (
	IUtility interface {
		// ----OptCode
		GenerateCaptcha() (id string, b64s string, err error)
		VerifyCaptcha(id string, answer string, clear bool) (match bool)
		VerifySmsCaptcha(areaCode string, phoneNum string, smsCaptcha string, clear bool) (match bool)
		// ---- JWT
		GenerateJWT(id uint64, ty string, secretKey string) (token string, err error)
		ParseJWT(token string, secretKey string) (*model.JWTD, error)
		GenerateResetPasswordJWT(claims model.JWTResetPwd, secretKey string) (token string, err error)
		ParseResetPasswordJWT(token string, secretKey string) (*model.JWTResetPwd, error)
		// ---- Password
		HashPassword(password string) (string, error)
		CheckPasswordHash(password string, hash string) bool
		GetClientIp(r *ghttp.Request) string
		// 查找字符串是否在切片及在切片中的位置
		StringSliceFind(slice []string, val string) (int, bool)
		// 查找int是否在切片及切片中的位置
		IntSliceFind(slice []int, val int) (int, bool)
		// 检测服务器自身网络是否异常
		NetWorkStatus() bool
		// ctx中获取self
		GetSelf(ctx context.Context) (usr *model.User, err error)
		// ctx中获取self
		IsVisitor(ctx context.Context) bool
		// GetSelfAccount 当前账号
		GetSelfAccount(ctx context.Context) (account string)
		// GetCurrentLanguageCode 当前用户的语言编码
		GetCurrentLanguageCode(ctx context.Context) (language string)
		// GetCurrentAppType 当前用户的设置类型
		GetCurrentAppType(ctx context.Context) (appType int)
		// GetAppTheme 获取当前UI主题
		GetAppTheme(ctx context.Context) (appTheme string)
		// 比较两个浮点数是否相等
		IsEqualFloat64(a float64, b float64) (isEqual bool)
		// GenerateOrderId 生成订单
		GenerateOrderId(ctx context.Context, userId uint) (orderId *gvar.Var)
		GetIPRegion(ctx context.Context, ip string) (region string)
		GetIPCity(ctx context.Context, ip string) (city string)
		// 获取host
		GetHost(ctx context.Context) (host string)
		// GetUserVirtuallyEmail 获取当前登录用户的虚拟邮箱 // 不放这
		GetUserVirtuallyEmail(ctx context.Context) (email string, err error)
		// 组装url
		BuildUpUrl(prefixUrl string, params string) string
		GenerateAccountAuthJWT(value string) (token string, err error)
		ParseAccountAuthJWT(token string) (*model.JWTAccountAuth, error)
		GenerateJWTByRegisteredPhone(account string, authWay int) (token string, err error)
		VerityRegisteredPhoneJWT(ctx context.Context, token string, authWay int) (err error)
		// Md5Encode 使用MD5算法对传入的字符串进行加密，并返回加密后的字符串
		Md5Encode(str string) string
		// 检查一个ip是否在一批ip里或ip段里
		// 单个ip；ip段 x.x.x.x-x.x.x.x；子网掩码 x.x.x.x/x
		IsInIps(ips []string, ip string) bool
		// IsFieldValueExist 检查一个表中是否有指定字段的值；前置条件：表名和字段名必须正确
		IsFieldValueExist(ctx context.Context, in model.IsFieldValueExistInput) (bool, error)
		GetDomain(ctx context.Context) (ret *model.RequestDomain, err error)
	}
)

var (
	localUtility IUtility
)

func Utility() IUtility {
	if localUtility == nil {
		panic("implement not found for interface IUtility, forgot register?")
	}
	return localUtility
}

func RegisterUtility(i IUtility) {
	localUtility = i
}
