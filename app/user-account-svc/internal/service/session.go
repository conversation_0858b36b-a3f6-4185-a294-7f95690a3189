// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	"halalplus/app/user-account-svc/internal/model"
	"halalplus/app/user-account-svc/internal/model/entity"
)

type (
	ISession interface {
		CreateUserSession(ctx context.Context, user *entity.User, frontInfo *model.FrontInfo) (userSession *model.UserSession, err error)
		// GenJwtToken _登录：生成token，token存入缓存
		GenJwtToken(ctx context.Context, userSession *model.UserSession) (token string, err error)
		// RefreshJwtToken 更新token
		RefreshJwtToken(ctx context.Context, sessionId string, frontInfo *model.FrontInfo) (token string, err error)
		// GetUserIdByToken 从 gRPC 上下文中提取 JWT token 并解析出用户 ID。
		// NOTICE: 必须在 gRPC controller 中使用，依赖 grpcx.Ctx.IncomingMap(ctx)
		GetUserIdByToken(ctx context.Context) (userId uint64, err error)
	}
)

var (
	localSession ISession
)

func Session() ISession {
	if localSession == nil {
		panic("implement not found for interface ISession, forgot register?")
	}
	return localSession
}

func RegisterSession(i ISession) {
	localSession = i
}
