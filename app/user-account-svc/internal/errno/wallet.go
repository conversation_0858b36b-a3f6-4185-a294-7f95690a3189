package errno

import (
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
)

var (
	CodeUserWalletNotFound             = gcode.New(19001, "use.wallet.not.find", nil)                // 用户钱包不存在
	CodeUserWalletUpdateError          = gcode.New(19002, "use.wallet.update.error", nil)            // 用户钱包更新失败
	CodeUserWalletMainBalanceNotEnough = gcode.New(19003, "use.wallet.main.balance.not.enough", nil) // 用户钱包主额度不足
	CodeUserWalletAccountException     = gcode.New(19005, "use.wallet.account.exception", nil)       // 用户钱包账户异常
)

var (
	ErrorUserWalletMainBalanceNotEnough = gerror.NewCode(CodeUserWalletMainBalanceNotEnough)
)
