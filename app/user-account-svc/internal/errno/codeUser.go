package errno

import (
	"github.com/gogf/gf/v2/errors/gcode"
)

var (
	CodeUserNotFoundError               = gcode.New(15000, "user.not.found", nil)                         // 找不到用户
	CodeOptCodeError                    = gcode.New(15001, "account.opt.code.expired.incorrect", nil)     // 验证OPT失败
	CodeVerifyAccessTokenError          = gcode.New(15002, "account.access.token.expired.incorrect", nil) // 验证AccessToken失败
	CodeUserLoginBen                    = gcode.New(15003, "user.login.ban.time", nil)                    // 用户禁止登录
	CodeUserAccountExisted              = gcode.New(15004, "user.account.account.existed", nil)           // 用户账户已经存在
	CodeUserPhoneNumberExisted          = gcode.New(15005, "user.account.phoneNum.existed", nil)          // 用户手机号码已经存在
	CodeUserPhoneNumberNotExisted       = gcode.New(15007, "account.phone.unregistered", nil)             // 用户手机号码不存在
	CodeUserPayPasswordMustBeSet        = gcode.New(15008, "user.pay.password.must.be.set", nil)          // 用户支付密码必须设置
	CodePaymentPasswordError            = gcode.New(15009, "user.payment.password.error", nil)            // 验证支付密码错误失败
	CodeBankCardMaxLimit                = gcode.New(15010, "use.bankcard.max.limit.num", nil)             // 用户最多有N张银行卡
	CodeCoinAddressMaxLimit             = gcode.New(15010, "use.coinAddress.max.limit.num", nil)          // 用户最多有N虚拟币地址
	CodeUserBankCardAccountNumDuplicate = gcode.New(15011, "use.bankcard.accountNum.duplicate", nil)      // 用户银行卡号重复
	CodeUserCoinAddressDuplicate        = gcode.New(15011, "use.coinAddress.duplicate", nil)              // 用户虚拟币地址号重复
	CodeUserPhoneNumberMustBeSet        = gcode.New(15012, "use.account.phoneNum.must.be.set", nil)       // 用户手机号码必须设置
	CodeUserBankCardIdNotFound          = gcode.New(15013, "use.bankcard.id.not.find", nil)               // 用户银行卡号不存在
	CodeUserCoinAddressIdNotFound       = gcode.New(15013, "use.coinAddress.id.not.find", nil)            // 用户虚拟币地址不存在
	CodeUserNameExisted                 = gcode.New(15014, "user.account.name.existed", nil)              // 用户姓名已经存在
)
