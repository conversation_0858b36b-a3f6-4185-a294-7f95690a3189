package errno

import "github.com/gogf/gf/v2/errors/gcode"

var (
	CodePayWayError                       = gcode.New(16000, "pay.way.error", nil)                          // 支付方式错误
	CodePayWithdrawError                  = gcode.New(16001, "pay.withdraw.error", nil)                     // 提现错误
	CodePayWithdrawAmountError            = gcode.New(16003, "pay.withdraw.amount.error", nil)              // 提现金额错误
	CodePayWithdrawBlock                  = gcode.New(16004, "pay.withdraw.block", nil)                     // 提现禁止
	CodePayWayNotFind                     = gcode.New(16006, "pay.way.not.find", nil)                       // 没有找到可用的支付方式
	CodePayChannelNotFind                 = gcode.New(16007, "pay.channel.not.find", nil)                   // 没有找到可用的支付渠道
	CodePayChannelError                   = gcode.New(16008, "pay.channel.err", nil)                        // 没有找到可用的支付渠道
	CodePaySysBankNotFind                 = gcode.New(16009, "pay.bank.not.find", nil)                      // 没有找到指定的银行
	CodeCurrencyRateNotFind               = gcode.New(16010, "pay.currency.rate.not.find", nil)             // 没有找到指定的汇率
	CodePayRechargeOrderNotNeedConfirm    = gcode.New(16011, "pay.recharge.order.not.need.confirm", nil)    // 充值订单不需要确认
	CodePayRechargeOrderConfirmed         = gcode.New(16012, "pay.recharge.order.confirmed", nil)           // 充值订单已经确认
	CodePayRechargeOrderInfoExpired       = gcode.New(16013, "pay.recharge.order.info.expired", nil)        // 充值订单信息过期
	CodePayRechargeOrderWaitBlock         = gcode.New(16014, "pay.recharge.order.wait.block", nil)          // 有待处理的充值订单
	CodePayWithdrawDeficitAmountError     = gcode.New(16015, "pay.withdraw.deficit.amount.error", nil)      // 提现不满足流水
	CodePayWithdrawDeficitAmountNameError = gcode.New(16015, "pay.withdraw.deficit.amount.name.error", nil) // 提现不满足流水(场馆名称)
	CodePayRechargeCouponUnavailable      = gcode.New(16016, "pay.recharge.coupon.unavailable", nil)        // 充值优惠券不可用
	CodePayRechargeError                  = gcode.New(16017, "pay.recharge.error", nil)                     // 充值失败
)
