package userAppSettings

import (
	"context"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"halalplus/app/user-account-svc/internal/consts"
	"halalplus/app/user-account-svc/internal/dao"
	"halalplus/app/user-account-svc/internal/model"
	"halalplus/app/user-account-svc/internal/model/do"
	"halalplus/app/user-account-svc/internal/model/entity"
	"halalplus/app/user-account-svc/internal/service"
	"time"
)

type (
	sUserAppSettings struct {
		attrsBatchUpdateChan chan *model.AttrsToUpdate
		quit                 chan struct{}
	}
)

func init() {
	service.RegisterUserAppSettings(New())
}

var cl = dao.UserAppSettings.Columns()

func New() service.IUserAppSettings {
	u := &sUserAppSettings{
		attrsBatchUpdateChan: make(chan *model.AttrsToUpdate, 10000),
		quit:                 make(chan struct{}, 1),
	}
	//u.cronJobUpdateUserAttrs()
	// u.syncUserAppSettings() // 由于已经将这个记录加入创建用户的事务中，所以不需要再单独调用
	return u
}

func (s *sUserAppSettings) SelfByUid(ctx context.Context, uid uint64, isFromDb bool) (rec entity.UserAppSettings, err error) {
	key := consts.KeyUserAppSetting(uid)
	md := dao.UserAppSettings.Ctx(ctx).Master()
	if isFromDb {
		cacheOpt := gdb.CacheOption{
			Duration: consts.MiddleCacheEx,
			Name:     key,
			Force:    true,
		}
		md = md.Cache(cacheOpt)
	}

	err = md.WherePri(uid).Scan(&rec)
	return
}

func (s *sUserAppSettings) InitUserAppSettings(ctx context.Context, uid uint64, account, lang string, homeDisplay int, tx gdb.TX) (err error) {
	//isValid := service.Sys().IsLanguageExist(ctx, lang)
	//if !isValid {
	//	lang = consts.DefaultLanguage
	//}
	lang = consts.DefaultLanguage
	item := do.UserAppSettings{
		Id:             uid,
		Account:        account,
		Gender:         "0",
		CountryId:      consts.DefaultCountryId,
		Currency:       consts.DefaultCurrency,
		Language:       lang,
		IsWithdrawAuth: consts.IsNo,
		CreateTime:     time.Now().UnixMilli(),
	}

	_, err = dao.UserAppSettings.Ctx(ctx).TX(tx).Insert(item)
	if err != nil {
		g.Log().Line().Error(ctx, "InitUserAppSettings item=%+v, err:%v", item, err)
	}

	return
}
