package userdata

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"halalplus/app/user-account-svc/internal/dao"
	"halalplus/app/user-account-svc/internal/model"
	"halalplus/app/user-account-svc/internal/service"
)

type (
	sUserData struct {
	}
)

func init() {
	service.RegisterUserData(New())
}

func New() service.IUserData {
	u := &sUserData{}
	return u
}

func (s *sUserData) GetUserData(ctx context.Context, uid uint64, keyPath string) (*model.UserKvData, error) {
	kvModel, err := dao.UserKvData.GetByKeyPath(ctx, uid, keyPath)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}
	return kvModel, nil
}

func (s *sUserData) UpdateUserData(ctx context.Context, userKvData *model.UserKvData) error {
	err := dao.UserKvData.UpdateByKeyPath(ctx, userKvData)
	return err
}
