package session

import (
	"context"
	"errors"
	"github.com/gogf/gf/contrib/rpc/grpcx/v2"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"halalplus/app/user-account-svc/internal/consts"
	"halalplus/app/user-account-svc/internal/dao"
	"halalplus/app/user-account-svc/internal/errno"
	"halalplus/app/user-account-svc/internal/model"
	"halalplus/app/user-account-svc/internal/model/do"
	"halalplus/app/user-account-svc/internal/model/entity"
	"halalplus/app/user-account-svc/internal/service"
	"halalplus/utility"
	"strings"
	"time"
)

type (
	sSession struct {
	}
)

func init() {
	service.RegisterSession(New())
}

func New() service.ISession {
	u := &sSession{}
	return u
}

func (s *sSession) CreateUserSession(ctx context.Context, user *entity.User, frontInfo *model.FrontInfo) (userSession *model.UserSession, err error) {
	session := do.UserSession{
		SessionId:      utility.GenerateRandomString(24),
		SecretKey:      utility.GenerateRandomString(36),
		UserId:         user.Id,
		LastActiveTime: time.Now().UnixMilli(),
		LoginTime:      time.Now().UnixMilli(),
		DeviceId:       frontInfo.DeviceId,
		DeviceOs:       frontInfo.DeviceOs,   // 设备系统
		DeviceType:     frontInfo.DeviceType, // 设备类型（mobile/desktop/pad等）
		AppType:        frontInfo.AppType,    // 应用类型（android/ios/h5/web等）
		AppVersion:     frontInfo.AppVersion, // 应用版本
	}
	_, err = dao.UserSession.Ctx(ctx).Insert(session)
	if err != nil {
		g.Log().Error(ctx, "插入UserSession失败", err)
		return nil, err
	}

	userSession = &model.UserSession{}
	err = gconv.Struct(session, userSession)
	return userSession, err
}

// GenJwtToken _登录：生成token，token存入缓存
func (s *sSession) GenJwtToken(ctx context.Context, userSession *model.UserSession) (token string, err error) {
	// 生成token
	token, err = service.Utility().GenerateJWT(userSession.UserId, "user", consts.JWTSecretKey)
	if err != nil {
		g.Log().Error(ctx, err)
		return
	}
	// token写入redis
	g.Log().Debug(ctx, "token写入redis")
	err = g.Redis().SetEX(ctx, consts.KeyUserToken(userSession.UserId, userSession.SessionId), token, consts.TokenRedisEx)
	if err != nil {
		g.Log().Error(ctx, err)
		return
	}
	return
}

// RefreshJwtToken 更新token
func (s *sSession) RefreshJwtToken(ctx context.Context, sessionId string, frontInfo *model.FrontInfo) (token string, err error) {
	var userSession = new(entity.UserSession)
	err = dao.UserSession.Ctx(ctx).Where(dao.UserSession.Columns().SessionId, sessionId).Scan(userSession)
	if err != nil {
		g.Log().Error(ctx, err)
		return "", err
	}
	if userSession.DeviceId != frontInfo.DeviceId {
		// 设备号变更
		return "", errors.New("请重新登录")
	}

	// 更新活跃时间
	_, err = dao.UserSession.Ctx(ctx).Where(dao.UserSession.Columns().SessionId, sessionId).Update(do.UserSession{
		LastActiveTime: time.Now().UnixMilli(),
	})
	if err != nil {
		g.Log().Warning(ctx, "更新UserSession活跃时间", err)
	}

	return s.GenJwtToken(ctx, &model.UserSession{
		SessionId: userSession.SessionId,
		UserId:    userSession.UserId,
	})
}

// GetUserIdByToken 从 gRPC 上下文中提取 JWT token 并解析出用户 ID。
// NOTICE: 必须在 gRPC controller 中使用，依赖 grpcx.Ctx.IncomingMap(ctx)
func (s *sSession) GetUserIdByToken(ctx context.Context) (userId uint64, err error) {
	headerMap := grpcx.Ctx.IncomingMap(ctx)

	// 安全获取 token 字段
	ok := headerMap.Contains(consts.HttpToken)
	if !ok {
		return 0, errno.T(ctx, errno.CodeVerifyAccessTokenError)
	}

	token, ok := headerMap.Get(consts.HttpToken).(string)
	if !ok || len(token) == 0 {
		return 0, errno.T(ctx, errno.CodeVerifyAccessTokenError)
	}

	// 去除 Bearer 前缀
	token = strings.TrimPrefix(token, "Bearer ")
	if len(token) == 0 {
		return 0, errno.T(ctx, errno.CodeVerifyAccessTokenError)
	}

	// 解析 JWT
	jwtd, err := service.Utility().ParseJWT(token, consts.JWTSecretKey)
	if err != nil {
		g.Log().Debug(ctx, "failed to parse JWT", err)
		return 0, errno.T(ctx, errno.CodeVerifyAccessTokenError)
	}

	// 验证类型是否为用户类型
	if jwtd.Ty != "user" {
		g.Log().Debug(ctx, "invalid JWT type", jwtd.Ty)
		return 0, errno.T(ctx, errno.CodeVerifyAccessTokenError)
	}

	// 获取 subject（userId）
	sub, err := jwtd.GetSubject()
	if err != nil || g.IsEmpty(sub) {
		g.Log().Debug(ctx, "invalid JWT subject", jwtd)
		return 0, errno.T(ctx, errno.CodeVerifyAccessTokenError)
	}

	return gconv.Uint64(sub), nil
}
