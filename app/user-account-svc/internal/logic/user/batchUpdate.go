package user

import (
	"github.com/gogf/gf/v2/frame/g"
	"halalplus/app/user-account-svc/internal/model"
)

func (s *sUser) PushUserAttr(itm *model.AttrsToUpdate, delay bool) {
	if itm == nil || itm.UserId <= 0 {
		g.Log().Line().Warning(itm.Ctx, "PushUserAttr failed! user id is invalid")
		return
	}
	// FIXME
	return
	if delay {
		s.attrsBatchUpdateChan <- itm
	} else {
		s.attrsNoDelayChan <- itm
	}
}
