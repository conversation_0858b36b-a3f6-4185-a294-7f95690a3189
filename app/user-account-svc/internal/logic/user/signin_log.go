package user

import (
	"context"
	"github.com/gogf/gf/v2/util/gconv"
	"halalplus/app/user-account-svc/internal/model"
	"halalplus/app/user-account-svc/internal/model/do"
	"halalplus/app/user-account-svc/internal/model/entity"
	"time"
)

func (s *sUser) PushSignInLog(ctx context.Context, usr *entity.User, front *model.FrontInfo) {
	signLog := &model.SignInLogInput{
		UserId: usr.Id,
	}
	_ = gconv.Struct(front, signLog)
	s.dealSignInLog(ctx, signLog)
}

func (s *sUser) dealSignInLog(ctx context.Context, in *model.SignInLogInput) {
	val := do.UserSigninLog{
		UserId:          in.UserId,
		VipLevel:        in.VipLevel,
		SigninTime:      time.Now().UnixMilli(),
		Ip:              "",
		DeviceId:        in.DeviceId,
		DeviceOs:        in.DeviceOs,
		DeviceOsVersion: in.DeviceOsVersion,
		DeviceType:      in.DeviceType,
		AppType:         in.AppType,
		AppVersion:      in.AppVersion,
		Host:            "",
	}

	// 写入登录日志
	_ = val
}
