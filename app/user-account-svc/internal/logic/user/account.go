package user

import (
	"context"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"halalplus/app/user-account-svc/internal/consts"
	"halalplus/app/user-account-svc/internal/dao"
	"halalplus/app/user-account-svc/internal/model"
	"halalplus/app/user-account-svc/internal/model/do"
	"halalplus/app/user-account-svc/internal/model/entity"
	"halalplus/app/user-account-svc/internal/service"
	"halalplus/utility"
	"halalplus/utility/redsync"
	"time"
)

const keyRegisterIp = "user:register:ip" // 注册时的IP
// CreateByPhone 用户注册（手机号）
func (s *sUser) CreateByPhone(ctx context.Context, in *model.SignUpByPhoneReq) (userId uint64, err error) {
	var hashPassword string
	hashPassword, err = service.Utility().HashPassword(in.Password)
	if err != nil {
		g.Log().Error(ctx, err)
		return
	}

	g.Log().Debug(ctx, in)
	usr := s.NewUser(ctx, &in.FrontInfo, in.OriginalDomain)
	usr.Account = in.Account
	usr.AreaCode = in.AreaCode
	usr.PhoneNum = in.PhoneNum
	usr.Password = hashPassword

	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		userId, err = s.createUser(ctx, usr, in.OriginalDomain, in.InviteCode, tx)
		if err != nil {
			return err
		}
		return nil
	})

	return
}

func (s *sUser) NewUser(ctx context.Context, in *model.FrontInfo, originalDomain string) (ret do.User) {
	ret = do.User{
		Password:         nil,
		AreaCode:         nil,
		PhoneNum:         nil,
		IsBanned:         consts.UserStatusNormal,
		CreateTime:       time.Now().UnixMilli(),
		UpdateTime:       time.Now().UnixMilli(),
		SecurityPassword: "",
	}
	return
}

func (s *sUser) ComposeUser(ctx context.Context, user *entity.User) (userModel *model.User) {
	var appSetting entity.UserAppSettings
	appSetting, _ = service.UserAppSettings().SelfByUid(ctx, user.Id, false)
	//avatar := service.File().GetFrontendUrl(ctx, appSetting.Avatar)
	avatar := appSetting.Avatar
	userModel = &model.User{
		Id:                user.Id,
		Account:           user.Account,
		AreaCode:          user.AreaCode,
		PhoneNum:          user.PhoneNum,
		Address:           appSetting.Address,
		FirstName:         appSetting.FirstName,
		MiddleName:        appSetting.MiddleName,
		LastName:          appSetting.LastName,
		CreateTime:        user.CreateTime,
		CountryId:         appSetting.CountryId,
		Currency:          appSetting.Currency,
		Language:          appSetting.Language,
		IsRemindVoiceOpen: appSetting.IsRemindVoiceOpen,
		IsRemindVoiceType: appSetting.IsRemindVoiceType,
		IsRemindShockOpen: appSetting.IsRemindShockOpen,
		Nickname:          appSetting.Nickname,
		Avatar:            avatar,
		YearOfBirth:       appSetting.YearOfBirth,
		MonthOfBirth:      appSetting.MonthOfBirth,
		DayOfBirth:        appSetting.DayOfBirth,
		Gender:            appSetting.Gender,
		IsWithdrawAuth:    appSetting.IsWithdrawAuth,
		IsTest:            user.IsTest,
	}

	if g.IsEmpty(userModel.Language) {
		userModel.Language = consts.DefaultLanguage
	}

	return
}

// createUser 新建用户&新建后的各种初始化
func (s *sUser) createUser(ctx context.Context, usr do.User, originalDomain, inviteCode string, tx gdb.TX) (userId uint64, err error) {

	lockOut, err := redsync.GetLock(ctx, redsync.GetLockInput{Key: "sUser:createUser:" + gconv.String(usr.PhoneNum) + ":" + gconv.String(usr.Account)})
	if err != nil {
		return
	}
	defer lockOut.Unlock()
	// 检查账号或手机号是否己存在
	var isUnique bool
	isUnique, err = service.User().IsPhoneAccountExist(ctx, gconv.String(usr.PhoneNum), gconv.String(usr.Account))
	if !isUnique {
		return
	}
	// -------- 创建事务
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		unixMilli := time.Now().UnixMilli()
		// 创建账号
		var temId int64
		usr.CreateTime = unixMilli
		temId, err = dao.User.Ctx(ctx).TX(tx).Data(usr).InsertAndGetId()
		if err != nil {
			return err
		}

		userId = gconv.Uint64(temId)

		ui := entity.UserInfo{
			UserId:           userId,
			Avatar:           "",
			Nickname:         utility.GenerateRandomString(5),
			CreateTime:       unixMilli,
			IdentityCardImgs: "[]",
		}

		if !g.IsEmpty(usr.PhoneNum) {
			ui.BindPhoneTime = unixMilli
			ui.PhoneNum = gconv.String(usr.PhoneNum)
		}

		_, err = dao.UserInfo.Ctx(ctx).TX(tx).Insert(ui)
		if err != nil {
			return err
		}

		//service.Task().OnRegister(ctx, userId)
		return nil
	})

	return
}
