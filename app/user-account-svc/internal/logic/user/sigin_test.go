package user

import (
	"context"
	"halalplus/app/user-account-svc/internal/model"
	"halalplus/app/user-account-svc/internal/model/do"
	"halalplus/app/user-account-svc/internal/model/entity"
	"halalplus/utility/bloom"
	"reflect"
	"testing"
)

func Test_sUser_commonLoginSuccess(t *testing.T) {
	type fields struct {
		signInMsgChan        chan *model.SignInLogInput
		attrsBatchUpdateChan chan *model.AttrsToUpdate
		attrsNoDelayChan     chan *model.AttrsToUpdate
		quit                 chan struct{}
		ConfigCaptcha        string
		signInRecordChan     chan *do.UserSigninLog
		accountSet           *bloom.Filter
		transferSet          *bloom.Filter
		phoneSet             *bloom.Filter
	}
	type args struct {
		ctx       context.Context
		user      *entity.User
		frontInfo *model.FrontInfo
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *model.User
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &sUser{
				signInMsgChan:        tt.fields.signInMsg<PERSON>han,
				attrsBatchUpdateChan: tt.fields.attrsBatchUpdateChan,
				attrsNoDelayChan:     tt.fields.attrsNoDelayChan,
				quit:                 tt.fields.quit,
				ConfigCaptcha:        tt.fields.ConfigCaptcha,
				signInRecordChan:     tt.fields.signInRecordChan,
				accountSet:           tt.fields.accountSet,
				transferSet:          tt.fields.transferSet,
				phoneSet:             tt.fields.phoneSet,
			}
			if got := s.commonLoginSuccess(tt.args.ctx, tt.args.user, tt.args.frontInfo); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("commonLoginSuccess() = %v, want %v", got, tt.want)
			}
		})
	}
}
