package user

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"halalplus/app/user-account-svc/internal/dao"
	"halalplus/app/user-account-svc/internal/model"
	"halalplus/app/user-account-svc/internal/model/entity"
	"halalplus/app/user-account-svc/internal/service"
	"halalplus/utility"
)

var cl = dao.User.Columns()

func (s *sUser) SignInByPhoneNum(ctx context.Context, areaCode string, phoneNum string, frontInfo *model.FrontInfo) (userModel *model.User, token string, err error) {
	// TODO: LoginFrequencyControl(areaCode+phoneNum)
	user, err := dao.User.GetUserEntityByPhone(ctx, areaCode, phoneNum)
	if err != nil {
		// TODO： 创建用户
		signUpByPhoneReq := &model.SignUpByPhoneReq{
			FrontInfo: *frontInfo,
			Account:   areaCode + phoneNum,
			Password:  utility.GenerateRandomString(12),
			PhoneInfo: model.PhoneInfo{
				PhoneNum: phoneNum,
				AreaCode: areaCode,
			},
			AgentCode:      "",
			InviteCode:     "",
			Lang:           "",
			OptCode:        "",
			OriginalDomain: "",
			PixelId:        "",
			RegisterUrl:    "",
			Fbclid:         "",
			FbTimeStamp:    0,
			Esu:            "",
		}
		uid, err2 := s.CreateByPhone(ctx, signUpByPhoneReq)
		if err2 != nil {
			g.Log().Error(ctx, err2)
			return
		}

		err = dao.User.Ctx(ctx).Master().Where(cl.Id, uid).Scan(&user)
		if err != nil {
			g.Log().Error(ctx, err)
			return
		}
	}
	if user == nil {
		g.Log().Error(ctx, "user nil")
		return
	}

	userModel, err = s.commonLoginSuccess(ctx, user, frontInfo)
	if err != nil {
		g.Log().Error(ctx, err)
		return
	}

	token, err = service.Session().GenJwtToken(ctx, &model.UserSession{
		UserId:    userModel.Id,
		SessionId: userModel.SessionId,
	})

	if err != nil {
		g.Log().Error(ctx, err)
		return
	}

	return
}

func (s *sUser) commonLoginSuccess(ctx context.Context, user *entity.User, frontInfo *model.FrontInfo) (userModel *model.User, err error) {
	// 创建会话
	userSession, err := service.Session().CreateUserSession(ctx, user, frontInfo)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	userModel = s.ComposeUser(ctx, user)
	userModel.SessionId = userSession.SessionId
	userModel.SecretKey = userSession.SecretKey

	s.PushSignInLog(ctx, user, frontInfo)
	return userModel, err
}
