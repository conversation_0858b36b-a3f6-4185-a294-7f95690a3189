package utility

import (
	"bytes"
	"context"
	"fmt"
	"github.com/gogf/gf/v2/crypto/gmd5"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/i18n/gi18n"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/golang-jwt/jwt/v5"
	"halalplus/app/user-account-svc/internal/consts"
	"halalplus/app/user-account-svc/internal/errno"
	"halalplus/app/user-account-svc/internal/model"
	"halalplus/app/user-account-svc/internal/service"
	"halalplus/utility"
	"halalplus/utility/iploc"
	"math"
	"net"
	"os/exec"
	"strings"
	"time"

	"github.com/gogf/gf/v2/util/grand"

	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/mojocn/base64Captcha"
	"golang.org/x/crypto/bcrypt"
)

type (
	sUtility struct {
		Gid *utility.Gid // 用于生成订单id
	}
)

func init() {
	service.RegisterUtility(New())
}

func New() service.IUtility {
	gid, err := utility.NewGid(g.Cfg().MustGet(ctx, "server.id").Int())
	if err != nil {
		panic(err)
	}
	return &sUtility{
		Gid: gid,
	}
}

// ---- CaptchaRedisStore
var store base64Captcha.Store = CaptchaRedisStore{}

type CaptchaRedisStore struct {
}

var ctx = context.Background()

func (r CaptchaRedisStore) Set(id, value string) (err error) {
	err = g.Redis().SetEX(ctx, "captcha_"+id, value, consts.CaptchaEx)
	return
}
func (r CaptchaRedisStore) Get(id string, clear bool) string {
	var value *gvar.Var
	var err error
	if clear {
		value, err = g.Redis().GetDel(ctx, "captcha_"+id)
	} else {
		value, err = g.Redis().Get(ctx, "captcha_"+id)
	}

	if err != nil {
		return ""
	}
	return value.String()
}
func (r CaptchaRedisStore) Verify(id, answer string, clear bool) bool {
	value := CaptchaRedisStore{}.Get(id, clear)
	return value == answer
}

// ----OptCode
func (s *sUtility) GenerateCaptcha() (id, b64s string, err error) {
	driver := base64Captcha.DefaultDriverDigit
	c := base64Captcha.NewCaptcha(driver, store)
	id, b64s, _, err = c.Generate()
	return
}
func (s *sUtility) VerifyCaptcha(id, answer string, clear bool) (match bool) {
	match = store.Verify(id, answer, clear)
	return
}
func (s *sUtility) VerifySmsCaptcha(areaCode string, phoneNum string, smsCaptcha string, clear bool) (match bool) {
	var value *gvar.Var
	var err error
	if clear {
		value, err = g.Redis().GetDel(ctx, "sms_"+areaCode+"_"+phoneNum)
	} else {
		value, err = g.Redis().Get(ctx, "sms_"+areaCode+"_"+phoneNum)
	}

	if err != nil {
		return false
	}
	return value.String() == smsCaptcha
}

// ---- JWT
func (s *sUtility) GenerateJWT(id uint64, ty string, secretKey string) (token string, err error) {
	claims := model.JWTD{
		Ty: ty,
		RegisteredClaims: jwt.RegisteredClaims{
			Subject:   gconv.String(id),
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(consts.JWTEx)), // 过期时间30天
			// ExpiresAt: jwt.NewNumericDate(time.Now().Add(30 * time.Second)), //过期时间30秒
			//IssuedAt:  jwt.NewNumericDate(time.Now()), // 签发时间
			//NotBefore: jwt.NewNumericDate(time.Now()), // 生效时间
		},
	}
	// 使用HS256签名算法
	t := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	token, err = t.SignedString([]byte(secretKey))
	return
}
func (s *sUtility) ParseJWT(token, secretKey string) (*model.JWTD, error) {
	t, err := jwt.ParseWithClaims(token, &model.JWTD{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(secretKey), nil
	})
	if err != nil {
		return nil, err
	}
	if claims, ok := t.Claims.(*model.JWTD); ok && t.Valid {
		return claims, nil
	} else {
		return nil, err
	}
}

func (s *sUtility) GenerateResetPasswordJWT(claims model.JWTResetPwd, secretKey string) (token string, err error) {
	claims.RegisteredClaims = jwt.RegisteredClaims{
		ExpiresAt: jwt.NewNumericDate(time.Now().Add(consts.JWTResetPasswordEx)),
		IssuedAt:  jwt.NewNumericDate(time.Now()), // 签发时间
		NotBefore: jwt.NewNumericDate(time.Now()), // 生效时间
	}
	// 使用HS256签名算法
	t := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	token, err = t.SignedString([]byte(secretKey))
	return
}

func (s *sUtility) ParseResetPasswordJWT(token, secretKey string) (*model.JWTResetPwd, error) {
	t, err := jwt.ParseWithClaims(token, &model.JWTResetPwd{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(secretKey), nil
	})
	if err != nil {
		return nil, err
	}
	if claims, ok := t.Claims.(*model.JWTResetPwd); ok && t.Valid {
		return claims, nil
	} else {
		return nil, err
	}
}

// ---- Password
func (s *sUtility) HashPassword(password string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	// fmt.Printf("password %s", string(bytes))
	return string(bytes), err
}
func (s *sUtility) CheckPasswordHash(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}

func (s *sUtility) GetClientIp(r *ghttp.Request) string {
	if r == nil {
		// in test block
		return "127.0.0.1"
	}
	// myProxys := []string{}  //单个ip；ip段 x.x.x.x-x.x.x.x；子网掩码 x.x.x.x/x
	// cdnProxys := []string{} //单个ip；ip段 x.x.x.x-x.x.x.x；子网掩码 x.x.x.x/x

	remoteIp := r.RemoteAddr
	if ip := r.Header.Get("X-Real-IP"); ip != "" {
		remoteIp = strings.TrimSpace(ip)
	} else if ip = r.Header.Get("X-Forwarded-For"); ip != "" {
		// splitIps := strings.Split(ips, ",")
		ip = strings.TrimSpace(strings.Split(ip, ",")[0])
		remoteIp = ip
	} else {
		remoteIp, _, _ = net.SplitHostPort(strings.TrimSpace(remoteIp))
	}
	// 本地ip
	if remoteIp == "::1" {
		remoteIp = "127.0.0.1"
	}
	return remoteIp
}

// 检查一个ip是否在一批ip里或ip段里
// 单个ip；ip段 x.x.x.x-x.x.x.x；子网掩码 x.x.x.x/x
func (s *sUtility) isInIps(ips []string, ip string) bool {
	var ipss []string
	nIp := net.ParseIP(ip)
	for _, v := range ips {
		if strings.Contains(v, "-") {
			ipss = strings.Split(v, "-")
			start := net.ParseIP(ipss[0])
			end := net.ParseIP(ipss[1])
			if bytes.Compare(nIp, start) >= 0 && bytes.Compare(nIp, end) <= 0 {
				return true
			}
		} else if strings.Contains(v, "/") {
			_, ipNet, _ := net.ParseCIDR(v)
			if ipNet.Contains(nIp) {
				return true
			}
		} else {
			if v == ip {
				return true
			}
		}
	}
	return false
}

// 查找字符串是否在切片及在切片中的位置
func (s *sUtility) StringSliceFind(slice []string, val string) (int, bool) {
	for i, item := range slice {
		if item == val {
			return i, true
		}
	}
	return -1, false
}

// 查找int是否在切片及切片中的位置
func (s *sUtility) IntSliceFind(slice []int, val int) (int, bool) {
	for i, item := range slice {
		if item == val {
			return i, true
		}
	}
	return -1, false
}

// 检测服务器自身网络是否异常
func (s *sUtility) NetWorkStatus() bool {
	cmd := exec.Command("ping", "baidu.com", "-c", "1", "-W", "5")
	// fmt.Println("NetWorkStatus Start:", time.Now().UnixMilli())
	err := cmd.Run()
	// fmt.Println("NetWorkStatus End :", time.Now().UnixMilli())
	if err != nil {
		// fmt.Println(err.Error())
		return false
	} else {
		// fmt.Println("Net Status , OK")
	}
	return true
}

// ctx中获取self
func (s *sUtility) GetSelf(ctx context.Context) (usr *model.User, err error) {
	ctxVal := ctx.Value("self")
	usr, ok := ctxVal.(*model.User)
	if !ok {
		return nil, gerror.New(g.I18n().T(ctx, `ctx.getUser.error`))
	}
	return
}

// ctx中获取self
func (s *sUtility) IsVisitor(ctx context.Context) bool {
	ctxVal := ctx.Value("self")
	_, ok := ctxVal.(*model.User)
	return !ok
}

// GetSelfAccount 当前账号
func (s *sUtility) GetSelfAccount(ctx context.Context) (account string) {
	selfCtx, err := s.GetSelf(ctx)
	if err != nil {
		g.Log().Line().Error(ctx, err)
	}
	account = selfCtx.Account
	return
}

// GetCurrentLanguageCode 当前用户的语言编码
func (s *sUtility) GetCurrentLanguageCode(ctx context.Context) (language string) {
	language = gi18n.LanguageFromCtx(ctx)
	if len(language) < 1 {
		language = consts.DefaultLanguage
	}
	return
}

// GetCurrentAppType 当前用户的设置类型
func (s *sUtility) GetCurrentAppType(ctx context.Context) (appType int) {
	v := ctx.Value(consts.ContextAppType)
	if v != nil {
		appType = v.(int)
	}
	if appType < 1 {
		appType = consts.H5
	}
	return
}

// GetAppTheme 获取当前UI主题
func (s *sUtility) GetAppTheme(ctx context.Context) (appTheme string) {
	appTheme = g.RequestFromCtx(ctx).Header.Get("app-theme")
	if appTheme == "" {
		appTheme = consts.AppThemeShallow
	}
	return
}

// 比较两个浮点数是否相等
func (s *sUtility) IsEqualFloat64(a float64, b float64) (isEqual bool) {
	dif := math.Abs(a - b)
	if dif < consts.Precision {
		isEqual = true
	}
	return
}

// GenerateOrderId 生成订单
func (s *sUtility) GenerateOrderId(ctx context.Context, userId uint) (orderId *gvar.Var) {
	return gvar.New(s.Gid.Generate(userId))
}

func (s *sUtility) GetIPRegion(ctx context.Context, ip string) (region string) {
	return iploc.IPRegion(ip)
}

func (s *sUtility) GetIPCity(ctx context.Context, ip string) (city string) {
	_, city = iploc.IPLoc(ip)
	return
}

// 获取host
func (s *sUtility) GetHost(ctx context.Context) (host string) {
	host = g.RequestFromCtx(ctx).Header.Get("X-Host")
	if host == "" {
		host = g.RequestFromCtx(ctx).Host
	}
	return
}

// GetUserVirtuallyEmail 获取当前登录用户的虚拟邮箱 // 不放这
func (s *sUtility) GetUserVirtuallyEmail(ctx context.Context) (email string, err error) {
	var user *model.User
	user, err = s.GetSelf(ctx)
	if err != nil {
		return
	}
	email = fmt.Sprintf("%d@%s", user.Id, s.GetHost(ctx)) // todo 后期可以改为定长数字或着使用userid进行hash
	return
}

// 是否成年人 满18周岁
func IsAdult(year, month, day int) bool {
	ago18years := time.Now().AddDate(-18, 0, 0)
	userAge := time.Date(year, time.Month(month), day, 0, 0, 0, 0, time.UTC)
	if userAge.Unix() > ago18years.Unix() {
		return false
	}
	return true
}

func GenRandomAccountName() string {
	// 生成随机8位账号名，首位必须是字符
	head := grand.Letters(1)
	// 后7位字母加数字
	body := grand.S(7, false)
	return head + body
}

// 组装url
func (s *sUtility) BuildUpUrl(prefixUrl, params string) string {
	var buf bytes.Buffer
	buf.WriteString(prefixUrl)
	buf.WriteString("?")
	buf.WriteString(params)
	return buf.String()
}

func (s *sUtility) GenerateAccountAuthJWT(value string) (token string, err error) {
	claims := model.JWTAccountAuth{
		Value: value,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(consts.JWTAccountAuthEx)),
			IssuedAt:  jwt.NewNumericDate(time.Now()), // 签发时间
			NotBefore: jwt.NewNumericDate(time.Now()), // 生效时间
		},
	}
	if !strings.Contains(value, "@") {
		claims.IsPhone = true
	}

	// 使用HS256签名算法
	t := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	token, err = t.SignedString([]byte(consts.JWTSecretKey))

	return
}

func (s *sUtility) ParseAccountAuthJWT(token string) (*model.JWTAccountAuth, error) {
	t, err := jwt.ParseWithClaims(token, &model.JWTAccountAuth{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(consts.JWTSecretKey), nil
	})
	if err != nil {
		return nil, err
	}
	if claims, ok := t.Claims.(*model.JWTAccountAuth); ok && t.Valid {
		return claims, nil
	} else {
		return nil, err
	}
}

func (s *sUtility) GenerateJWTByRegisteredPhone(account string, authWay int) (token string, err error) {
	claims := model.JWTUserAuth{
		AuthAccount: account,
		AuthWay:     authWay,
		IsUseOnce:   false,
		// CreatTime:   time.Now().UnixMilli(),
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(consts.JWTUserAuthEx)),
			IssuedAt:  jwt.NewNumericDate(time.Now()), // 签发时间
			NotBefore: jwt.NewNumericDate(time.Now()), // 生效时间
		},
	}
	// if authWay {
	//	claims.IsUseOnce = true
	// }

	t := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	token, err = t.SignedString([]byte(consts.JWTUserAuthSecretKey))

	key := fmt.Sprintf("%s:%d", account, authWay)
	_ = g.Redis().SetEX(ctx, consts.KeyUserAuth(key), token, 10*consts.Minute)
	return
}

func (s *sUtility) VerityRegisteredPhoneJWT(ctx context.Context, token string, authWay int) (err error) {
	// 1 解析token
	var info *model.JWTUserAuth
	info, err = s.parseRegisteredPhoneJWT(token)
	if err != nil {
		return
	}

	if authWay != info.AuthWay {
		return errno.T(ctx, errno.CodeVerifyAccessTokenError)
	}

	user, err := service.Utility().GetSelf(ctx)
	if err != nil {
		return
	}
	if user.Account != info.AuthAccount {
		return gerror.NewCode(gcode.New(1000, "", nil), g.I18n().T(ctx, `account.phone.different`))
	}

	var value *gvar.Var
	key := fmt.Sprintf("%s:%d", info.AuthAccount, authWay)
	value, err = g.Redis().Get(ctx, consts.KeyUserAuth(key))
	if err != nil {
		return
	}

	if info.IsUseOnce {
		_, _ = g.Redis().Del(ctx, consts.KeyUserAuth(key))
	}

	if value.String() == token {
		return nil
	}
	return errno.T(ctx, errno.CodeVerifyAccessTokenError)
}

func (s *sUtility) parseRegisteredPhoneJWT(token string) (*model.JWTUserAuth, error) {
	t, err := jwt.ParseWithClaims(token, &model.JWTUserAuth{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(consts.JWTUserAuthSecretKey), nil
	})
	if err != nil {
		return nil, err
	}
	if claims, ok := t.Claims.(*model.JWTUserAuth); ok && t.Valid {
		return claims, nil
	} else {
		return nil, err
	}
}

// Md5Encode 使用MD5算法对传入的字符串进行加密，并返回加密后的字符串
func (s *sUtility) Md5Encode(str string) string {
	encryptString, _ := gmd5.EncryptString(str)
	return encryptString
}

// 检查一个ip是否在一批ip里或ip段里
// 单个ip；ip段 x.x.x.x-x.x.x.x；子网掩码 x.x.x.x/x
func (s *sUtility) IsInIps(ips []string, ip string) bool {
	var ipss []string
	nIp := net.ParseIP(ip)
	for _, v := range ips {
		if strings.Contains(v, "-") {
			ipss = strings.Split(v, "-")
			start := net.ParseIP(ipss[0])
			end := net.ParseIP(ipss[1])
			if bytes.Compare(nIp, start) >= 0 && bytes.Compare(nIp, end) <= 0 {
				return true
			}
		} else if strings.Contains(v, "/") {
			_, ipNet, _ := net.ParseCIDR(v)
			if ipNet.Contains(nIp) {
				return true
			}
		} else {
			if v == ip {
				return true
			}
		}
	}
	return false
}

// IsFieldValueExist 检查一个表中是否有指定字段的值；前置条件：表名和字段名必须正确
func (s *sUtility) IsFieldValueExist(ctx context.Context, in model.IsFieldValueExistInput) (bool, error) {
	m := g.Model(in.TableName).Ctx(ctx)
	n := 1
	if in.Value.IsSlice() {
		vs := in.Value.Slice()
		m = m.WhereIn(in.FieldName, vs)
		n = len(vs)
	} else {
		m = m.Where(in.FieldName, in.Value)
	}
	if len(in.DeleteTime) > 0 {
		m = m.Where(in.DeleteTime, 0)
	}
	count, err := m.Limit(1).Count()
	if err != nil {
		return false, err
	}
	return count >= n, nil
}

func (s *sUtility) GetDomain(ctx context.Context) (ret *model.RequestDomain, err error) {
	ctxVar := ctx.Value(consts.ContextKeyDomain)
	var ok bool
	ret, ok = ctxVar.(*model.RequestDomain)
	if !ok {
		return nil, gerror.New(g.I18n().T(ctx, "ctx.GetDomain.error"))
	}
	return
}
