package utility

import (
	"fmt"
	"idnuapi/internal/consts"
	"testing"
)

func TestIsAdult(t *testing.T) {
	type args struct {
		year  int
		month int
		day   int
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		// TODO: Add test cases.
		{
			name: "Test1: 大于18岁",
			args: args{
				year:  1980,
				month: 11,
				day:   6,
			},
			want: true,
		},
		{
			name: "Test2: 小于18岁",
			args: args{
				year:  2020,
				month: 1,
				day:   6,
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsAdult(tt.args.year, tt.args.month, tt.args.day); got != tt.want {
				t.Errorf("IsAdult() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_sUtility_HashPassword(t *testing.T) {
	type args struct {
		password string
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		{
			name: "Test1",
			args: args{
				// password: "J9#@bUg56p!Q895RX",
				password: "698521",
			},
			want:    "1",
			wantErr: false,
		},
		// {
		//	name: "Test2",
		//	args: args{
		//		password: "pass123",
		//	},
		//	want:    "1",
		//	wantErr: false,
		// },
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &sUtility{}
			got, err := s.HashPassword(tt.args.password)
			if (err != nil) != tt.wantErr {
				t.Errorf("HashPassword() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("HashPassword() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_sUtility_CheckPasswordHash(t *testing.T) {
	type args struct {
		password string
		hash     string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "Test1",
			args: args{
				password: "pass123",
				hash:     "$2a$10$y0FK4dK3M3RZjlI4Kv7zuejT9lhU.CbY0FHdftEOxVnRSey1tLL0m",
			},
			want: true,
		},
		{
			name: "Test2",
			args: args{
				password: "pass123",
				hash:     "$2a$10$3OVJwe2VB2/74W9s1c1FGO9eZypa5bZpmh2gk85fa/m7X.XYZpc9K",
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &sUtility{}
			if got := s.CheckPasswordHash(tt.args.password, tt.args.hash); got != tt.want {
				t.Errorf("CheckPasswordHash() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGenRandomAccountName(t *testing.T) {
	for i := 0; i < 10000; i++ {
		accountName := GenRandomAccountName()
		fmt.Printf(" %s ", GenRandomAccountName())
		if len(accountName) != 8 && (accountName[0] >= 'A' && accountName[0] <= 'z') {
			t.Errorf("GenRandomAccountName() = %v ", accountName)
			break
		}
	}

}

func Benchmark_sUtility_ParseJWT(b *testing.B) {
	s := sUtility{}
	token, _ := s.GenerateJWT(1, "user", "222", consts.JWTSecretKey)
	b.SetParallelism(10)
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			s.ParseJWT(token, consts.JWTSecretKey)
		}
	})
}

func TestParseToken(t *testing.T) {
	token := "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************.RbY9S-b3WzXjO36kUd6weluocFSrbe4Ut0gMOY3N9I4"
	s := sUtility{}
	d, err := s.ParseJWT(token, consts.JWTSecretKey)
	t.Logf("%+v, %+v", d, err)
	t.Log(d.Id)
}
