package user

import (
	"context"
	"github.com/gogf/gf/contrib/rpc/grpcx/v2"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/test/gtest"
	"halalplus/api/common"
	userv1 "halalplus/app/user-account-svc/api/user/v1"
	v1 "halalplus/app/user-account-svc/api/user/v1"
	"halalplus/utility"
	"reflect"
	"testing"
	"time"
)

var userServiceClient userv1.UserServiceClient
var ctx context.Context
var frontInfo *common.FrontInfo

func TestMain(m *testing.M) {
	ctx = gctx.GetInitCtx()
	conn := grpcx.Client.MustNewGrpcClientConn("192.168.64.1:9200")
	userServiceClient = userv1.NewUserServiceClient(conn)
	frontInfo = &common.FrontInfo{
		DeviceId:        "r7bJjKjHnToH0XghqF64h8erSdcErMQW",
		DeviceOs:        "mac",
		AppVersion:      "v0.0.0",
		AppType:         5,
		DeviceOsVersion: "Version 15.5",
	}
	m.Run()
}

func Test_SignInByPhone(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		res, err := userServiceClient.SignInByPhone(ctx, &userv1.SignInByPhoneReq{
			OptCode: "123456",
			PhoneInfo: &common.PhoneInfo{
				AreaCode: "+62",
				PhoneNum: "**********",
			},
			FrontInfo: frontInfo,
		})
		if err != nil {
			t.Error(ctx, err)
		}
		t.Log(res)
		t.Assert(res.Code, "200")

	})
}

func TestController_RefreshToken(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		reqCtx := grpcx.Ctx.NewOutgoing(ctx, g.Map{
			"X-Session-Id":     "cCLbTwnxlfDRzQzXuavNG6GR",
			"X-Request-Id":     utility.GenerateRandomString(36),
			"X-Timestamp":      time.Now().Unix(),
			"X-Signature":      "xxxx",
			"X-Sign-Verersion": "v1",
		})
		req := &userv1.RefreshTokenReq{
			FrontInfo: frontInfo,
		}
		res, err := userServiceClient.RefreshToken(reqCtx, req)
		if err != nil {
			t.Error(ctx, err)
		}
		t.Assert(res.Code, "200")
		t.Log(res)

		reqCtx = grpcx.Ctx.NewOutgoing(ctx, g.Map{
			"Authorization":    "Bearer " + res.Data.Token,
			"X-Session-Id":     "BrxxMJGRCB3xE5r55QEhK7vs",
			"X-Request-Id":     utility.GenerateRandomString(36),
			"X-Timestamp":      time.Now().Unix(),
			"X-Signature":      "xxxx",
			"X-Sign-Verersion": "v1",
		})
		req2 := &userv1.UserInfoReq{}
		res2, err := userServiceClient.UserInfo(reqCtx, req2)
		if err != nil {
			t.Error(ctx, err)
		}
		t.Log(res2)
	})
}

func TestController_UserInfo(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		reqCtx := grpcx.Ctx.NewOutgoing(ctx, g.Map{
			"Authorization":    "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0eSI6InVzZXIiLCJzdWIiOiI1IiwiZXhwIjoxNzUzMjQzNzc0fQ.XVtLrE6CtyrZ0E-jxm7Ks5p4kPUog6H1W1O6de8HOwE",
			"X-Session-Id":     "BPKjBq7swSsj1RHxdLUnADdg",
			"X-Request-Id":     utility.GenerateRandomString(36),
			"X-Timestamp":      time.Now().Unix(),
			"X-Signature":      "xxxx",
			"X-Sign-Verersion": "v1",
		})
		req := &userv1.UserInfoReq{}
		res, err := userServiceClient.UserInfo(reqCtx, req)
		if err != nil {
			t.Error(ctx, err)
		}
		t.Log(res)
		t.Assert(res.Code, "200")
	})
}

func TestController_UpdateUserInfo(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		reqCtx := grpcx.Ctx.NewOutgoing(ctx, g.Map{
			"Authorization":    "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0eSI6InVzZXIiLCJzdWIiOiI1IiwiZXhwIjoxNzUzMjQzNzc0fQ.XVtLrE6CtyrZ0E-jxm7Ks5p4kPUog6H1W1O6de8HOwE",
			"X-Session-Id":     "BPKjBq7swSsj1RHxdLUnADdg",
			"X-Request-Id":     utility.GenerateRandomString(36),
			"X-Timestamp":      time.Now().Unix(),
			"X-Signature":      "xxxx",
			"X-Sign-Verersion": "v1",
		})
		req := &userv1.UpdateUserInfoReq{
			Nickname: "adrian2",
			Avatar:   "default",
			Gender:   1,
		}
		res, err := userServiceClient.UpdateUserInfo(reqCtx, req)
		if err != nil {
			t.Error(ctx, err)
		}
		t.Log(res)
		t.Assert(res.Code, "200")
	})
}

func TestController_SendVerifyCode(t *testing.T) {
	type fields struct {
		UnimplementedUserServiceServer userv1.UnimplementedUserServiceServer
	}
	type args struct {
		ctx context.Context
		req *v1.SendVerifyCodeReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantRes *v1.SendVerifyCodeRes
		wantErr bool
	}{
		{
			name: "发送sms短信",
			args: args{req: &v1.SendVerifyCodeReq{
				PhoneInfo: &common.PhoneInfo{
					AreaCode: "+62",
					PhoneNum: "**********",
				},
				VerifyCodeChannel: v1.VerifyCodeChannel_SMS,
				VerifyCodeScene:   v1.VerifyCodeScene_SIGN_UP,
			}},
			wantRes: &v1.SendVerifyCodeRes{
				Code: 200,
				Msg:  "success",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			co := &Controller{
				UnimplementedUserServiceServer: tt.fields.UnimplementedUserServiceServer,
			}
			gotRes, err := co.SendVerifyCode(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("SendVerifyCode() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotRes, tt.wantRes) {
				t.Errorf("SendVerifyCode() gotRes = %v, want %v", gotRes, tt.wantRes)
			}
		})
	}
}
