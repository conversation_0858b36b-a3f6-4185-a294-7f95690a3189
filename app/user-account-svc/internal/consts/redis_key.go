package consts

import (
	"fmt"
	"strconv"
	"time"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/util/gconv"
)

//idn:db:SelectCache:

func KeyUserSelf(id uint) string {
	return "user:self:" + gconv.String(id)
}

func DelUserCache(id uint) gdb.CacheOption {
	var DelUser = gdb.CacheOption{
		Duration: DeleteCacheEx,
		Name:     KeyUserSelf(id),
	}
	return DelUser
}

func KeyUserAppSetting(id uint64) string {
	return "user:app:setting:" + gconv.String(id)
}

func DelUserAppSettingCache(id uint64) gdb.CacheOption {
	var DelUser = gdb.CacheOption{
		Duration: DeleteCacheEx,
		Name:     KeyUserAppSetting(id),
	}
	return DelUser
}

func KeyUserToken(id uint64, sessionId string) string {
	return "user:token:" + gconv.String(id) + ":" + sessionId
}

func KeyUserSignInLimit(id uint) string {
	return "user:signin:limit:" + gconv.String(id)
}

func KeyPhoneOpt(areaCode, phone string) string {
	// 手机验证码前缀 + 国家号 + 手机号
	return "user:opt:" + areaCode + ":" + phone
}

func KeyEmailOpt(email string) string {
	return "user:opt:email:" + email
}

func KeyResetPwd(phoneOrEmail string) string {
	return "user:reset:pwd:" + phoneOrEmail
}

func KeyAccountAuth(phoneOrEmail string) string {
	return "auth:account:" + phoneOrEmail
}

func KeyUserAuth(phone string) string {
	return "auth:user:" + phone
}

func KeySceneCache(userId uint, key string) string {
	return fmt.Sprintf("scene:%d:%s", userId, key)
}

func KeyUserVisit(uid uint) string {
	return fmt.Sprintf("%s:%d", KeyUserVisitPrefix, uid)
}

func KeyNavClass(navType int) string {
	return "navClasses:" + gconv.String(navType)
}

func KeyUserNav(userId uint) string {
	return "userNavClass:" + gconv.String(userId)
}

const KeyUserVisitPrefix string = "visit"

const KeyStatOnlineUserCnt string = "stat:online:cnt"

func KeyLoginLimit(key string) string {
	return "rate:login:limit" + key
}

func KeyRateOptIpDaily(ip string) string {
	return "rate:opt:ip:day:" + ip
}

func KeyRateNoAuthRequest(ip string) string {
	return "rate:none:auth:request:" + ip
}

func KeyRateOptInternal(key string) string {
	return "rate:opt:internal:" + key
}

func KeyRateOptUser(key string) string {
	return "rate:opt:user:" + key
}
func KeyRateSignUp(key string) string {
	return "rate:account:signup:" + key
}

func KeyAddPixelEvent(key string) string {
	return "rate:add:pixel:" + key
}

func KeyReplaceTransferCode(key string) string {
	return "rate:replace:code:" + key
}

func KeyRateLogin(key string) string {
	return "rate:account:login:" + key
}

func KeyRateUser(key string) string {
	return "rate:user:" + key
}

func KeyAgentAccount(id uint) string {
	return "agentAccount:" + gconv.String(id)
}

func KeyAgentDomainPrivate(id uint) string {
	return "agentDomainPrivate:" + gconv.String(id)
}

func KeyAgentDomainPublic() string {
	return "agentDomainPrivate:valid:status"
}

func KeyAgentAgentDomainTag() string {
	return "agentDomainTag"
}

func KeyAgentPromotionLink(id uint) string {
	return "agentPromotionLink:" + gconv.String(id)
}

func KeyAgentPromotionLinkEdit(id uint) string {
	return "agentPromotionLinkEdit:" + gconv.String(id)
}

func KeyUserFeedbackEdit(id uint) string {
	return "userFeedback:" + gconv.String(id)
}

func KeyUserTopNavEdit(id uint) string {
	return "userTopNavEdit:" + gconv.String(id)
}

func KeyUserAppSettings(id uint) string {
	return "user:AppSettings:" + gconv.String(id)
}

func KeyAgentPromotionMaterial() string {
	return "agentPromotionMaterial:" + "all"
}

func KeyAgentPromotionMaterialThemeSize() string {
	return "agentPromotionMaterial:ThemeSize"
}

func KeyAppSharing() string {
	return "appSharing"
}
func KeyUserBackCardList[T string | uint](key T) string {
	return "user:BankCardList:" + gconv.String(key)
}
func KeyUserCoinAddressList[T string | uint](key T) string {
	return "user:CoinAddressList:" + gconv.String(key)
}

// game业务开始
func KeySupplier(id uint) string { // 废弃
	return "supplier:byId:" + gconv.String(id)
}
func KeyMerchant(id uint) string { // 废弃
	return "merchant:" + gconv.String(id)
}
func KeyVenue(id uint) string { // 废弃
	return "venue:byId:" + gconv.String(id)
}
func KeyVenueByName(name string) string { // 废弃
	return "venue:byName:" + name
}
func KeyVenueUser(venueId uint, userId uint) string {
	return "venueUser:" + gconv.String(venueId) + ":" + gconv.String(userId)
}
func KeyVenueUser2(venueId uint, uAccount string) string {
	return "venueUser:" + gconv.String(venueId) + ":" + uAccount
}
func KeyGame(id uint) string { // 废弃
	return "game:byId:" + gconv.String(id)
}
func KeyGameByName(name string) string { // 废弃
	return "game:byName:" + name
}
func KeyGameCategoryAll() string {
	return "gameCategoryAll"
}
func KeySportMenuAll() string {
	return "sportMenuAll"
}
func KeyGameSubAll() string {
	return "gameSubAll"
}
func KeyGameSub(id uint) string {
	return "gameSub:byId:" + gconv.String(id)
}
func KeyGameSubByOName(gameId uint, oName string) string {
	return "gameSubByOName:" + gconv.String(gameId) + ":" + oName
}
func KeyGameAll() string {
	return "gameAll"
}
func KeySupplierAll() string {
	return "supplierAll"
}
func KeyMerchantAll() string {
	return "merchantAll"
}
func KeyVenueAll() string {
	return "venueAll"
}
func KeyGameBrandAll() string {
	return "gameBrandAll"
}
func KeyGameSubHistory(userId uint) string {
	return "GameSubHistory:" + gconv.String(userId)
}

// game业务结束

// order业务开始
func KeyWinRankingList() string {
	return "winRankingList"
}

// order业务结束

// apiBase业务开始
func KeyApiBaseAll() string {
	return "apiBaseAll"
}

// apiBase业务结束

// sys业务开始
func KeySysCountryAll() string {
	return "sysCountryAll"
}

// sys业务结束

func KeyUserLogin(uid uint, now time.Time) string {
	return "user:login:" + strconv.FormatInt(int64(uid), 10) + ":" + now.Format("20060102")
}
