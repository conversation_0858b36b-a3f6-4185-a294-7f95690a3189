// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// UserSession is the golang structure for table user_session.
type UserSession struct {
	Id             uint64 `json:"id"             orm:"id"               description:"会话记录ID"`                                   // 会话记录ID
	SessionId      string `json:"sessionId"      orm:"session_id"       description:"会话唯一ID（绑定 JWT 的 sid 字段）"`                  // 会话唯一ID（绑定 JWT 的 sid 字段）
	SecretKey      string `json:"secretKey"      orm:"secret_key"       description:"refresh token的密钥"`                         // refresh token的密钥
	UserId         uint64 `json:"userId"         orm:"user_id"          description:"会员ID"`                                     // 会员ID
	LoginTime      int64  `json:"loginTime"      orm:"login_time"       description:"登录时间（时间戳）"`                                // 登录时间（时间戳）
	ExpireTime     int64  `json:"expireTime"     orm:"expire_time"      description:"会话过期时间（时间戳）"`                              // 会话过期时间（时间戳）
	LogoutTime     int64  `json:"logoutTime"     orm:"logout_time"      description:"登出时间（0表示未登出）"`                             // 登出时间（0表示未登出）
	Ip             string `json:"ip"             orm:"ip"               description:"登录IP"`                                     // 登录IP
	DeviceId       string `json:"deviceId"       orm:"device_id"        description:"设备ID"`                                     // 设备ID
	DeviceOs       string `json:"deviceOs"       orm:"device_os"        description:"设备系统"`                                     // 设备系统
	DeviceType     string `json:"deviceType"     orm:"device_type"      description:"设备类型（mobile/desktop/pad等）"`                // 设备类型（mobile/desktop/pad等）
	AppType        string `json:"appType"        orm:"app_type"         description:"应用类型（android/ios/h5/web等）"`                // 应用类型（android/ios/h5/web等）
	AppVersion     string `json:"appVersion"     orm:"app_version"      description:"应用版本"`                                     // 应用版本
	PushToken      string `json:"pushToken"      orm:"push_token"       description:"推送token，对应 Android（如 FCM）和 iOS（如 APNs）系统"` // 推送token，对应 Android（如 FCM）和 iOS（如 APNs）系统
	IsOnline       int    `json:"isOnline"       orm:"is_online"        description:"是否在线（1=在线，0=离线）"`                          // 是否在线（1=在线，0=离线）
	IsKicked       int    `json:"isKicked"       orm:"is_kicked"        description:"是否被踢下线（1=是，0=否）"`                          // 是否被踢下线（1=是，0=否）
	LastActiveTime int64  `json:"lastActiveTime" orm:"last_active_time" description:"最近活跃时间（如访问接口时间）"`                          // 最近活跃时间（如访问接口时间）
}
