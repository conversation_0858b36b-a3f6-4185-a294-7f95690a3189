// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// UserAppSettings is the golang structure for table user_app_settings.
type UserAppSettings struct {
	Id                 uint64 `json:"id"                 orm:"id"                   description:""`                                              //
	Account            string `json:"account"            orm:"account"              description:"账号"`                                            // 账号
	CountryId          uint   `json:"countryId"          orm:"country_id"           description:"国家id"`                                          // 国家id
	Currency           string `json:"currency"           orm:"currency"             description:"币种"`                                            // 币种
	Language           string `json:"language"           orm:"language"             description:"语言 : zh-CN:中文, id:Indonesian, en:English"`      // 语言 : zh-CN:中文, id:Indonesian, en:English
	IsVibrationOpen    int    `json:"isVibrationOpen"    orm:"is_vibration_open"    description:"振动设置 开关状态：1开, 2关"`                              // 振动设置 开关状态：1开, 2关
	YearOfBirth        int    `json:"yearOfBirth"        orm:"year_of_birth"        description:"出生年"`                                           // 出生年
	MonthOfBirth       int    `json:"monthOfBirth"       orm:"month_of_birth"       description:"出生月"`                                           // 出生月
	DayOfBirth         int    `json:"dayOfBirth"         orm:"day_of_birth"         description:"出生日"`                                           // 出生日
	IsRemindVoiceOpen  int    `json:"isRemindVoiceOpen"  orm:"is_remind_voice_open" description:"推送提示-声音：1开, 2关"`                                // 推送提示-声音：1开, 2关
	IsRemindVoiceType  int    `json:"isRemindVoiceType"  orm:"is_remind_voice_type" description:"推送提示-声音类型：1欢呼声,  2哨声"`                          // 推送提示-声音类型：1欢呼声,  2哨声
	IsRemindShockOpen  int    `json:"isRemindShockOpen"  orm:"is_remind_shock_open" description:"推送提示-震动：1开, 2关"`                                // 推送提示-震动：1开, 2关
	IsWithdrawAuth     int    `json:"isWithdrawAuth"     orm:"is_withdraw_auth"     description:"提现验证：1开, 2关"`                                   // 提现验证：1开, 2关
	Avatar             string `json:"avatar"             orm:"avatar"               description:"头像url"`                                         // 头像url
	Gender             string `json:"gender"             orm:"gender"               description:"性别：0未知 1男 2女"`                                  // 性别：0未知 1男 2女
	Nickname           string `json:"nickname"           orm:"nickname"             description:"昵称"`                                            // 昵称
	NicknameModityTime int64  `json:"nicknameModityTime" orm:"nickname_modity_time" description:"昵称最近一次修改时间"`                                    // 昵称最近一次修改时间
	FirstName          string `json:"firstName"          orm:"first_name"           description:"第一个名字"`                                         // 第一个名字
	MiddleName         string `json:"middleName"         orm:"middle_name"          description:"中间名字"`                                          // 中间名字
	LastName           string `json:"lastName"           orm:"last_name"            description:"最后一个名字"`                                        // 最后一个名字
	Version            int64  `json:"version"            orm:"version"              description:"该记录的版本号"`                                       // 该记录的版本号
	IdentityCard       string `json:"identityCard"       orm:"identity_card"        description:"身份证号码"`                                         // 身份证号码
	IdentityCardImgs   string `json:"identityCardImgs"   orm:"identity_card_imgs"   description:"身份证图片"`                                         // 身份证图片
	Contact            string `json:"contact"            orm:"contact"              description:"社交联系方式 wechat qq等"`                             // 社交联系方式 wechat qq等
	Address            string `json:"address"            orm:"address"              description:"住址"`                                            // 住址
	CreateAccount      string `json:"createAccount"      orm:"create_account"       description:"创建者账号"`                                         // 创建者账号
	CreateType         int    `json:"createType"         orm:"create_type"          description:"创建者来源(注册来源)（1会员注册user，2代理添加agent，3管理员添加admin）"` // 创建者来源(注册来源)（1会员注册user，2代理添加agent，3管理员添加admin）
	UpdateAccount      string `json:"updateAccount"      orm:"update_account"       description:"更新者账号"`                                         // 更新者账号
	UpdateType         int    `json:"updateType"         orm:"update_type"          description:"更新者来源"`                                         // 更新者来源
	CreateTime         int64  `json:"createTime"         orm:"create_time"          description:"创建时间"`                                          // 创建时间
	UpdateTime         int64  `json:"updateTime"         orm:"update_time"          description:"更新时间"`                                          // 更新时间
}
