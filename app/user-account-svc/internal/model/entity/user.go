// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// User is the golang structure for table user.
type User struct {
	Id               uint64 `json:"id"               orm:"id"                description:""`                  //
	Account          string `json:"account"          orm:"account"           description:"账号"`                // 账号
	Password         string `json:"password"         orm:"password"          description:"密码"`                // 密码
	AreaCode         string `json:"areaCode"         orm:"area_code"         description:"手机国际区号，如：86"`       // 手机国际区号，如：86
	PhoneNum         string `json:"phoneNum"         orm:"phone_num"         description:"手机号"`               // 手机号
	IsBanned         int    `json:"isBanned"         orm:"is_banned"         description:"账号封号状态： 1 正常 2 封号"` // 账号封号状态： 1 正常 2 封号
	SecurityPassword string `json:"securityPassword" orm:"security_password" description:"安全密码，修改个人绑定信息时要验证"` // 安全密码，修改个人绑定信息时要验证
	IsTest           int    `json:"isTest"           orm:"is_test"           description:"测试账号：  1 是 ，其他值：否"` // 测试账号：  1 是 ，其他值：否
	CreateTime       int64  `json:"createTime"       orm:"create_time"       description:"创建时间（注册时间）"`        // 创建时间（注册时间）
	UpdateTime       int64  `json:"updateTime"       orm:"update_time"       description:"更新时间，0代表创建后未被修改过"`  // 更新时间，0代表创建后未被修改过
}
