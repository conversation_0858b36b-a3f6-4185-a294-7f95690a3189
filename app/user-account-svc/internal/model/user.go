package model

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"halalplus/app/user-account-svc/internal/consts"
	"halalplus/app/user-account-svc/internal/model/entity"
	"halalplus/utility"
	"halalplus/utility/slices"
	"strings"
)

type User struct {
	Id      uint64 `json:"id"                    dc:"用户id"`
	Lang    string `json:"lang"                  dc:"语言"`
	Account string `json:"account"               dc:"账号"`

	SessionId string `json:"sessionId" dc:"会话唯一ID（绑定 JWT 的 sid 字段）"` // 会话唯一ID（绑定 JWT 的 sid 字段）
	SecretKey string `json:"secretKey" dc:"refresh token的密钥"`        // refresh token的密钥
	LoginTime int64  `json:"loginTime"  dc:"登录时间（时间戳）"`              // 登录时间（时间戳）

	Level        int    `json:"level"                               dc:"代理层级 [1一级代理, 2二级代理 , 3三级代理]"`
	InviteCode   string `json:"inviteCode"                 dc:"代理邀请码"`
	TransferCode string `json:"transferCode"       dc:"转线码"`

	AreaCode          string `json:"areaCode"              dc:"手机国际区号，如：86"`
	AreaCodeImage     string `json:"areaCodeImage"         dc:"手机国际区号图片"`
	PhoneNum          string `json:"phoneNum"              dc:"手机号"`
	VipLevel          int    `json:"vipLevel"              dc:"vip等级"`
	Email             string `json:"email"                 dc:"邮箱"`
	Address           string `json:"address"               dc:"住址"`
	FirstName         string `json:"firstName"             dc:"第一个名字"`
	MiddleName        string `json:"middleName"            dc:"中间名字"`
	LastName          string `json:"lastName"              dc:"最后一个名字"`
	IsOnline          int    `json:"isOnline"       dc:"是否在线：1是  2 否"`
	CreateTime        int64  `json:"createTime"            dc:"注册时间"`
	CountryId         uint   `json:"countryId"             dc:"国家id"`
	Currency          string `json:"currency"              dc:"币种"`
	CurrencyIconHome  string `json:"currencyIconHome"      dc:"币种图片地址 首页黄色"`
	CurrencyIconOther string `json:"currencyIconOther"     dc:"币种图片地址 其他页面红色"`
	SignupDomain      string `json:"signupDomain"            dc:"注册域名(页面域名)"`
	SignupDomain2     string `json:"signupDomain2"            dc:"注册域名(页面域名)"`

	Language string `json:"language"            dc:"语言 zh-CN:中文, id:Indonesian, en:English, zh-TW:繁体中文, vi:越南语"`

	IsRemindVoiceOpen int `json:"isRemindVoiceOpen"   dc:"推送提示-声音：1开, 2关"`
	IsRemindVoiceType int `json:"isRemindVoiceType"   dc:"推送提示-声音类型：1欢呼声, 2哨声"`
	IsRemindShockOpen int `json:"isRemindShockOpen"   dc:"推送提示-震动：1开, 2关"`

	Nickname string `json:"nickname"              dc:"昵称"`
	Avatar   string `json:"avatar"                dc:"头像url"`

	YearOfBirth    int    `json:"yearOfBirth"           dc:"出生年"`
	MonthOfBirth   int    `json:"monthOfBirth"          dc:"出生月"`
	DayOfBirth     int    `json:"dayOfBirth"            dc:"出生日"`
	Gender         string `json:"gender"                dc:"性别：0未知 1男 2女"`
	HasPayPassword bool   `json:"hasPayPassword"        dc:"是否已设置支付密码"`

	PasswordModifyTime     int64  `json:"passwordModifyTime"          dc:"登录密码支付密码最近修改时间"`
	DaysPasswordLastChange uint   `json:"daysPasswordLastChange"      dc:"密码上次修改天数"`
	IsWithdrawAuth         int    `json:"isWithdrawAuth"              dc:"提现验证[1开, 2关]"`
	IsTest                 int    `json:"isTest"                      dc:"测试账号：  1 是 ，其他值：否"`
	LastSigninIp           string `json:"lastSigninIp"                dc:"最后登录ip"`
	PixelId                string `json:"pixelId"  dc:"像素ID"`
	Source                 int    `json:"source" dc:"注册来源"`
}

// UserInfo 返回给前端的用户信息
type UserInfo struct {
	Id         uint64 `json:"id"                    dc:"用户id"`
	Account    string `json:"account"               dc:"账号"`
	CreateTime int64  `json:"createTime"            dc:"注册时间"`

	AreaCode      string `json:"areaCode"              dc:"手机国际区号，如：86"`
	AreaCodeImage string `json:"areaCodeImage"         dc:"手机国际区号图片"`
	PhoneNum      string `json:"phoneNum"              dc:"手机号"`

	BindEmail    int8 `json:"bindEmail" dc:"是否绑定email"`
	BindPhone    int8 `json:"bindPhone" dc:"是否绑定手机"`
	BindRealName int8 `json:"bindRealName" dc:"是否实名认证"`

	Nickname string `json:"nickname"              dc:"昵称"`
	Avatar   string `json:"avatar"                dc:"头像url"`

	Gender string `json:"gender"                dc:"性别：0未知 1男 2女"`

	FirstName  string `json:"firstName"`
	MiddleName string `json:"middleName"`
	LastName   string `json:"lastName"`
}

func (info *UserInfo) FillUser(u *User) {
	info.Id = u.Id
	info.Gender = u.Gender
	info.Avatar = u.Avatar
	info.Nickname = u.Nickname
	if !g.IsEmpty(u.PhoneNum) {
		info.BindPhone = 1
		info.PhoneNum = u.PhoneNum
		info.AreaCode = u.AreaCode
	}
}

func (u User) GetDataType() int {
	if u.IsTest == consts.IsYes {
		return consts.DataTypeTest
	}
	return consts.DataTypeNormal
}

func (u User) GetAccountName() string {
	names := []string{
		u.FirstName,
		u.MiddleName,
		u.LastName,
	}
	names = slices.RemoveEmpty(names)
	set := " "
	if utility.ContainsChinese(u.FirstName) { // 中文不用空格拼接
		set = ""
	}
	if len(names) < 1 {
		return ""
	}
	return strings.Join(names, set)
}

type UserOption struct {
	Uid      uint
	IsFromDb bool
}

type PayUser struct {
	WithdrawDayNum      int   `json:"withdrawDayNum" dc:"每天可以提现次数(0则不限制)"`
	WithdrawDayAllowNum int   `json:"withdrawDayAllowNum" dc:"目前每天剩余提现次数(-1则不限制)"`
	BankCardMaxNum      int   `json:"bankCardMaxNum" dc:"可以拥有银行卡最大数量"`
	BankCardNum         int   `json:"bankCardNum" dc:"目前拥有银行卡数量"`
	CoinAddressMaxNum   int   `json:"coinAddressMaxNum" dc:"可以拥有虚拟币最大数量"`
	CoinAddressNum      int   `json:"coinAddressNum" dc:"目前拥有虚拟币数量"`
	FirstRechargeTime   int64 `json:"firstRechargeTime" dc:"首充时间"`
	WaitRechargeId      int64 `json:"waitRechargeId" dc:"待充值订单ID"`
}

type SignInLogInput struct {
	Ctx      context.Context
	UserId   uint64 `json:"userId"` // 用户id
	VipLevel int    `json:"vipLevel"  dc:"vip等级"`

	DeviceId        string `json:"deviceId" dc:"设备号（设备指纹）"`
	DeviceOs        string `json:"deviceOs" dc:"设备操作系统（android,ios,windows,mac...）"`
	DeviceOsVersion string `json:"deviceOsVersion" dc:"设备操作系统版本号"`
	DeviceType      int    `json:"deviceType" dc:"设备类型（1:mobile手机,2:desktop台式,3:pad平板，4:其他）"`
	AppType         int    `json:"appType" dc:"应用类型（1:android 2:ios，3:h5，4:web，5:其他）"`
	AppVersion      string `json:"appVersion" dc:"应用版本号"`
	SigninCount     int    `json:"signinCount"   dc:"登录次数"`
}

type AttrsToUpdate struct {
	Ctx    context.Context
	UserId uint64 `json:"userId"` // 用户id
	Attrs  g.Map  `json:"mapAttr"`
}

type SmsOptInfo struct {
	Ctx         context.Context
	AppType     int `json:"appType" dc:"应用类型（1:android 2:ios，3:h5，4:web，5:其他）"`
	PurposeType int `json:"purposeType" dc:"目标类型 1:注册  2:绑定，3:修改密码，4:其他"`
	Recipient   string
	Opt         string
	Content     string
	RespMsg     string
	Result      bool
}
type UserPanicLogAddInput struct {
	UserId          uint64 `json:"userId"          dc:"用户ID"`
	Type            int    `json:"type"            dc:"客户端类型[1 前台, 2 后台]"`
	Ip              string `json:"ip"              dc:"崩溃ip"`
	DeviceId        string `json:"deviceId"        dc:"崩溃设备号（设备指纹）"`
	DeviceOs        string `json:"deviceOs"        dc:"崩溃设备系统（android,ios,windows,mac,...）"`
	DeviceOsVersion string `json:"deviceOsVersion" dc:"崩溃设备系统版本号"`
	DeviceType      int    `json:"deviceType"      dc:"崩溃设备类型（1:mobile手机，2:desktop台式，3:pad平板，。。。其他）"`
	AppType         int    `json:"appType"         dc:"崩溃应用类型（1:android  2: ios，3:h5，4:web，5:其他）"`
	AppVersion      string `json:"appVersion"      dc:"崩溃应用类型版本号"`
	Detail          string `json:"detail"          dc:"崩溃详情"`
	PanicTime       int64  `json:"panicTime"          dc:"崩溃时间"`
}

type AppSharingInfo struct {
	QRCodeImage string `  json:"qRCodeImage" dc:"二维码图片地址"`
	H5Link      string `  json:"h5Link" dc:"H5链接"`
	AndroidLink string ` json:"androidLink" dc:"Android链接"`
	IOSLink     string `  json:"iOSLink" dc:"IOS链接"`
}

type AppSharingRecord struct {
	QRImage   string   `  json:"qRImage" dc:"二维码图片地址"`
	H5        string   `  json:"h5" dc:"H5链接"`
	Android   string   ` json:"android" dc:"Android链接"`
	IOS       string   `  json:"iOS" dc:"IOS链接"`
	Domains   []string `  json:"domains" dc:"域名域名配置"`
	IsDefault bool     `  json:"isDefault" dc:"1：默认配置 2：否"`
}

type DateBirth struct {
	YearOfBirth  int `v:"between:0,3000" json:"yearOfBirth"  dc:"出生年，值范围[1900,3000] 例如:2022"`
	MonthOfBirth int `v:"between:0,12" json:"monthOfBirth" dc:"出生月，值范围[1,12] 例如:10"`
	DayOfBirth   int `v:"between:0,31" json:"dayOfBirth"   dc:"出生日，值范围[1,31] 例如:6"`
}

type UserAppSettings struct {
	Id      uint   `json:"id"                  dc:"用户id"`
	Account string `json:"account"             dc:"账号"`
	// CountryId           uint   `json:"countryId"           dc:"国家id"`
	// Currency            string `json:"currency"            dc:"币种"`
	Language        string   `json:"language"            dc:"语言 zh-CN:中文, id:Indonesian, en:English, zh-TW:繁体中文"`
	HomeDisplay     int      `json:"homeDisplay"      dc:"首页显示：1体育靠前 2娱乐靠前, 默认值1"`
	ConfigLanguages []string `json:"configLanguages"     dc:"可选的语言配置 [语言 zh-CN:中文, id:Indonesian, en:English, zh-TW:繁体中文]"`
	OddsType        string   `json:"oddsType"            dc:"盘口类型: Indo印尼盘, Euro欧洲盘, HongKong香港盘, Malay马来盘, American美式盘"`
	ConfigOddsTypes []string `json:"configOddsTypes"     dc:"可选的盘口类型配置, 示例 [Indo,Euro,Hongkong,Malay,Euro,American]"`

	HandicapDisplayType int `json:"handicapDisplayType" dc:"盘口显示方式：1纵向显示，2横向显示"`
	IsGoalPushOpen      int `json:"isGoalPushOpen"      dc:"进球推送： 1开, 2关"`
	IsFavoriteMatch     int `json:"isFavoriteMatch"     dc:"我收藏的赛事：1开, 2关"`
	IsBettingMatch      int `json:"isBettingMatch"      dc:"我投注的赛事：1开, 2关"`
	IsScrollMatch       int `json:"isScrollMatch"       dc:"全部滚球：1开, 2关"`

	IsRemindVoiceOpen     int    `json:"isRemindVoiceOpen"   dc:"推送提示-声音：1开, 2关"`
	IsRemindVoiceType     int    `json:"isRemindVoiceType"   dc:"推送提示-声音类型：1欢呼声, 2哨声"`
	ConfigVoiceCheerUrl   string `json:"configVoiceCheerUrl"   dc:"欢呼声S3地址 "`
	ConfigVoiceWhistleUrl string `json:"configVoiceWhistleUrl"   dc:"哨声S3地址"`
	IsRemindShockOpen     int    `json:"isRemindShockOpen"   dc:"推送提示-震动：1开, 2关"`

	OddsChange int `json:"oddsChange"          dc:"投注设置 1:自动接收更好的赔率, 2:自动接收任何赔率,3:不接收任何赔率变动"`

	IsBetVoiceOpen    int `json:"isBetVoiceOpen"      dc:"投注音效设置：1开, 2关"`
	IsBetVoiceConfirm int `json:"isBetVoiceConfirm"   dc:"投注确认中音效：1开, 2关"`
	IsBetVoiceSucc    int `json:"isBetVoiceSucc"      dc:"投注成功音效：1开, 2关"`

	IsRebateAutoAward int `json:"isRebateAutoAward"   dc:"返水自动领取： 1:开 自动领取, 2:关 手动领取"`
	IsVibrationOpen   int `json:"isVibrationOpen"     dc:"振动设置 开关状态：1开, 2关"`

	Nickname           string `json:"nickname"              dc:"昵称"`
	NicknameModifyTime int64  `json:"nicknameModifyTime"   dc:"昵称最近一次修改时间"`

	Avatar string `json:"avatar"                dc:"头像url"`

	YearOfBirth  int    `json:"yearOfBirth"           dc:"出生年"`
	MonthOfBirth int    `json:"monthOfBirth"          dc:"出生月"`
	DayOfBirth   int    `json:"dayOfBirth"            dc:"出生日"`
	Gender       string `json:"gender"                dc:"性别：0未知 1男 2女"`
}

type UserEditProfile struct {
	Id           uint   `json:"id"                  dc:"用户id"`
	Account      string `json:"account"             dc:"账号"`
	Avatar       string `json:"avatar"              dc:"头像url"`
	Nickname     string `json:"nickname"            dc:"昵称"`
	YearOfBirth  int    `json:"yearOfBirth"         dc:"出生年"`
	MonthOfBirth int    `json:"monthOfBirth"        dc:"出生月"`
	DayOfBirth   int    `json:"dayOfBirth"          dc:"出生日"`
	Gender       string `json:"gender"              dc:"性别：0未知 1男 2女"`
}

type AddBankCardInput struct {
	UserId            uint64 `json:"userId"`     // 用户id
	BankId            uint   `json:"bankId"`     // 银行id
	AccountNum        string `json:"accountNum"` // 银行卡卡号
	AccountFirstName  string `json:"accountFirstName" dc:"银行卡姓氏"`
	AccountMiddleName string `json:"accountMiddleName" dc:"银行卡中间名"`
	AccountLastName   string `json:"accountLastName" dc:"银行卡姓名"`
	Currency          string `json:"currency"` // 币种
	OrderBy           int    `json:"orderBy"`
	DeviceId          string `json:"deviceId" dc:"设备号（设备指纹）"`
	SigninIp          string `json:"signinIp" dc:"添加IP"`
	CreateAccount     string `json:"createAccount"`
	CreateType        int    `json:"createType"`
	UpdateAccount     string `json:"updateAccount"`
	UpdateType        int    `json:"updateType"`
}

type AddCoinAddressInput struct {
	UserId        uint64 `json:"userId"`   // 用户id
	Currency      string `json:"currency"` // 币种
	Protocol      string `json:"protocol" dc:"协议"`
	Address       string `json:"address" dc:"钱包地址"`
	Title         string `json:"title" dc:"别名"`
	OrderBy       int    `json:"orderBy" dc:"排序"`
	DeviceId      string `json:"deviceId" dc:"设备号（设备指纹）"`
	SigninIp      string `json:"signinIp" dc:"添加IP"`
	CreateAccount string `json:"createAccount"`
	CreateType    int    `json:"createType"`
	UpdateAccount string `json:"updateAccount"`
	UpdateType    int    `json:"updateType"`
}

type UserListInput struct {
	ListPager
	AgentId         []uint       `json:"agentId" dc:"代理ID"`
	Ids             []uint       `json:"ids" dc:"用户ID"`
	IsBanned        int          `json:"isBanned" dc:"账号封号状态： 1 正常 2 封号"`
	CreateTimeStart int64        `json:"createTimeStart" dc:"注册时间开始"`
	OrderBy         OrderByItems `json:"orderBy" dc:"排序"`
}

type UserListOutput struct {
	ListRes
	List []entity.User
}

type RegisterUserByAgentInput struct {
	Ctx       context.Context
	Page      *ListPager
	AgentId   uint   `json:"agentId" dc:"代理id, 值为0获取所有代理的统计"`
	AgentIds  []uint `json:"agentIds" dc:"多代理id"`
	StartTime int64  `v:"required" json:"startTime" dc:"开始时间"`
	EndTime   int64  `v:"required" json:"endTime" dc:"结束时间"`
	// Order     int          `json:"order" dc:"注册人数顺序：0无序 1正序 2倒序"`
	OrderBy  *OrderByItem `json:"orderBy" dc:"排序值：[RegisterUser]"`
	DataType int          `json:"dataType" dc:"数据类型[0不区分， 1-正常, 2 测试数据]"`
}

type RegisterUserByAgentOutput struct {
	ListRes
	List []*RegisterUserItem `json:"list" dc:"代理新增会员列表"`
	// TotalRegisterUser int              `json:"totalRegisterUser"      dc:"总注册人数"`
}

type RegisterUserTotalInput struct {
	Ctx       context.Context
	AgentId   uint   `json:"agentId" dc:"代理id, 值为0获取所有代理的统计"`
	AgentIds  []uint `json:"agentIds" dc:"多代理id"`
	StartTime int64  `v:"required" json:"startTime" dc:"开始时间"`
	EndTime   int64  `v:"required" json:"endTime" dc:"结束时间"`
	DataType  int    `json:"dataType" dc:"数据类型[0不区分， 1-正常, 2 测试数据]"`
}

type RegisterUserTotalOutput struct {
	TotalRegisterUser int `json:"totalRegisterUser"      dc:"总注册人数"`
}

type RegisterUserItem struct {
	AgentId      int `json:"agentId"                 dc:"代理id"`
	RegisterUser int `json:"registerUser"      dc:"注册人数"`
}

type RegisterUserByDateInput struct {
	Ctx       context.Context
	Page      *ListPager
	AgentId   uint  `json:"agentId" dc:"代理id"`
	StartTime int64 `v:"required" json:"startTime" dc:"开始时间"`
	EndTime   int64 `v:"required" json:"endTime" dc:"结束时间"`
	DataType  int   `json:"dataType" dc:"数据类型[0不区分， 1-正常, 2 测试数据]"`
}

type RegisterUserByDateOutput struct {
	ListRes
	List []*RegisterUserDateItem `json:"list" dc:"代理新增会员列表"`
}
type RegisterUserDateItem struct {
	StatDate     string `json:"statDate"  dc:"汇总日期"`
	RegisterUser int    `json:"registerUser"  dc:"注册人数"`
}

type RegisterUserByMonthInput struct {
	Ctx       context.Context
	Page      *ListPager
	AgentId   uint  `json:"agentId" dc:"代理id"`
	StartTime int64 `v:"required" json:"startTime" dc:"开始时间"`
	EndTime   int64 `v:"required" json:"endTime" dc:"结束时间"`
	DataType  int   `json:"dataType" dc:"数据类型[0不区分， 1-正常, 2 测试数据]"`
}

type RegisterUserByMonthOutput struct {
	ListRes
	List              []*RegisterUserMonthItem `json:"list" dc:"代理新增会员列表"`
	TotalRegisterUser int                      `dc:"总注册人数"`
}
type RegisterUserMonthItem struct {
	StatMonth    string `json:"statMonth"  dc:"汇总月"`
	RegisterUser int    `json:"registerUser"  dc:"注册人数"`
}

type PixelInfoOutput struct {
	PixelId       string `json:"pixelId"   dc:"默认脸书像素ID"`              // 脸书像素ID
	PixelIdGoogle string `json:"pixelIdGoogle"    dc:"谷歌像素ID"`         // 谷歌像素ID
	PixelIdTiktok string `json:"pixelIdTiktok"      dc:"tiktok像素ID"`   // tiktok像素ID
	PixelIdFlyer  string `json:"pixelIdFlyer"        dc:"AppsFlyerID"` // AppsFlyerID
	AccessToken   string `json:"accessToken"       dc:"访问口令"`
}
