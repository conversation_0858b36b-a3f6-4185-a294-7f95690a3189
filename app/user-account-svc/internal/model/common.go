package model

import (
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/shopspring/decimal"
	"unsafe"
)

type Decimal struct {
	decimal.Decimal
}

func (d *Decimal) UnmarshalValue(data interface{}) error {
	s := gconv.String(data)
	if len(s) == 0 {
		s = "0"
	}
	return d.UnmarshalText(unsafe.Slice(unsafe.StringData(s), len(s)))
}

type RequestDomain struct {
	DomainName string `json:"domainName"      dc:"一级域名"`
	FullDomain string `json:"fullDomain"      dc:"全域名"`
	UserAgent  string `json:"userAgent"      dc:"用户代理"`
}

type CommonDelayData struct {
	Id uint `json:"id"`
}
