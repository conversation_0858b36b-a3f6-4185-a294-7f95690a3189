package model

type UserSession struct {
	SessionId  string `json:"sessionId" description:"会话唯一ID（绑定 JWT 的 sid 字段）"` // 会话唯一ID（绑定 JWT 的 sid 字段）
	SecretKey  string `json:"secretKey" description:"refresh token的密钥"`        // refresh token的密钥
	UserId     uint64 `json:"userId" description:"会员ID"`                       // 会员ID
	LoginTime  int64  `json:"loginTime"  description:"登录时间（时间戳）"`              // 登录时间（时间戳）
	ExpireTime int64  `json:"expireTime"    description:"会话过期时间（时间戳）"`         // 会话过期时间（时间戳）
	LogoutTime int64  `json:"logoutTime"  description:"登出时间（0表示未登出）"`          // 登出时间（0表示未登出）
}
