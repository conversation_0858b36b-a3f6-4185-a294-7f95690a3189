package model

import (
	"github.com/gogf/gf/v2/container/gvar"
	"github.com/golang-jwt/jwt/v5"
)

type JWTD struct {
	Ty string `json:"ty"`
	jwt.RegisteredClaims
}

// 邮箱或手机号修改密码
type JWTResetPwd struct {
	ResetType ResetType `json:"reset_type" dc:"1：手机 2 邮箱"`
	AreaCode  string    `json:"areaCode" dc:"区号"`
	Phone     string    `json:"phone" dc:"手机号"`
	Email     string    `json:"email" dc:"邮箱"`
	jwt.RegisteredClaims
}

type ResetType int

// 修改密码方式
const (
	ResetByPhone ResetType = 1
	ResetByMail  ResetType = 2
)

type JWTAccountAuth struct {
	IsPhone bool   `json:"is_phone" dc:"true：手机 false 邮箱"`
	Value   string `json:"value" dc:"手机或邮箱"`
	jwt.RegisteredClaims
}

type SelectOptionRes []SelectOption

type SelectOption struct {
	Label string `json:"label" dc:"显示名称"`
	Value any    `json:"value" dc:"值"`
}

type IsFieldValueExistInput struct {
	TableName  string
	FieldName  string
	Value      *gvar.Var
	DeleteTime string
}

type JWTUserAuth struct {
	AuthAccount string `json:"auth_account" dc:"鉴权账号"`
	AuthWay     int    `json:"auth_way" dc:"鉴权方式 1:手机验证码 2:支付密码"`
	IsUseOnce   bool   `json:"is_use_once" dc:"1 true：仅能使用一次；2 false 可使用多次 超时结束"`
	//CreatTime   int64  `json:"create_time" dc:"创建时间"`
	jwt.RegisteredClaims
}
