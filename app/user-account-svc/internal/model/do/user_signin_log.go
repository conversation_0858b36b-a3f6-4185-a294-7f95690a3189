// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// UserSigninLog is the golang structure of table user_signin_log for DAO operations like Where/Data.
type UserSigninLog struct {
	g.Meta          `orm:"table:user_signin_log, do:true"`
	BigintUnsigned  interface{} //
	UserId          interface{} // 会员id
	VipLevel        interface{} // 会员当时vip等级
	SigninTime      interface{} // 登录时间
	Ip              interface{} // 登录ip（ip6长度为39字符）
	IpRegion        interface{} // ip地址位置
	DeviceId        interface{} // 设备编号
	DeviceOs        interface{} // 设备系统（ios，android，mac，windows，。。。）
	DeviceOsVersion interface{} // 设备系统版本号
	DeviceType      interface{} // 设备类型（mobile手机，desktop台式，pad平板，。。。其他）
	AppType         interface{} // 应用类型（1:android  2: ios，3:h5，4:web，5:其他）
	AppVersion      interface{} // 应用版本号
	Host            interface{} // 登录域名
}
