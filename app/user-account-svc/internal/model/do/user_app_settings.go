// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// UserAppSettings is the golang structure of table user_app_settings for DAO operations like Where/Data.
type UserAppSettings struct {
	g.Meta             `orm:"table:user_app_settings, do:true"`
	Id                 interface{} //
	Account            interface{} // 账号
	CountryId          interface{} // 国家id
	Currency           interface{} // 币种
	Language           interface{} // 语言 : zh-CN:中文, id:Indonesian, en:English
	IsVibrationOpen    interface{} // 振动设置 开关状态：1开, 2关
	YearOfBirth        interface{} // 出生年
	MonthOfBirth       interface{} // 出生月
	DayOfBirth         interface{} // 出生日
	IsRemindVoiceOpen  interface{} // 推送提示-声音：1开, 2关
	IsRemindVoiceType  interface{} // 推送提示-声音类型：1欢呼声,  2哨声
	IsRemindShockOpen  interface{} // 推送提示-震动：1开, 2关
	IsWithdrawAuth     interface{} // 提现验证：1开, 2关
	Avatar             interface{} // 头像url
	Gender             interface{} // 性别：0未知 1男 2女
	Nickname           interface{} // 昵称
	NicknameModityTime interface{} // 昵称最近一次修改时间
	FirstName          interface{} // 第一个名字
	MiddleName         interface{} // 中间名字
	LastName           interface{} // 最后一个名字
	Version            interface{} // 该记录的版本号
	IdentityCard       interface{} // 身份证号码
	IdentityCardImgs   interface{} // 身份证图片
	Contact            interface{} // 社交联系方式 wechat qq等
	Address            interface{} // 住址
	CreateAccount      interface{} // 创建者账号
	CreateType         interface{} // 创建者来源(注册来源)（1会员注册user，2代理添加agent，3管理员添加admin）
	UpdateAccount      interface{} // 更新者账号
	UpdateType         interface{} // 更新者来源
	CreateTime         interface{} // 创建时间
	UpdateTime         interface{} // 更新时间
}
