// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"context"
	"errors"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"halalplus/app/user-account-svc/internal/dao/internal"
	"halalplus/app/user-account-svc/internal/model"
	"halalplus/app/user-account-svc/internal/model/entity"
)

// userDao is the data access object for the table user.
// You can define custom methods on it to extend its functionality as needed.
type userDao struct {
	*internal.UserDao
}

var (
	// User is a globally accessible object for table user operations.
	User = userDao{internal.NewUserDao()}
)

func (d *userDao) GetUserEntityByPhone(ctx context.Context, areaCode string, phoneNum string) (user *entity.User, err error) {
	user = new(entity.User)
	err = d.Ctx(ctx).Where(d.Columns().AreaCode, areaCode).Where(d.Columns().PhoneNum, phoneNum).Scan(&user)
	if err != nil {
		return nil, err
	}
	if g.IsEmpty(user.Id) {
		return nil, errors.New("user is not exist")
	}
	return user, nil
}

func (d *userDao) GetUser(ctx context.Context, uid uint64) (userModel *model.User, err error) {
	var userEntity = new(entity.User)
	err = d.Ctx(ctx).Where(d.Columns().Id, uid).Scan(&userEntity)
	if err != nil {
		g.Log().Error(ctx, "GetUser failed to query user", err)
		return nil, err
	}
	userModel = new(model.User)
	err = gconv.Struct(userEntity, userModel)
	if err != nil {
		g.Log().Error(ctx, "Struct to user model", err)
		return nil, err
	}
	return userModel, err
}
