// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/user-account-svc/internal/dao/internal"
)

// userSessionDao is the data access object for the table user_session.
// You can define custom methods on it to extend its functionality as needed.
type userSessionDao struct {
	*internal.UserSessionDao
}

var (
	// UserSession is a globally accessible object for table user_session operations.
	UserSession = userSessionDao{internal.NewUserSessionDao()}
)

// Add your custom methods and functionality below.
