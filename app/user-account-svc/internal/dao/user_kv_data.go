// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"halalplus/app/user-account-svc/internal/dao/internal"
	"halalplus/app/user-account-svc/internal/model"
	"halalplus/app/user-account-svc/internal/model/do"
	"halalplus/app/user-account-svc/internal/model/entity"
	"strconv"
	"strings"
)

// userKvDataDao is the data access object for the table user_kv_data.
// You can define custom methods on it to extend its functionality as needed.
type userKvDataDao struct {
	*internal.UserKvDataDao
}

var (
	// UserKvData is a globally accessible object for table user_kv_data operations.
	UserKvData = userKvDataDao{internal.NewUserKvDataDao()}
)

// 使用JSON_EXTRACT函数查询JSON类型数据中某个元素的值
// select JSON_EXTRACT(`value_data`, '$.theme') as val from user_kv_data WHERE (`user_id`=10000) AND (`key_path`='profile')

// JSON_SET（）函数不会更新路径不存在的key

// GetByKeyPath 获取用户kv数据
// keyPath是以 dot 分隔的键路径字符串，例如 "profile.theme.color"，分割字符串
func (d *userKvDataDao) GetByKeyPath(ctx context.Context, userId uint64, keyPath string) (*model.UserKvData, error) {
	parts := strings.SplitN(keyPath, ".", 2)
	docKey := strings.TrimSpace(parts[0]) // 文档级 key_path
	// 获取路径部分，例如 profile.theme.color → $.theme.color
	subPath := strings.TrimPrefix(keyPath, docKey)
	sqlStr := fmt.Sprintf("JSON_EXTRACT(%s, '$%s')", d.Columns().ValueData, subPath)

	val, err := d.Ctx(ctx).Where(d.Columns().UserId, userId).
		Where(d.Columns().KeyPath, docKey).Value(gdb.Raw(sqlStr))
	if err != nil {
		return nil, err
	}
	g.Log().Debug(ctx, "##JSON_EXTRACT", val.Val())
	return &model.UserKvData{
		UserId:    userId,
		KeyPath:   keyPath,
		ValueData: val.Val(), // 支持字符串、数组、对象
	}, nil
}

func toMysqlJsonValue(val interface{}) interface{} {
	s, ok := val.(string)
	if ok {
		s = strings.TrimSpace(s)
		if strings.HasPrefix(s, "{") {
			return gjson.New(s)
		}
		if strings.HasPrefix(s, "[") {
			return gjson.New(s).Array()
		}

		// 空或 null → nil
		if s == "" || strings.EqualFold(s, "null") {
			return nil
		}

		// 布尔值
		if s == "true" {
			return true
		}
		if s == "false" {
			return false
		}

		// 数字（整数）
		if i, err := strconv.Atoi(s); err == nil {
			return int32(i)
		}

		return gjson.New(s).String()
	}

	return val
}

func (d *userKvDataDao) UpdateByKeyPath(ctx context.Context, kvModel *model.UserKvData) (err error) {
	parts := strings.SplitN(kvModel.KeyPath, ".", 2)
	docKey := strings.TrimSpace(parts[0]) // 文档级 key_path

	item := do.UserKvData{
		UserId:     kvModel.UserId,
		KeyPath:    docKey,
		ValueData:  "{}",
		CreateTime: gtime.Now().Unix(),
		UpdateTime: gtime.Now().Unix(),
	}

	// 是否是整文档更新
	isFullDoc := len(parts) == 1

	if isFullDoc {
		item.ValueData = gjson.New(kvModel.ValueData).String()
	} else {
		// 局部更新 JSON 字段，先把数据全量读取出来
		originKv, err := d.GetByKeyPath(ctx, kvModel.UserId, docKey)
		if err != nil {
			g.Log().Error(ctx, err)
			return err
		}

		// 合并json
		if g.IsEmpty(originKv.ValueData) {
			originKv.ValueData = "{}"
		}
		originVal := gjson.New(originKv.ValueData)
		originVal.Set(parts[1], toMysqlJsonValue(kvModel.ValueData))
		echo := originVal.Get(parts[1])
		if echo.String() != kvModel.ValueData {
			g.Log().Warning(ctx, "设置json失败", originVal)
		}
		// 合并后的数据
		item.ValueData = originVal.String()
	}

	result, err := d.Ctx(ctx).
		OnDuplicate(g.Map{
			d.Columns().ValueData: gdb.Raw(fmt.Sprintf(
				"VALUES(%s)", d.Columns().ValueData)),
			d.Columns().UpdateTime: gdb.Raw(fmt.Sprintf(
				"VALUES(%s)", d.Columns().UpdateTime)),
		}).
		Save(item)
	if err != nil {
		g.Log().Error(ctx, err)
		return err
	}
	_, err = result.RowsAffected()
	if err != nil {
		g.Log().Error(ctx, err)
		return err
	}
	return nil
}

// getOne 没记录时返回默认空值
func (d *userKvDataDao) mustGetOne(ctx context.Context, userId uint64, docKey string) (*entity.UserKvData, error) {
	// 默认值
	kvEntity := &entity.UserKvData{
		UserId:    userId,
		KeyPath:   docKey,
		ValueData: "{}",
	}
	err := d.Ctx(ctx).
		Where(d.Columns().UserId, userId).
		Where(d.Columns().KeyPath, docKey).
		Scan(&kvEntity)
	// 不存在，不要报错
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		g.Log().Error(ctx, err)
		return nil, err
	}
	return kvEntity, nil
}
