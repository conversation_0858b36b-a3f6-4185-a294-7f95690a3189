// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/user-account-svc/internal/dao/internal"
)

// userSigninLogDao is the data access object for the table user_signin_log.
// You can define custom methods on it to extend its functionality as needed.
type userSigninLogDao struct {
	*internal.UserSigninLogDao
}

var (
	// UserSigninLog is a globally accessible object for table user_signin_log operations.
	UserSigninLog = userSigninLogDao{internal.NewUserSigninLogDao()}
)

// Add your custom methods and functionality below.
