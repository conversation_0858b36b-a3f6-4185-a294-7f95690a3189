// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"halalplus/app/user-account-svc/internal/dao/internal"
	"halalplus/app/user-account-svc/internal/model"
	"halalplus/app/user-account-svc/internal/model/entity"
	"halalplus/utility"
)

// userInfoDao is the data access object for the table user_info.
// You can define custom methods on it to extend its functionality as needed.
type userInfoDao struct {
	*internal.UserInfoDao
}

var (
	// UserInfo is a globally accessible object for table user_info operations.
	UserInfo = userInfoDao{internal.NewUserInfoDao()}
)

// Add your custom methods and functionality below.
func (d *userInfoDao) GetInfo(ctx context.Context, userId uint64) (info *model.UserInfo, err error) {
	info = new(model.UserInfo)
	u := &entity.UserInfo{}
	err = d.Ctx(ctx).Where(d.Columns().UserId, userId).Scan(&u)
	if err != nil {
		g.Log().Debug(ctx, "get user info err: ", err)
		return nil, err
	}
	info.Id = u.Id
	info.Gender = u.Gender
	info.Avatar = u.Avatar
	info.Nickname = u.Nickname
	info.FirstName = u.FirstName
	info.MiddleName = u.MiddleName
	info.LastName = u.LastName
	if !g.IsEmpty(u.BindPhoneTime) {
		info.BindPhone = 1
		info.PhoneNum = utility.Desensitize(u.PhoneNum)
		info.AreaCode = ""
	}
	return info, nil
}
