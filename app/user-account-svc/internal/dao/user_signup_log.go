// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/user-account-svc/internal/dao/internal"
)

// userSignupLogDao is the data access object for the table user_signup_log.
// You can define custom methods on it to extend its functionality as needed.
type userSignupLogDao struct {
	*internal.UserSignupLogDao
}

var (
	// UserSignupLog is a globally accessible object for table user_signup_log operations.
	UserSignupLog = userSignupLogDao{internal.NewUserSignupLogDao()}
)

// Add your custom methods and functionality below.
