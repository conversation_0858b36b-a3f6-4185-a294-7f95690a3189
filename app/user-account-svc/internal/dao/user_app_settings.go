// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/user-account-svc/internal/dao/internal"
)

// userAppSettingsDao is the data access object for the table user_app_settings.
// You can define custom methods on it to extend its functionality as needed.
type userAppSettingsDao struct {
	*internal.UserAppSettingsDao
}

var (
	// UserAppSettings is a globally accessible object for table user_app_settings operations.
	UserAppSettings = userAppSettingsDao{internal.NewUserAppSettingsDao()}
)

// Add your custom methods and functionality below.
