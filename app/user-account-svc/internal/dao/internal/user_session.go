// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UserSessionDao is the data access object for the table user_session.
type UserSessionDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  UserSessionColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// UserSessionColumns defines and stores column names for the table user_session.
type UserSessionColumns struct {
	Id             string // 会话记录ID
	SessionId      string // 会话唯一ID（绑定 JWT 的 sid 字段）
	SecretKey      string // refresh token的密钥
	UserId         string // 会员ID
	LoginTime      string // 登录时间（时间戳）
	ExpireTime     string // 会话过期时间（时间戳）
	LogoutTime     string // 登出时间（0表示未登出）
	Ip             string // 登录IP
	DeviceId       string // 设备ID
	DeviceOs       string // 设备系统
	DeviceType     string // 设备类型（mobile/desktop/pad等）
	AppType        string // 应用类型（android/ios/h5/web等）
	AppVersion     string // 应用版本
	PushToken      string // 推送token，对应 Android（如 FCM）和 iOS（如 APNs）系统
	IsOnline       string // 是否在线（1=在线，0=离线）
	IsKicked       string // 是否被踢下线（1=是，0=否）
	LastActiveTime string // 最近活跃时间（如访问接口时间）
}

// userSessionColumns holds the columns for the table user_session.
var userSessionColumns = UserSessionColumns{
	Id:             "id",
	SessionId:      "session_id",
	SecretKey:      "secret_key",
	UserId:         "user_id",
	LoginTime:      "login_time",
	ExpireTime:     "expire_time",
	LogoutTime:     "logout_time",
	Ip:             "ip",
	DeviceId:       "device_id",
	DeviceOs:       "device_os",
	DeviceType:     "device_type",
	AppType:        "app_type",
	AppVersion:     "app_version",
	PushToken:      "push_token",
	IsOnline:       "is_online",
	IsKicked:       "is_kicked",
	LastActiveTime: "last_active_time",
}

// NewUserSessionDao creates and returns a new DAO object for table data access.
func NewUserSessionDao(handlers ...gdb.ModelHandler) *UserSessionDao {
	return &UserSessionDao{
		group:    "default",
		table:    "user_session",
		columns:  userSessionColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *UserSessionDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *UserSessionDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *UserSessionDao) Columns() UserSessionColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *UserSessionDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *UserSessionDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *UserSessionDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
