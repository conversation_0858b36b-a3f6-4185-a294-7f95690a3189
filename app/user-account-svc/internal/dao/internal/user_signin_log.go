// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UserSigninLogDao is the data access object for the table user_signin_log.
type UserSigninLogDao struct {
	table    string               // table is the underlying table name of the DAO.
	group    string               // group is the database configuration group name of the current DAO.
	columns  UserSigninLogColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler   // handlers for customized model modification.
}

// UserSigninLogColumns defines and stores column names for the table user_signin_log.
type UserSigninLogColumns struct {
	BigintUnsigned  string //
	UserId          string // 会员id
	VipLevel        string // 会员当时vip等级
	SigninTime      string // 登录时间
	Ip              string // 登录ip（ip6长度为39字符）
	IpRegion        string // ip地址位置
	DeviceId        string // 设备编号
	DeviceOs        string // 设备系统（ios，android，mac，windows，。。。）
	DeviceOsVersion string // 设备系统版本号
	DeviceType      string // 设备类型（mobile手机，desktop台式，pad平板，。。。其他）
	AppType         string // 应用类型（1:android  2: ios，3:h5，4:web，5:其他）
	AppVersion      string // 应用版本号
	Host            string // 登录域名
}

// userSigninLogColumns holds the columns for the table user_signin_log.
var userSigninLogColumns = UserSigninLogColumns{
	BigintUnsigned:  "bigint unsigned",
	UserId:          "user_id",
	VipLevel:        "vip_level",
	SigninTime:      "signin_time",
	Ip:              "ip",
	IpRegion:        "ip_region",
	DeviceId:        "device_id",
	DeviceOs:        "device_os",
	DeviceOsVersion: "device_os_version",
	DeviceType:      "device_type",
	AppType:         "app_type",
	AppVersion:      "app_version",
	Host:            "host",
}

// NewUserSigninLogDao creates and returns a new DAO object for table data access.
func NewUserSigninLogDao(handlers ...gdb.ModelHandler) *UserSigninLogDao {
	return &UserSigninLogDao{
		group:    "default",
		table:    "user_signin_log",
		columns:  userSigninLogColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *UserSigninLogDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *UserSigninLogDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *UserSigninLogDao) Columns() UserSigninLogColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *UserSigninLogDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *UserSigninLogDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *UserSigninLogDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
