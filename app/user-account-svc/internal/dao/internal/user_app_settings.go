// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UserAppSettingsDao is the data access object for the table user_app_settings.
type UserAppSettingsDao struct {
	table    string                 // table is the underlying table name of the DAO.
	group    string                 // group is the database configuration group name of the current DAO.
	columns  UserAppSettingsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler     // handlers for customized model modification.
}

// UserAppSettingsColumns defines and stores column names for the table user_app_settings.
type UserAppSettingsColumns struct {
	Id                 string //
	Account            string // 账号
	CountryId          string // 国家id
	Currency           string // 币种
	Language           string // 语言 : zh-CN:中文, id:Indonesian, en:English
	IsVibrationOpen    string // 振动设置 开关状态：1开, 2关
	YearOfBirth        string // 出生年
	MonthOfBirth       string // 出生月
	DayOfBirth         string // 出生日
	IsRemindVoiceOpen  string // 推送提示-声音：1开, 2关
	IsRemindVoiceType  string // 推送提示-声音类型：1欢呼声,  2哨声
	IsRemindShockOpen  string // 推送提示-震动：1开, 2关
	IsWithdrawAuth     string // 提现验证：1开, 2关
	Avatar             string // 头像url
	Gender             string // 性别：0未知 1男 2女
	Nickname           string // 昵称
	NicknameModityTime string // 昵称最近一次修改时间
	FirstName          string // 第一个名字
	MiddleName         string // 中间名字
	LastName           string // 最后一个名字
	Version            string // 该记录的版本号
	IdentityCard       string // 身份证号码
	IdentityCardImgs   string // 身份证图片
	Contact            string // 社交联系方式 wechat qq等
	Address            string // 住址
	CreateAccount      string // 创建者账号
	CreateType         string // 创建者来源(注册来源)（1会员注册user，2代理添加agent，3管理员添加admin）
	UpdateAccount      string // 更新者账号
	UpdateType         string // 更新者来源
	CreateTime         string // 创建时间
	UpdateTime         string // 更新时间
}

// userAppSettingsColumns holds the columns for the table user_app_settings.
var userAppSettingsColumns = UserAppSettingsColumns{
	Id:                 "id",
	Account:            "account",
	CountryId:          "country_id",
	Currency:           "currency",
	Language:           "language",
	IsVibrationOpen:    "is_vibration_open",
	YearOfBirth:        "year_of_birth",
	MonthOfBirth:       "month_of_birth",
	DayOfBirth:         "day_of_birth",
	IsRemindVoiceOpen:  "is_remind_voice_open",
	IsRemindVoiceType:  "is_remind_voice_type",
	IsRemindShockOpen:  "is_remind_shock_open",
	IsWithdrawAuth:     "is_withdraw_auth",
	Avatar:             "avatar",
	Gender:             "gender",
	Nickname:           "nickname",
	NicknameModityTime: "nickname_modity_time",
	FirstName:          "first_name",
	MiddleName:         "middle_name",
	LastName:           "last_name",
	Version:            "version",
	IdentityCard:       "identity_card",
	IdentityCardImgs:   "identity_card_imgs",
	Contact:            "contact",
	Address:            "address",
	CreateAccount:      "create_account",
	CreateType:         "create_type",
	UpdateAccount:      "update_account",
	UpdateType:         "update_type",
	CreateTime:         "create_time",
	UpdateTime:         "update_time",
}

// NewUserAppSettingsDao creates and returns a new DAO object for table data access.
func NewUserAppSettingsDao(handlers ...gdb.ModelHandler) *UserAppSettingsDao {
	return &UserAppSettingsDao{
		group:    "default",
		table:    "user_app_settings",
		columns:  userAppSettingsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *UserAppSettingsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *UserAppSettingsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *UserAppSettingsDao) Columns() UserAppSettingsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *UserAppSettingsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *UserAppSettingsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *UserAppSettingsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
