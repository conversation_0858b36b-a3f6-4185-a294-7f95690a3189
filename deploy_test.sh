#!/bin/sh

# 部署到测试服
# 本地manifest/deploy/supervisor -> 测试服/home/<USER>/supervisor

SHELL_NAME=$0

function echo_supervisor_cfg() {
  NAME=$1
  BASE_NAME="${NAME%-svc}"   # 去掉最后一个 -svc
  cat <<EOF
# =================================================================================
# Code generated and maintained by shell ${SHELL_NAME}. DO NOT EDIT.
# =================================================================================
[program:$NAME]
user=ec2-user
directory=/home/<USER>/supervisor/$BASE_NAME
command=/home/<USER>/supervisor/$BASE_NAME/$NAME
# 设置环境变量，多个用逗号隔开
environment=CONSUL_HTTP_ADDR="127.0.0.1:8500",CONSUL_GF_CONFIG_PATH="config/$NAME.yaml"
startretries=1
autostart=true
killasgroup=true
stopasgroup=true
stdout_logfile=/home/<USER>/supervisor/$BASE_NAME/log/std_out.log
stdout_logfile_maxbytes=200MB
stdout_logfile_backups=10
stderr_logfile=/home/<USER>/supervisor/$BASE_NAME/log/std_err.log
stderr_logfile_maxbytes=200MB
stderr_logfile_backups=10
EOF
}

function echo_upgrade_shell() {
    cat <<'EOF'
#!/bin/sh
# =================================================================================
# Code generated and maintained by tool. DO NOT EDIT.
# =================================================================================

# 用.latest文件覆盖原程序，然后重启微服务
for LATEST in $(find . -type f -name '*.latest'); do
  OLD_SVC=${LATEST%.latest}
  SVC_NAME=$(basename ${OLD_SVC})

  mv "${OLD_SVC}" "${OLD_SVC}.$(date +%Y%m%d%H%M%S).bak"
  mv $LATEST ${OLD_SVC}
  sudo supervisorctl restart $SVC_NAME
done

sudo supervisorctl status

# 更新pb到apisix
rm -rf /home/<USER>/halalplus-docker/apisix-admin/pb
cp -rf /home/<USER>/pb/ /home/<USER>/halalplus-docker/apisix-admin/
cd /home/<USER>/halalplus-docker/apisix-admin
./apisix_ctl.sh upload_proto

## 恢复目录
rm -rf /home/<USER>/halalplus-docker/apisix-admin/pb
git checkout .
git pull
## 更新route
./apisix_ctl.sh set_route
EOF
}
# 部署到测试服

ROOT_DIR=$(pwd)
rm -r $ROOT_DIR/manifest/deploy/supervisor
test -d $ROOT_DIR/manifest/deploy/supervisor/conf.d || mkdir -p $ROOT_DIR/manifest/deploy/supervisor/conf.d

echo_upgrade_shell > $ROOT_DIR/manifest/deploy/supervisor/upgrade.sh

sh gf-gen-pb.sh
for name in `ls app`; do
  SVC_PATH=$ROOT_DIR/app/$name
  echo $SVC_PATH
  DST_DIR=$ROOT_DIR/manifest/deploy/supervisor/${name%-svc}
  cd $SVC_PATH
  test -d $DST_DIR/log || mkdir -p $DST_DIR/log
  GOOS=linux GOARCH=amd64 go build -o $DST_DIR/${name}.latest
  chmod +x $DST_DIR/${name}.latest
  echo_supervisor_cfg $name > $ROOT_DIR/manifest/deploy/supervisor/conf.d/${name%-svc}.conf
done

# ./main --gf.gcfg.file=config.prod.toml
scp -r $ROOT_DIR/manifest/deploy/supervisor halal-dev:/home/<USER>/
scp -r $ROOT_DIR/manifest/deploy/pb halal-dev:/home/<USER>/

ssh halal-dev "cd supervisor && sh upgrade.sh"