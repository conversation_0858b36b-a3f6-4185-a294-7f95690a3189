#!/bin/bash

# 云服务器配置
REMOTE_USER="ec2-user"
REMOTE_HOST="************"
REMOTE_PORT=2542
REMOTE_PATH="/home/<USER>/halalplus/html/admin/apiDoc"
PRIVATE_KEY="~/.ssh/id_rsa"

# 本地文档输出目录
DOCS_DIR="./apiDoc"
mkdir -p "$DOCS_DIR"

# 清理旧文档
rm -f "$DOCS_DIR"/*.html

# 查找所有 proto 文件
PROTO_FILES=$(find ./app -name "*.proto")

# 为每个 proto 文件生成 HTML 文档
for PROTO in $PROTO_FILES; do
  echo "Processing proto file: $PROTO"
  # 如果需要排除特定目录，可以在这里添加条件
  if [[ "$PROTO" == *"pbentity"* ]]; then
    echo "Skipping $PROTO as it is in pbentity directory."
    continue
  fi
  if [[ "$PROTO" == *"common"* ]]; then
      echo "Skipping $PROTO as it is in common directory."
      continue
    fi
  SERVICE_NAME=$(basename "$PROTO" .proto)

  PROTO_ROOT=$(echo "$PROTO" | awk -F'protobuf' '{print $1 "protobuf/"}')

  protoc -I="$PROTO_ROOT" --doc_out="${DOCS_DIR}" --doc_opt=html,"${SERVICE_NAME}-doc.html" "$PROTO"
  #打包$PROTO_ROOT 文件夹下的所有文件到一个zip包 用${SERVICE_NAME}命名
  ZIP_FILE="${DOCS_DIR}/${SERVICE_NAME}-proto.zip"
  echo "Creating zip file: ${ZIP_FILE}"
  zip -r "$ZIP_FILE" "$PROTO_ROOT"/*

done

# 上传到远程服务器（使用指定端口）
echo "Uploading docs to ${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_PATH} via port ${REMOTE_PORT}"
scp -i "$PRIVATE_KEY" -P "$REMOTE_PORT" "$DOCS_DIR"/*.html "${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_PATH}"
scp -i "$PRIVATE_KEY" -P "$REMOTE_PORT" "$DOCS_DIR"/*.zip "${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_PATH}"

echo "✅ Done. Docs uploaded."
