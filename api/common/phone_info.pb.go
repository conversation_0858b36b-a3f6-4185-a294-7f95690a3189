// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: common/phone_info.proto

package common

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 手机号信息
type PhoneInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AreaCode      string                 `protobuf:"bytes,1,opt,name=area_code,json=areaCode,proto3" json:"area_code,omitempty" dc:"国家码（如 '62'）"`     // 国家码（如 "62"）
	PhoneNum      string                 `protobuf:"bytes,2,opt,name=phone_num,json=phoneNum,proto3" json:"phone_num,omitempty" dc:"手机号（8~13位数字字符串）"` // 手机号（8~13位数字字符串）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PhoneInfo) Reset() {
	*x = PhoneInfo{}
	mi := &file_common_phone_info_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PhoneInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PhoneInfo) ProtoMessage() {}

func (x *PhoneInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_phone_info_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PhoneInfo.ProtoReflect.Descriptor instead.
func (*PhoneInfo) Descriptor() ([]byte, []int) {
	return file_common_phone_info_proto_rawDescGZIP(), []int{0}
}

func (x *PhoneInfo) GetAreaCode() string {
	if x != nil {
		return x.AreaCode
	}
	return ""
}

func (x *PhoneInfo) GetPhoneNum() string {
	if x != nil {
		return x.PhoneNum
	}
	return ""
}

var File_common_phone_info_proto protoreflect.FileDescriptor

const file_common_phone_info_proto_rawDesc = "" +
	"\n" +
	"\x17common/phone_info.proto\x12\x06common\"E\n" +
	"\tPhoneInfo\x12\x1b\n" +
	"\tarea_code\x18\x01 \x01(\tR\bareaCode\x12\x1b\n" +
	"\tphone_num\x18\x02 \x01(\tR\bphoneNumB\x1dZ\x1bhalalplus/api/common;commonb\x06proto3"

var (
	file_common_phone_info_proto_rawDescOnce sync.Once
	file_common_phone_info_proto_rawDescData []byte
)

func file_common_phone_info_proto_rawDescGZIP() []byte {
	file_common_phone_info_proto_rawDescOnce.Do(func() {
		file_common_phone_info_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_common_phone_info_proto_rawDesc), len(file_common_phone_info_proto_rawDesc)))
	})
	return file_common_phone_info_proto_rawDescData
}

var file_common_phone_info_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_common_phone_info_proto_goTypes = []any{
	(*PhoneInfo)(nil), // 0: common.PhoneInfo
}
var file_common_phone_info_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_common_phone_info_proto_init() }
func file_common_phone_info_proto_init() {
	if File_common_phone_info_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_common_phone_info_proto_rawDesc), len(file_common_phone_info_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_phone_info_proto_goTypes,
		DependencyIndexes: file_common_phone_info_proto_depIdxs,
		MessageInfos:      file_common_phone_info_proto_msgTypes,
	}.Build()
	File_common_phone_info_proto = out.File
	file_common_phone_info_proto_goTypes = nil
	file_common_phone_info_proto_depIdxs = nil
}
