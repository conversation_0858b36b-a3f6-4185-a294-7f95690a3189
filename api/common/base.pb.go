// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: common/base.proto

package common

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RequestHeader struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RequestId     string                 `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty" dc:"请求唯一ID."` // 请求唯一ID.
	Timestamp     int64                  `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty" dc:"毫秒时间戳"`                   // 毫秒时间戳
	TraceId       string                 `protobuf:"bytes,3,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty" dc:"链路追踪ID"`        // 链路追踪ID
	ClientIp      string                 `protobuf:"bytes,4,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty" dc:"客户端IP"`      // 客户端IP
	Lang          string                 `protobuf:"bytes,5,opt,name=lang,proto3" json:"lang,omitempty" dc:"客户端语言"`                              // 客户端语言
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RequestHeader) Reset() {
	*x = RequestHeader{}
	mi := &file_common_base_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RequestHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestHeader) ProtoMessage() {}

func (x *RequestHeader) ProtoReflect() protoreflect.Message {
	mi := &file_common_base_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestHeader.ProtoReflect.Descriptor instead.
func (*RequestHeader) Descriptor() ([]byte, []int) {
	return file_common_base_proto_rawDescGZIP(), []int{0}
}

func (x *RequestHeader) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *RequestHeader) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *RequestHeader) GetTraceId() string {
	if x != nil {
		return x.TraceId
	}
	return ""
}

func (x *RequestHeader) GetClientIp() string {
	if x != nil {
		return x.ClientIp
	}
	return ""
}

func (x *RequestHeader) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

type PageRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty" dc:"当前页"`  // 当前页
	Size          int32                  `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty" dc:"每页数量"` // 每页数量
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PageRequest) Reset() {
	*x = PageRequest{}
	mi := &file_common_base_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageRequest) ProtoMessage() {}

func (x *PageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_common_base_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageRequest.ProtoReflect.Descriptor instead.
func (*PageRequest) Descriptor() ([]byte, []int) {
	return file_common_base_proto_rawDescGZIP(), []int{1}
}

func (x *PageRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *PageRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

type PageResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty" dc:"当前页"`   // 当前页
	Size          int32                  `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty" dc:"每页数量"`  // 每页数量
	Total         int32                  `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty" dc:"总条数"` // 总条数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PageResponse) Reset() {
	*x = PageResponse{}
	mi := &file_common_base_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageResponse) ProtoMessage() {}

func (x *PageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_base_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageResponse.ProtoReflect.Descriptor instead.
func (*PageResponse) Descriptor() ([]byte, []int) {
	return file_common_base_proto_rawDescGZIP(), []int{2}
}

func (x *PageResponse) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *PageResponse) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *PageResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type Error struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty" dc:"错误编码，扩展gcode"` // 错误编码，扩展gcode
	Reason        string                 `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty" dc:"错误原因"`      // 错误原因
	Detail        string                 `protobuf:"bytes,3,opt,name=detail,proto3" json:"detail,omitempty" dc:"可选详细信息"`    // 可选详细信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Error) Reset() {
	*x = Error{}
	mi := &file_common_base_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Error) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Error) ProtoMessage() {}

func (x *Error) ProtoReflect() protoreflect.Message {
	mi := &file_common_base_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Error.ProtoReflect.Descriptor instead.
func (*Error) Descriptor() ([]byte, []int) {
	return file_common_base_proto_rawDescGZIP(), []int{3}
}

func (x *Error) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *Error) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *Error) GetDetail() string {
	if x != nil {
		return x.Detail
	}
	return ""
}

// CommonResponse 是所有接口统一返回结构
// 包含：
// - code：业务状态码，0 表示成功
// - msg：人类可读的错误或提示信息
// - error: 错误详情
// - data：具体业务数据，根据接口不同可能有不同结构
type CommonResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty" dc:"业务码"`   // 业务码
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty" dc:"错误原因"`     // 错误原因
	Error         *Error                 `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty" dc:"错误详情"` // 错误详情
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CommonResponse) Reset() {
	*x = CommonResponse{}
	mi := &file_common_base_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommonResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonResponse) ProtoMessage() {}

func (x *CommonResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_base_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonResponse.ProtoReflect.Descriptor instead.
func (*CommonResponse) Descriptor() ([]byte, []int) {
	return file_common_base_proto_rawDescGZIP(), []int{4}
}

func (x *CommonResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CommonResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CommonResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_common_base_proto protoreflect.FileDescriptor

const file_common_base_proto_rawDesc = "" +
	"\n" +
	"\x11common/base.proto\x12\x06common\"\x98\x01\n" +
	"\rRequestHeader\x12\x1d\n" +
	"\n" +
	"request_id\x18\x01 \x01(\tR\trequestId\x12\x1c\n" +
	"\ttimestamp\x18\x02 \x01(\x03R\ttimestamp\x12\x19\n" +
	"\btrace_id\x18\x03 \x01(\tR\atraceId\x12\x1b\n" +
	"\tclient_ip\x18\x04 \x01(\tR\bclientIp\x12\x12\n" +
	"\x04lang\x18\x05 \x01(\tR\x04lang\"5\n" +
	"\vPageRequest\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x12\n" +
	"\x04size\x18\x02 \x01(\x05R\x04size\"L\n" +
	"\fPageResponse\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x12\n" +
	"\x04size\x18\x02 \x01(\x05R\x04size\x12\x14\n" +
	"\x05total\x18\x03 \x01(\x05R\x05total\"K\n" +
	"\x05Error\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x16\n" +
	"\x06reason\x18\x02 \x01(\tR\x06reason\x12\x16\n" +
	"\x06detail\x18\x03 \x01(\tR\x06detail\"[\n" +
	"\x0eCommonResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05errorB\x1dZ\x1bhalalplus/api/common;commonb\x06proto3"

var (
	file_common_base_proto_rawDescOnce sync.Once
	file_common_base_proto_rawDescData []byte
)

func file_common_base_proto_rawDescGZIP() []byte {
	file_common_base_proto_rawDescOnce.Do(func() {
		file_common_base_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_common_base_proto_rawDesc), len(file_common_base_proto_rawDesc)))
	})
	return file_common_base_proto_rawDescData
}

var file_common_base_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_common_base_proto_goTypes = []any{
	(*RequestHeader)(nil),  // 0: common.RequestHeader
	(*PageRequest)(nil),    // 1: common.PageRequest
	(*PageResponse)(nil),   // 2: common.PageResponse
	(*Error)(nil),          // 3: common.Error
	(*CommonResponse)(nil), // 4: common.CommonResponse
}
var file_common_base_proto_depIdxs = []int32{
	3, // 0: common.CommonResponse.error:type_name -> common.Error
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_common_base_proto_init() }
func file_common_base_proto_init() {
	if File_common_base_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_common_base_proto_rawDesc), len(file_common_base_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_base_proto_goTypes,
		DependencyIndexes: file_common_base_proto_depIdxs,
		MessageInfos:      file_common_base_proto_msgTypes,
	}.Build()
	File_common_base_proto = out.File
	file_common_base_proto_goTypes = nil
	file_common_base_proto_depIdxs = nil
}
