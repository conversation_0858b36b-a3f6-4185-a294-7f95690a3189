// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: common/front_info.proto

package common

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 前端信息采集
type FrontInfo struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	DeviceId        string                 `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty" dc:"设备号（设备指纹）"`                                           // 设备号（设备指纹）
	DeviceOs        string                 `protobuf:"bytes,2,opt,name=device_os,json=deviceOs,proto3" json:"device_os,omitempty" dc:"设备操作系统（android, ios, windows, mac, ...）"`             // 设备操作系统（android, ios, windows, mac, ...）
	DeviceOsVersion string                 `protobuf:"bytes,3,opt,name=device_os_version,json=deviceOsVersion,proto3" json:"device_os_version,omitempty" dc:"设备操作系统版本号"`                    // 设备操作系统版本号
	DeviceType      int32                  `protobuf:"varint,4,opt,name=device_type,json=deviceType,proto3" json:"device_type,omitempty" dc:"设备类型（1:mobile手机, 2:desktop台式, 3:pad平板, 4:其他）"` // 设备类型（1:mobile手机, 2:desktop台式, 3:pad平板, 4:其他）
	AppType         int32                  `protobuf:"varint,5,opt,name=app_type,json=appType,proto3" json:"app_type,omitempty" dc:"应用类型（1:android, 2:ios, 3:h5, 4:web, 5:其他）"`             // 应用类型（1:android, 2:ios, 3:h5, 4:web, 5:其他）
	AppVersion      string                 `protobuf:"bytes,6,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty" dc:"应用版本号"`                                         // 应用版本号
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *FrontInfo) Reset() {
	*x = FrontInfo{}
	mi := &file_common_front_info_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FrontInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FrontInfo) ProtoMessage() {}

func (x *FrontInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_front_info_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FrontInfo.ProtoReflect.Descriptor instead.
func (*FrontInfo) Descriptor() ([]byte, []int) {
	return file_common_front_info_proto_rawDescGZIP(), []int{0}
}

func (x *FrontInfo) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *FrontInfo) GetDeviceOs() string {
	if x != nil {
		return x.DeviceOs
	}
	return ""
}

func (x *FrontInfo) GetDeviceOsVersion() string {
	if x != nil {
		return x.DeviceOsVersion
	}
	return ""
}

func (x *FrontInfo) GetDeviceType() int32 {
	if x != nil {
		return x.DeviceType
	}
	return 0
}

func (x *FrontInfo) GetAppType() int32 {
	if x != nil {
		return x.AppType
	}
	return 0
}

func (x *FrontInfo) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

var File_common_front_info_proto protoreflect.FileDescriptor

const file_common_front_info_proto_rawDesc = "" +
	"\n" +
	"\x17common/front_info.proto\x12\x06common\"\xce\x01\n" +
	"\tFrontInfo\x12\x1b\n" +
	"\tdevice_id\x18\x01 \x01(\tR\bdeviceId\x12\x1b\n" +
	"\tdevice_os\x18\x02 \x01(\tR\bdeviceOs\x12*\n" +
	"\x11device_os_version\x18\x03 \x01(\tR\x0fdeviceOsVersion\x12\x1f\n" +
	"\vdevice_type\x18\x04 \x01(\x05R\n" +
	"deviceType\x12\x19\n" +
	"\bapp_type\x18\x05 \x01(\x05R\aappType\x12\x1f\n" +
	"\vapp_version\x18\x06 \x01(\tR\n" +
	"appVersionB\x1dZ\x1bhalalplus/api/common;commonb\x06proto3"

var (
	file_common_front_info_proto_rawDescOnce sync.Once
	file_common_front_info_proto_rawDescData []byte
)

func file_common_front_info_proto_rawDescGZIP() []byte {
	file_common_front_info_proto_rawDescOnce.Do(func() {
		file_common_front_info_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_common_front_info_proto_rawDesc), len(file_common_front_info_proto_rawDesc)))
	})
	return file_common_front_info_proto_rawDescData
}

var file_common_front_info_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_common_front_info_proto_goTypes = []any{
	(*FrontInfo)(nil), // 0: common.FrontInfo
}
var file_common_front_info_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_common_front_info_proto_init() }
func file_common_front_info_proto_init() {
	if File_common_front_info_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_common_front_info_proto_rawDesc), len(file_common_front_info_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_front_info_proto_goTypes,
		DependencyIndexes: file_common_front_info_proto_depIdxs,
		MessageInfos:      file_common_front_info_proto_msgTypes,
	}.Build()
	File_common_front_info_proto = out.File
	file_common_front_info_proto_goTypes = nil
	file_common_front_info_proto_depIdxs = nil
}
