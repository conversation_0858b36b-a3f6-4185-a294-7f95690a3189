#!/bin/sh

# 安装 Dart 插件（用于生成 Dart 代码）
# 安装dart sdk
# dart pub global activate protoc_plugin
export PATH="$PATH":"$HOME/.pub-cache/bin":"$HOME/workspace4nu/flutter/bin"

# 全部微服务的pb
# 1. 用根目录覆盖所有微服务的`manifest/protobuf/common`
# 2. 在每个微服务下执行`gf gen pb`

ROOT_DIR=$(pwd)

function make_imported_pb() {
  SVC_PATH=$1
  NAME=$2
  DST_PATH=$3
  test -d $DST_PATH || mkdir -p $DST_PATH
    # 查找所有 proto 文件
    PROTO_FILES=$(find $SVC_PATH/manifest/protobuf -name "*.proto")

    # 为每个 proto 文件生成 pb 文件导入apisix
    for PROTO in $PROTO_FILES; do
#      echo "Processing proto file: $PROTO"
      # 去掉 manifest/protobuf前的字符串
      DST_FILE=${PROTO#*manifest/protobuf/}
      # 替换后缀 .proto → .pb
      DST_FILE=${NAME}/${DST_FILE%.proto}.pb
      # 把 / 换成 __
      DST_FILE=${DST_FILE//\//__}

      protoc --include_imports \
        --proto_path=$SVC_PATH/manifest/protobuf  \
        --descriptor_set_out=$DST_PATH/$DST_FILE \
        $PROTO

      SERVICE=$(grpcurl -protoset $DST_PATH/$DST_FILE list)
      if [[ $SERVICE == \(N* ]]; then
        # 删掉无服务的pb文件
        rm $DST_PATH/$DST_FILE
      else
        PBID="${NAME%-svc}.${SERVICE}.pb"
        mv $DST_PATH/$DST_FILE $DST_PATH/$PBID
      fi
    done
}


function copy_proto() {
  SVC_PATH=$1
  NAME=$2
  DST_PATH=$3
  test -d $DST_PATH || mkdir -p $DST_PATH
    # 查找所有 proto 文件
    PROTO_FILES=$(find $SVC_PATH/manifest/protobuf -name "*.proto")

    # 为每个 proto 文件生成 pb 文件导入apisix
    for PROTO in $PROTO_FILES; do
      # 去掉 manifest/protobuf前的字符串
      DST_FILE=${PROTO#*manifest/protobuf/}
      DST_FILE_PATH=$DST_PATH/${NAME%-svc}/$DST_FILE
      mkdir -p $(dirname ${DST_FILE_PATH})
      cp $PROTO $DST_FILE_PATH
#      echo "copy to $DST_FILE_PATH"
    done
}

function gen_flutter_lib() {
  SVC_PATH=$1
  NAME=$2
  FLUTTER_PROTO_LIB_DIR=$3
  DST_PATH=$3/lib/${NAME}
  test -d $DST_PATH || mkdir -p $DST_PATH
    # 查找所有 proto 文件
    PROTO_FILES=$(find $SVC_PATH/manifest/protobuf -name "*.proto")

    for PROTO in $PROTO_FILES; do
      protoc --proto_path=$SVC_PATH/manifest/protobuf  \
        --dart_out=$DST_PATH \
        $PROTO
    done

    cd $FLUTTER_PROTO_LIB_DIR && find lib/${NAME} -name "*.pb.dart" | sort | sed 's|lib/|export '\''|g' | sed "s|\$|';|g" > lib/${NAME}.dart

}

gf gen pb
for name in `ls app`; do
  # 微服务目录
  SVC_PATH=$ROOT_DIR/app/$name
#  echo $SVC_PATH
  # 用根目录覆盖所有微服务的manifest/protobuf/common
  cp -rf $ROOT_DIR/manifest/protobuf/common $SVC_PATH/manifest/protobuf/
  touch $SVC_PATH/manifest/protobuf/common/DO_NOT_EDIT_COPY_FROM_ROOT
  cd $SVC_PATH && gf gen pb

  make_imported_pb $SVC_PATH $name $ROOT_DIR/manifest/deploy/pb
  gen_flutter_lib $SVC_PATH ${name%-svc} $ROOT_DIR/flutter_proto_lib
  copy_proto $SVC_PATH $name $ROOT_DIR/manifest/deploy/protobuf

done
